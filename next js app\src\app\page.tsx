'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import LoadingScreen from '@/components/LoadingScreen';
import ToastContainer, { useToast } from '@/components/Toast';
import { Product, SelectedProduct, CustomerInfo, Company } from '@/types';
import { storage } from '@/lib/auth-utils';
import { api } from '@/lib/api';
import { useApiPagination, useApi } from '@/hooks/useApi';

const CURRENCY_SYMBOL = 'R';
const TAX_RATE = 0.15;
const DEBOUNCE_DELAY = 300;
const PRODUCTS_PER_PAGE = 20;

const companies: Record<string, Company> = {
  company1: {
    key: 'company1',
    name: "Shans Accessories PTY LTD",
    bankingInformation: `
      First National Bank<br>
      Account :  ***********<br>
      Branch code 257705<br>
      Swift code FIRNZAJJ
    `
  },
  company2: {
    key: 'company2',
    name: "Shans Autosport PTY LTD",
    bankingInformation: `
      Business Account<br>
      Capitec Current Account<br>
      Account: **********
    `
  },
  company3: {
    key: 'company3',
    name: "Shans Motorstyle PTY LTD",
    bankingInformation: `
      SHANS MOTORSTYLE (PTY) LTD<br>
      Gold Business Account<br>
      Account Number: ***********<br>
      Branch Code: 250655<br>
      Swift Code: FIRNZAJJ
    `
  }
};

export default function HomePage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const { messages, showToast, removeToast } = useToast();

  // State management
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [newProductCounter, setNewProductCounter] = useState(1);

  // Form state
  const [selectedCompany, setSelectedCompany] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [customerAddress, setCustomerAddress] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [salespersonName, setSalespersonName] = useState('');
  const [sameAsBilling, setSameAsBilling] = useState(true);
  const [shippingName, setShippingName] = useState('');
  const [shippingEmail, setShippingEmail] = useState('');
  const [shippingAddress, setShippingAddress] = useState('');
  const [shippingPhone, setShippingPhone] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState(0);
  const [room, setRoom] = useState('');
  const [includeTax, setIncludeTax] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState('Cash');
  const [comments, setComments] = useState('');

  // Product search state
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [showProductList, setShowProductList] = useState(false);
  const [selectedProductData, setSelectedProductData] = useState<Product | null>(null);
  const [isSelectingProduct, setIsSelectingProduct] = useState(false);

  // Refs
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Debounced search function
  const debouncedSearch = useCallback(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      displayFilteredProducts();
    }, DEBOUNCE_DELAY);
  }, [productSearch, allProducts]);

  // Fetch products from API
  const fetchAllProducts = async () => {
    try {
      setIsLoading(true);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const cacheBuster = `&_t=${Date.now()}`;
      const response = await fetch(`${API_BASE_URL}/products?page=${currentPage}&limit=${PRODUCTS_PER_PAGE}${cacheBuster}`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const newProducts = await response.json();

      if (newProducts.length < PRODUCTS_PER_PAGE) {
        setHasMoreProducts(false);
      }

      // Filter out duplicates
      const existingItemCodes = new Set(allProducts.map(p => p.item_code));
      const uniqueNewProducts = newProducts.filter((product: Product) => !existingItemCodes.has(product.item_code));

      setAllProducts(prev => [...prev, ...uniqueNewProducts]);
      setCurrentPage(prev => prev + 1);

    } catch (error) {
      console.error('Error fetching products:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        setLoadingError('Request timed out. Please check your connection.');
      } else {
        setLoadingError('Failed to load products. Please try again.');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Display filtered products
  const displayFilteredProducts = () => {
    const searchTerm = productSearch.toLowerCase().trim();
    
    if (searchTerm.length === 0) {
      setFilteredProducts([]);
      setShowProductList(false);
      return;
    }

    const filtered = allProducts.filter(product =>
      product.item_name && product.item_name.toLowerCase().includes(searchTerm)
    );

    setFilteredProducts(filtered);
    setShowProductList(true);
  };

  // Initialize app
  const initializeApp = async () => {
    try {
      setIsLoading(true);
      setLoadingError('');
      
      await fetchAllProducts();
      
      // Load saved company selection
      const savedCompany = storage.get('selectedCompany');
      if (savedCompany) {
        try {
          const company = JSON.parse(savedCompany);
          setSelectedCompany(company.key);
        } catch (e) {
          console.warn('Error parsing saved company:', e);
        }
      }

      // Load existing order data
      loadExistingOrderData();
      
    } catch (error) {
      console.error('Initialization error:', error);
      setLoadingError('Failed to initialize the application. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load existing order data from storage
  const loadExistingOrderData = () => {
    // Implementation for loading saved order data
    // This would restore form fields from localStorage if needed
  };

  // Retry initialization
  const retryInitialization = () => {
    setLoadingError('');
    setAllProducts([]);
    setCurrentPage(1);
    setHasMoreProducts(true);
    initializeApp();
  };

  // Add product to cart
  const addProduct = () => {
    if (!productSearch.trim()) {
      showToast('Please enter a product name', 'error');
      return;
    }

    if (price <= 0) {
      showToast('Please enter a valid price', 'error');
      return;
    }

    if (quantity <= 0) {
      showToast('Please enter a valid quantity', 'error');
      return;
    }

    const newProduct: SelectedProduct = {
      item_code: selectedProductData?.item_code || `NEW_${newProductCounter}`,
      name: productSearch.trim(),
      room_name: room.trim() || 'N/A',
      quantity: quantity,
      price: price,
      tax_per_product: includeTax ? price * TAX_RATE : 0,
      is_new: !selectedProductData
    };

    setSelectedProducts(prev => [...prev, newProduct]);

    // Reset form
    setProductSearch('');
    setQuantity(1);
    setPrice(0);
    setRoom('');
    setSelectedProductData(null);
    setShowProductList(false);

    if (!selectedProductData) {
      setNewProductCounter(prev => prev + 1);
    }

    showToast('Product added to cart', 'success');
  };

  // Effects
  useEffect(() => {
    initializeApp();
  }, []);

  useEffect(() => {
    debouncedSearch();
  }, [productSearch, debouncedSearch]);

  // Calculate totals
  const calculateTotals = () => {
    let subtotal = 0;
    let tax = 0;
    let total = 0;

    selectedProducts.forEach(product => {
      if (includeTax) {
        subtotal += (product.price * (1 - TAX_RATE)) * product.quantity;
        tax += (product.price * TAX_RATE) * product.quantity;
        total += (product.price * product.quantity);
      } else {
        subtotal += (product.price * product.quantity);
        total += (product.price * product.quantity);
      }
    });

    if (!includeTax) {
      tax = 0;
    }

    return {
      subtotal: Math.round(subtotal),
      tax: Math.round(tax),
      total: Math.round(total)
    };
  };

  const totals = calculateTotals();

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100">
        <LoadingScreen 
          isVisible={isLoading} 
          error={loadingError}
          onRetry={retryInitialization}
        />
        
        <ToastContainer messages={messages} onRemove={removeToast} />
        
        <div className="max-w-4xl mx-auto bg-white p-5 shadow-lg">
          {/* Header */}
          <div className="flex justify-between items-center mb-5 flex-wrap gap-4">
            <h1 className="text-gray-800 text-2xl font-normal m-0">Receipt and Quotation Generator</h1>
            <div className="flex items-center gap-2 flex-wrap">
              <a href="/convert-to-receipt" className="bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors">
                Convert to Receipt
              </a>
              {user?.is_admin && (
                <a href="/admin" className="bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors">
                  Admin Dashboard
                </a>
              )}
              <button 
                onClick={logout}
                className="bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>

          {/* Company Selection */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Select Company</h2>
            <label htmlFor="companySelect" className="block mb-1">Choose a company:</label>
            <select 
              id="companySelect"
              value={selectedCompany}
              onChange={(e) => setSelectedCompany(e.target.value)}
              required
              className="w-full p-2 border border-gray-300 rounded box-border"
            >
              <option value="" disabled>Select a company</option>
              <option value="company1">Shans Accessories PTY LTD</option>
              <option value="company2">Shans Autosport PTY LTD</option>
              <option value="company3">Shans Motorstyle PTY LTD</option>
            </select>
          </div>

          {/* Customer Information */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Customer Information</h2>
            
            <label htmlFor="customerName" className="block mb-1">Name:</label>
            <input 
              type="text" 
              id="customerName"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
            />

            <label htmlFor="customerEmail" className="block mb-1">Email:</label>
            <input 
              type="email" 
              id="customerEmail"
              value={customerEmail}
              onChange={(e) => setCustomerEmail(e.target.value)}
              className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
            />

            <label htmlFor="customerAddress" className="block mb-1">Address:</label>
            <input 
              type="text" 
              id="customerAddress"
              value={customerAddress}
              onChange={(e) => setCustomerAddress(e.target.value)}
              className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
            />

            <label htmlFor="customerPhone" className="block mb-1">Phone:</label>
            <input 
              type="tel" 
              id="customerPhone"
              value={customerPhone}
              onChange={(e) => setCustomerPhone(e.target.value)}
              className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
            />

            <label htmlFor="salespersonName" className="block mb-1">Salesperson Name:</label>
            <input 
              type="text" 
              id="salespersonName"
              value={salespersonName}
              onChange={(e) => setSalespersonName(e.target.value)}
              placeholder="Enter your name..."
              className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
            />
          </div>

          {/* Shipping Information */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Shipping Information</h2>

            <div className="mb-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={sameAsBilling}
                  onChange={(e) => setSameAsBilling(e.target.checked)}
                  className="mr-2"
                />
                Same as billing address
              </label>
            </div>

            {!sameAsBilling && (
              <>
                <label htmlFor="shippingName" className="block mb-1">Name:</label>
                <input
                  type="text"
                  id="shippingName"
                  value={shippingName}
                  onChange={(e) => setShippingName(e.target.value)}
                  className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
                />

                <label htmlFor="shippingEmail" className="block mb-1">Email:</label>
                <input
                  type="email"
                  id="shippingEmail"
                  value={shippingEmail}
                  onChange={(e) => setShippingEmail(e.target.value)}
                  className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
                />

                <label htmlFor="shippingAddress" className="block mb-1">Address:</label>
                <input
                  type="text"
                  id="shippingAddress"
                  value={shippingAddress}
                  onChange={(e) => setShippingAddress(e.target.value)}
                  className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
                />

                <label htmlFor="shippingPhone" className="block mb-1">Phone:</label>
                <input
                  type="tel"
                  id="shippingPhone"
                  value={shippingPhone}
                  onChange={(e) => setShippingPhone(e.target.value)}
                  className="w-full p-2 mb-2 border border-gray-300 rounded box-border"
                />
              </>
            )}
          </div>

          {/* Product Search and Selection */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Add Products</h2>

            <div className="relative mb-3">
              <label htmlFor="productSearch" className="block mb-1">Search Products:</label>
              <input
                type="text"
                id="productSearch"
                value={productSearch}
                onChange={(e) => setProductSearch(e.target.value)}
                placeholder="Type to search products..."
                className="w-full p-2 border border-gray-300 rounded box-border"
              />

              {showProductList && filteredProducts.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg">
                  {filteredProducts.map((product) => (
                    <div
                      key={product.item_code}
                      className="p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => {
                        setSelectedProductData(product);
                        setProductSearch(product.item_name);
                        setPrice(product.unit_retail_price);
                        setRoom(product.room_name);
                        setShowProductList(false);
                      }}
                    >
                      <div className="font-medium text-gray-800">{product.item_name}</div>
                      <div className="text-sm text-gray-600">
                        Code: {product.item_code} | Price: {CURRENCY_SYMBOL}{product.unit_retail_price} |
                        Stock: {product.available_stock} | Room: {product.room_name}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              <div>
                <label htmlFor="quantity" className="block mb-1">Quantity:</label>
                <input
                  type="number"
                  id="quantity"
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  min="1"
                  className="w-full p-2 border border-gray-300 rounded box-border"
                />
              </div>

              <div>
                <label htmlFor="price" className="block mb-1">Price ({CURRENCY_SYMBOL}):</label>
                <input
                  type="number"
                  id="price"
                  value={price}
                  onChange={(e) => setPrice(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                  className="w-full p-2 border border-gray-300 rounded box-border"
                />
              </div>

              <div>
                <label htmlFor="room" className="block mb-1">Room:</label>
                <input
                  type="text"
                  id="room"
                  value={room}
                  onChange={(e) => setRoom(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded box-border"
                />
              </div>
            </div>

            <button
              onClick={addProduct}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Add Product
            </button>
          </div>

          {/* Selected Products */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Selected Products</h2>

            {selectedProducts.length === 0 ? (
              <p className="text-gray-600">No products selected yet.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-300 p-2 text-left">Product</th>
                      <th className="border border-gray-300 p-2 text-left">Room</th>
                      <th className="border border-gray-300 p-2 text-left">Qty</th>
                      <th className="border border-gray-300 p-2 text-left">Price</th>
                      <th className="border border-gray-300 p-2 text-left">Total</th>
                      <th className="border border-gray-300 p-2 text-left">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedProducts.map((product, index) => (
                      <tr key={index}>
                        <td className="border border-gray-300 p-2">{product.name}</td>
                        <td className="border border-gray-300 p-2">{product.room_name}</td>
                        <td className="border border-gray-300 p-2">{product.quantity}</td>
                        <td className="border border-gray-300 p-2">{CURRENCY_SYMBOL}{product.price}</td>
                        <td className="border border-gray-300 p-2">{CURRENCY_SYMBOL}{product.price * product.quantity}</td>
                        <td className="border border-gray-300 p-2">
                          <button
                            onClick={() => {
                              // Remove product logic
                              setSelectedProducts(prev => prev.filter((_, i) => i !== index));
                              showToast('Product removed', 'info');
                            }}
                            className="bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="mb-5 border border-gray-300 p-4 rounded">
            <h2 className="text-gray-800 text-xl mb-3">Order Summary</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="paymentMethod" className="block mb-1">Payment Method:</label>
                <select
                  id="paymentMethod"
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded box-border"
                >
                  <option value="Cash">Cash</option>
                  <option value="Card">Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={includeTax}
                    onChange={(e) => setIncludeTax(e.target.checked)}
                    className="mr-2"
                  />
                  Include Tax (15%)
                </label>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="comments" className="block mb-1">Comments:</label>
              <textarea
                id="comments"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded box-border resize-vertical"
                placeholder="Additional comments or notes..."
              />
            </div>

            <div className="mt-4 bg-gray-100 p-3 rounded">
              <div className="flex justify-between mb-2">
                <span>Subtotal:</span>
                <span>{CURRENCY_SYMBOL}{totals.subtotal}</span>
              </div>
              {includeTax && (
                <div className="flex justify-between mb-2">
                  <span>Tax (15%):</span>
                  <span>{CURRENCY_SYMBOL}{totals.tax}</span>
                </div>
              )}
              <div className="flex justify-between font-bold text-lg border-t border-gray-300 pt-2">
                <span>Total:</span>
                <span>{CURRENCY_SYMBOL}{totals.total}</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-center">
            <button
              onClick={() => showToast('Generate Receipt functionality coming soon', 'info')}
              className="bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors"
            >
              Generate Receipt
            </button>
            <button
              onClick={() => showToast('Generate Quotation functionality coming soon', 'info')}
              className="bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors"
            >
              Generate Quotation
            </button>
            <button
              onClick={() => showToast('Generate Invoice functionality coming soon', 'info')}
              className="bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors"
            >
              Generate Invoice
            </button>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
