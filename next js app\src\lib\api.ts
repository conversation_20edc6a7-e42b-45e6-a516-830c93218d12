import { API_BASE_URL } from './auth-utils';

// Types for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface Product {
  item_code: string;
  name: string;
  price: number;
  room_name: string;
  image_url?: string;
}

export interface QuotationData {
  reference_number: string;
  company: {
    name: string;
    bankingInformation: string;
  };
  billing: {
    name: string;
    email: string;
    address: string;
    phone: string;
  };
  shipping?: {
    name: string;
    email: string;
    address: string;
    phone: string;
  };
  items: Array<{
    item_code: string;
    item_name: string;
    room_name: string;
    quantity: number;
    unit_price_including_tax: number;
    unit_price_excluding_tax: number;
    total_price: number;
    tax_per_product: number;
  }>;
  subtotal: number;
  tax: number;
  total: number;
  payment_method: string;
  comments: string;
  salesperson_name: string;
  include_tax: boolean;
}

export interface InvoiceData extends QuotationData {}
export interface ReceiptData extends QuotationData {}

export interface QuotationResponse {
  quotation_id: number;
  reference_number: string;
  date: string;
  billing_name: string;
  billing_email: string;
  payment_method: string;
  salesperson_name: string;
  subtotal: number;
  tax: number;
  total: number;
  items: Array<{
    item_name: string;
    quantity: number;
    unit_price_excluding_tax: number;
    total_price: number;
  }>;
}

export interface InvoiceResponse extends Omit<QuotationResponse, 'quotation_id'> {
  invoice_id: number;
}

export interface ReceiptResponse extends Omit<QuotationResponse, 'quotation_id'> {
  receipt_id: number;
}

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `HTTP error! status: ${response.status}`,
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

// Product API functions
export const productApi = {
  search: async (query: string, page: number = 1, limit: number = 20): Promise<ApiResponse<Product[]>> => {
    return apiRequest<Product[]>(`/products/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  },

  getAll: async (page: number = 1, limit: number = 50): Promise<ApiResponse<Product[]>> => {
    return apiRequest<Product[]>(`/products?page=${page}&limit=${limit}`);
  },

  getByCode: async (itemCode: string): Promise<ApiResponse<Product>> => {
    return apiRequest<Product>(`/products/${encodeURIComponent(itemCode)}`);
  },
};

// Quotation API functions
export const quotationApi = {
  create: async (data: QuotationData): Promise<ApiResponse<QuotationResponse>> => {
    return apiRequest<QuotationResponse>('/quotations', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  getAll: async (): Promise<ApiResponse<QuotationResponse[]>> => {
    return apiRequest<QuotationResponse[]>('/quotations');
  },

  getById: async (id: number): Promise<ApiResponse<QuotationResponse>> => {
    return apiRequest<QuotationResponse>(`/quotations/${id}`);
  },

  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/quotations/${id}`, {
      method: 'DELETE',
    });
  },

  convertToReceipt: async (id: number): Promise<ApiResponse<ReceiptResponse>> => {
    return apiRequest<ReceiptResponse>(`/convert-quotation-to-receipt/${id}`, {
      method: 'POST',
    });
  },
};

// Invoice API functions
export const invoiceApi = {
  create: async (data: InvoiceData): Promise<ApiResponse<InvoiceResponse>> => {
    return apiRequest<InvoiceResponse>('/invoices', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  getAll: async (): Promise<ApiResponse<InvoiceResponse[]>> => {
    return apiRequest<InvoiceResponse[]>('/invoices');
  },

  getById: async (id: number): Promise<ApiResponse<InvoiceResponse>> => {
    return apiRequest<InvoiceResponse>(`/invoices/${id}`);
  },

  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/invoices/${id}`, {
      method: 'DELETE',
    });
  },

  convertToReceipt: async (id: number): Promise<ApiResponse<ReceiptResponse>> => {
    return apiRequest<ReceiptResponse>(`/convert-invoice-to-receipt/${id}`, {
      method: 'POST',
    });
  },
};

// Receipt API functions
export const receiptApi = {
  create: async (data: ReceiptData): Promise<ApiResponse<ReceiptResponse>> => {
    return apiRequest<ReceiptResponse>('/receipts', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  getAll: async (): Promise<ApiResponse<ReceiptResponse[]>> => {
    return apiRequest<ReceiptResponse[]>('/receipts');
  },

  getById: async (id: number): Promise<ApiResponse<ReceiptResponse>> => {
    return apiRequest<ReceiptResponse>(`/receipts/${id}`);
  },

  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/receipts/${id}`, {
      method: 'DELETE',
    });
  },
};

// Authentication API functions
export const authApi = {
  login: async (username: string, password: string): Promise<ApiResponse<{ token: string; user: any }>> => {
    return apiRequest<{ token: string; user: any }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  },

  checkStatus: async (): Promise<ApiResponse<{ status: string }>> => {
    return apiRequest<{ status: string }>('/auth/status');
  },

  verifyToken: async (token: string): Promise<ApiResponse<{ user: any }>> => {
    return apiRequest<{ user: any }>('/auth/verify', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },
};

// Server status API
export const serverApi = {
  checkStatus: async (): Promise<ApiResponse<{ status: string; message: string }>> => {
    return apiRequest<{ status: string; message: string }>('/status');
  },
};

// Export all APIs as a single object for convenience
export const api = {
  products: productApi,
  quotations: quotationApi,
  invoices: invoiceApi,
  receipts: receiptApi,
  auth: authApi,
  server: serverApi,
};

export default api;
