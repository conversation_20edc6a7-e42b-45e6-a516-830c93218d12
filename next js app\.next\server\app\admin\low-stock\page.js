(()=>{var e={};e.id=276,e.ids=[276],e.modules={769:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(687),a=r(3210),o=r(6189),n=r(3213);function i({children:e,requireAdmin:t=!1,redirectTo:r="/login"}){let{user:i,isLoading:l,isAuthenticated:d}=(0,n.A)();(0,o.useRouter)();let[c,u]=(0,a.useState)(!1);return l?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):c?(0,s.jsx)(s.Fragment,{children:e}):null}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(687);function a({isVisible:e,message:t="Loading products, please wait...",error:r,onRetry:a}){return e?(0,s.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,s.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:r?"Connection Error":t}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:r}),a&&(0,s.jsx)("button",{onClick:a,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>n,IG:()=>a,JR:()=>s,ri:()=>i,z:()=>o});let s="https://shans-backend.onrender.com/api",a={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function o(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:n=2,timeout:i=8e3}=e,l=a.get("authToken"),d=a.get("userInfo");if(!l)return!1;if(d)try{JSON.parse(d);let e=a.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),i),r=`?_t=${Date.now()}`,o=await fetch(`${s}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),o.ok){let e=await o.json();return a.set("userInfo",JSON.stringify(e.user)),a.set("lastAuthCheck",Date.now().toString()),!0}if(401===o.status||403===o.status){a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${o.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===n){if(d)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function n(){let e=a.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=a.get("authToken");e&&fetch(`${s}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck")}},2032:(e,t,r)=>{Promise.resolve().then(r.bind(r,7992))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var s=r(687),a=r(3210),o=r(1439);let n=(0,a.createContext)(void 0);function i(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),u=async()=>{try{if(l(!0),!await (0,o.z)({redirectOnFail:!1}))return r(null),c(!1),!1;{let e=(0,o.HW)();return r(e),c(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),c(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let s=await fetch(`${o.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await s.json();if(s.ok)return o.IG.set("authToken",a.token),o.IG.set("userInfo",JSON.stringify(a.user)),o.IG.set("lastAuthCheck",Date.now().toString()),r(a.user),c(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,s.jsx)(n.Provider,{value:{user:t,isLoading:i,isAuthenticated:d,login:h,logout:()=>{r(null),c(!1),(0,o.ri)()},checkAuth:u},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>o});var s=r(7413);r(1135);var a=r(9131);let o={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function n({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,s.jsx)(a.AuthProvider,{children:e})})})}},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(687),a=r(3210),o=r(6189),n=r(769),i=r(854);function l(){let e=(0,o.useRouter)(),[t,r]=(0,a.useState)(""),[l,d]=(0,a.useState)(""),[c,u]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null),f=c.filter(e=>{let r=e.low_stock_threshold||5,s=e.available_stock<=r,a=e.item_name.toLowerCase().includes(t.toLowerCase())||e.item_code.toLowerCase().includes(t.toLowerCase()),o=""===l||r<=l;return s&&a&&o}).sort((e,t)=>e.available_stock/(e.low_stock_threshold||5)-t.available_stock/(t.low_stock_threshold||5)),g=e=>{let t=e.low_stock_threshold||5,r=e.available_stock/t;return 0===e.available_stock?{status:"Out of Stock",color:"bg-red-600",textColor:"text-red-600"}:r<=.5?{status:"Critical",color:"bg-red-500",textColor:"text-red-500"}:r<=1?{status:"Low Stock",color:"bg-orange-500",textColor:"text-orange-500"}:{status:"Normal",color:"bg-green-500",textColor:"text-green-500"}};return h?(0,s.jsx)(n.A,{requireAdmin:!0,children:(0,s.jsx)(i.A,{isVisible:!0,message:"Loading low stock data..."})}):(0,s.jsx)(n.A,{requireAdmin:!0,children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Low Stock Alert"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-red-600 rounded-full"}),(0,s.jsx)("span",{children:"Critical"}),(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-orange-500 rounded-full ml-4"}),(0,s.jsx)("span",{children:"Low Stock"}),(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-red-500 rounded-full ml-4"}),(0,s.jsx)("span",{children:"Out of Stock"})]})]})]}),(0,s.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Products"}),(0,s.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search by name or code...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Threshold"}),(0,s.jsx)("input",{type:"number",value:l,onChange:e=>d(e.target.value?parseInt(e.target.value):""),placeholder:"Filter by threshold...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",min:"0"})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"font-medium",children:["Total Low Stock Items: ",f.length]}),(0,s.jsxs)("div",{children:["Out of Stock: ",f.filter(e=>0===e.available_stock).length]})]})})]})}),f.length>0?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Room"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Stock"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Threshold"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Brand"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map(t=>{let r=g(t);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:`inline-block w-3 h-3 rounded-full ${r.color}`}),(0,s.jsx)("span",{className:`text-sm font-medium ${r.textColor}`,children:r.status})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.item_code}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t.item_name}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Room ",t.room_id]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium",children:t.available_stock}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.low_stock_threshold||5}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.car_brand_id?`Brand ${t.car_brand_id}`:"N/A"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.product_category?.replace(/_/g," ")||"N/A"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsx)("button",{onClick:()=>e.push(`/admin/manage-products?edit=${t.id}`),className:"text-blue-600 hover:text-blue-900",children:"Update Stock"})})]},t.id)})})]})})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,s.jsx)("div",{className:"text-green-600 text-6xl mb-4",children:"✓"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"All Stock Levels Normal"}),(0,s.jsx)("p",{className:"text-gray-500",children:t||l?"No products match your search criteria with low stock levels.":"All products are currently above their low stock thresholds."})]})]})})})}r(1439)},8138:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\low-stock\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\low-stock\\page.tsx","default")},8480:(e,t,r)=>{Promise.resolve().then(r.bind(r,8138))},8517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(5239),a=r(8088),o=r(8170),n=r.n(o),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["low-stock",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8138)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\low-stock\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\low-stock\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/low-stock/page",pathname:"/admin/low-stock",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[825],()=>r(8517));module.exports=s})();