(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[259],{4910:(e,s,t)=>{Promise.resolve().then(t.bind(t,7126))},7126:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var n=t(5155),r=t(2115),a=t(5695),i=t(9053),c=t(7389),l=t(2799),o=t(5731),d=t(3213);let m={company1:{key:"company1",name:"Shans Accessories PTY LTD",bankingInformation:"\n      First National Bank<br>\n      Account :  ***********<br>\n      Branch code 257705<br>\n      Swift code FIRNZAJJ\n    "},company2:{key:"company2",name:"Shans Autosport PTY LTD",bankingInformation:"\n      Business Account<br>\n      Capitec Current Account<br>\n      Account: **********\n    "},company3:{key:"company3",name:"Shans Motorstyle PTY LTD",bankingInformation:"\n      SHANS MOTORSTYLE (PTY) LTD<br>\n      Gold Business Account<br>\n      Account Number: ***********<br>\n      Branch Code: 250655<br>\n      Swift Code: FIRNZAJJ\n    "}};function x(){var e;let s=(0,a.useRouter)();(0,a.useSearchParams)();let{messages:t,showToast:x,removeToast:h}=(0,c.d)(),[u,p]=(0,r.useState)(null),[g,b]=(0,r.useState)(!0),[j,y]=(0,r.useState)(""),{mutate:f,loading:N,error:v}=(0,d.L2)(o.FH.quotations.create),I=e=>"".concat("R").concat(parseFloat(e.toString()).toLocaleString("en-ZA",{minimumFractionDigits:0,maximumFractionDigits:0})),T=()=>Math.floor(1e6+9e6*Math.random()).toString(),S=()=>{try{let e=l.IG.get("customerInfo"),t=l.IG.get("selectedProducts"),n=l.IG.get("paymentMethod"),r=l.IG.get("comments"),a=l.IG.get("salespersonName"),i=l.IG.get("selectedCompany"),c=l.IG.get("includeTax");if(!e||!t){x("No order data found. Please create an order first.","error"),s.push("/");return}let o=JSON.parse(e),d=JSON.parse(t),m=JSON.parse(i||"{}").key||"company1";p({customerInfo:o,selectedProducts:d,paymentMethod:n||"Cash",comments:r||"",salespersonName:a||"",companyKey:m,includeTax:"true"===c}),y(T())}catch(e){console.error("Error loading order data:",e),x("Error loading order data","error"),s.push("/")}finally{b(!1)}},_=()=>{if(!u)return{subtotal:0,tax:0,total:0};let e=0,s=0,t=0;return u.selectedProducts.forEach(n=>{let r=n.price*n.quantity;if(u.includeTax){let a=n.price/1.15,i=n.price-a;e+=a*n.quantity,s+=i*n.quantity,t+=r}else e+=r,t+=r}),{subtotal:Math.round(e),tax:Math.round(s),total:Math.round(t)}},w=async()=>{if(u)try{let e=_(),t=m[u.companyKey],n={reference_number:j,company:{name:t.name,bankingInformation:t.bankingInformation},billing:u.customerInfo.billing,shipping:u.customerInfo.shipping,items:u.selectedProducts.map(e=>({item_code:e.item_code,item_name:e.name,room_name:e.room_name,quantity:e.quantity,unit_price_including_tax:u.includeTax?e.price:1.15*e.price,unit_price_excluding_tax:u.includeTax?e.price/1.15:e.price,total_price:e.price*e.quantity,tax_per_product:u.includeTax?e.price-e.price/1.15:.15*e.price})),subtotal:e.subtotal,tax:e.tax,total:e.total,payment_method:u.paymentMethod,comments:u.comments,salesperson_name:u.salespersonName,include_tax:u.includeTax};await f(n)&&(x("Quotation created successfully!","success"),l.IG.remove("customerInfo"),l.IG.remove("selectedProducts"),l.IG.remove("paymentMethod"),l.IG.remove("comments"),l.IG.remove("salespersonName"),l.IG.remove("includeTax"),setTimeout(()=>{s.push("/")},2e3))}catch(e){console.error("Error creating quotation:",e),x("Error: ".concat(e instanceof Error?e.message:"Failed to create quotation"),"error")}};if((0,r.useEffect)(()=>{S()},[]),(0,r.useEffect)(()=>{v&&x("Error: ".concat(v),"error")},[v,x]),g)return(0,n.jsx)(i.A,{children:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading quotation..."})]})})});if(!u)return(0,n.jsx)(i.A,{children:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"No order data found"}),(0,n.jsx)("button",{onClick:()=>s.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Go to Home"})]})})});let k=_(),A=m[u.companyKey];return(0,n.jsx)(i.A,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-100 p-5",children:[(0,n.jsx)(c.A,{messages:t,onRemove:h}),(0,n.jsxs)("div",{className:"flex gap-5 mb-5",children:[(0,n.jsx)("button",{onClick:()=>s.push("/"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Home"}),(0,n.jsx)("button",{onClick:()=>{sessionStorage.setItem("editingOrder","true"),s.push("/")},className:"text-blue-600 hover:text-blue-800 transition-colors",children:"✏️ Edit Order"})]}),(0,n.jsxs)("div",{id:"pdf-1",className:"max-w-4xl mx-auto bg-white p-8 shadow-lg",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"QUOTATION"}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Reference: ",j]}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Date: ",(e=new Date,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e))]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:A.name}),(0,n.jsx)("div",{className:"text-sm text-gray-600",dangerouslySetInnerHTML:{__html:A.bankingInformation}})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"BILL TO"}),(0,n.jsxs)("div",{className:"text-gray-600",children:[(0,n.jsx)("p",{children:u.customerInfo.billing.name}),(0,n.jsx)("p",{children:u.customerInfo.billing.email}),(0,n.jsx)("p",{children:u.customerInfo.billing.address}),(0,n.jsx)("p",{children:u.customerInfo.billing.phone})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"SHIP TO"}),(0,n.jsx)("div",{className:"text-gray-600",children:u.customerInfo.shipping?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:u.customerInfo.shipping.name}),(0,n.jsx)("p",{children:u.customerInfo.shipping.email}),(0,n.jsx)("p",{children:u.customerInfo.shipping.address}),(0,n.jsx)("p",{children:u.customerInfo.shipping.phone})]}):(0,n.jsx)("p",{children:"Same as billing address"})})]})]}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"border border-gray-300",children:[(0,n.jsxs)("div",{className:"bg-gray-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300",children:[(0,n.jsx)("div",{className:"col-span-4",children:"DESCRIPTION"}),(0,n.jsx)("div",{className:"col-span-1 text-center",children:"QTY"}),(0,n.jsx)("div",{className:"col-span-2 text-right",children:"UNIT PRICE"}),u.includeTax&&(0,n.jsx)("div",{className:"col-span-2 text-right",children:"TAX"}),(0,n.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right"),children:"TOTAL"})]}),u.selectedProducts.map((e,s)=>{let t=e.price*e.quantity,r=u.includeTax?e.price/1.15:e.price,a=u.includeTax?e.price-r:0;return(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0",children:[(0,n.jsxs)("div",{className:"col-span-4",children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Room: ",e.room_name]})]}),(0,n.jsx)("div",{className:"col-span-1 text-center",children:e.quantity}),(0,n.jsx)("div",{className:"col-span-2 text-right",children:I(u.includeTax?r:e.price)}),u.includeTax&&(0,n.jsx)("div",{className:"col-span-2 text-right",children:I(a)}),(0,n.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right font-medium"),children:I(t)})]},s)})]})}),(0,n.jsx)("div",{className:"flex justify-end mb-8",children:(0,n.jsxs)("div",{className:"w-64",children:[(0,n.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,n.jsx)("span",{children:"Subtotal:"}),(0,n.jsx)("span",{children:I(k.subtotal)})]}),u.includeTax&&(0,n.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,n.jsx)("span",{children:"Tax (15%):"}),(0,n.jsx)("span",{children:I(k.tax)})]}),(0,n.jsxs)("div",{className:"flex justify-between py-2 font-bold text-lg",children:[(0,n.jsx)("span",{children:"Total:"}),(0,n.jsx)("span",{children:I(k.total)})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Method"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.paymentMethod})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Salesperson"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.salespersonName||"N/A"})]})]}),u.comments&&(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Comments"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.comments})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,n.jsxs)("button",{onClick:w,disabled:N,className:"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2",children:[N&&(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),N?"Creating...":"Confirm Quotation"]}),(0,n.jsx)("button",{onClick:()=>window.print(),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"\uD83D\uDCF8 Print/Save"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[535,441,684,358],()=>s(4910)),_N_E=e.O()}]);