(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var a=r(5155),s=r(2115),n=r(2799);let o=(0,s.createContext)(void 0);function l(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[i,c]=(0,s.useState)(!0),[u,d]=(0,s.useState)(!1),m=async()=>{try{if(c(!0),!await (0,n.z)({redirectOnFail:!1}))return l(null),d(!1),!1;{let e=(0,n.HW)();return l(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),l(null),d(!1),!1}finally{c(!1)}},h=async(e,t)=>{try{c(!0);let r=await fetch("".concat(n.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return n.IG.set("authToken",a.token),n.IG.set("userInfo",JSON.stringify(a.user)),n.IG.set("lastAuthCheck",Date.now().toString()),l(a.user),d(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{c(!1)}};return(0,s.useEffect)(()=>{m()},[]),(0,a.jsx)(o.Provider,{value:{user:r,isLoading:i,isAuthenticated:u,login:h,logout:()=>{l(null),d(!1),(0,n.ri)()},checkAuth:m},children:t})}},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>o,IG:()=>s,JR:()=>a,ri:()=>l,z:()=>n});let a="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:n=2,timeout:o=8e3}=e,l=s.get("authToken"),i=s.get("userInfo");if(!l)return t&&(window.location.href="/login"),!1;if(i)try{JSON.parse(i);let e=s.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),o),n="?_t=".concat(Date.now()),i=await fetch("".concat(a,"/auth/me").concat(n),{headers:{Authorization:"Bearer ".concat(l)},signal:e.signal});if(clearTimeout(r),i.ok){let e=await i.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===i.status||403===i.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(i.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===n){if(i)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function o(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function l(){let e=s.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},3213:(e,t,r)=>{"use strict";r.d(t,{L2:()=>o,U9:()=>n,gf:()=>s});var a=r(2115);function s(e){let[t,r]=(0,a.useState)({data:null,loading:!1,error:null}),s=(0,a.useCallback)(async function(){for(var t=arguments.length,a=Array(t),s=0;s<t;s++)a[s]=arguments[s];r(e=>({...e,loading:!0,error:null}));try{let t=await e(...a);if(t.success&&t.data)return r({data:t.data,loading:!1,error:null}),t.data;return r({data:null,loading:!1,error:t.error||"An unknown error occurred"}),null}catch(e){return r({data:null,loading:!1,error:e instanceof Error?e.message:"An unknown error occurred"}),null}},[e]),n=(0,a.useCallback)(()=>{r({data:null,loading:!1,error:null})},[]);return{...t,execute:s,reset:n}}function n(e){let t=s(e),r=(0,a.useCallback)(e=>{t.data&&i(t=>({...t,data:t.data?[...t.data,...e]:e}))},[t.data]),n=(0,a.useCallback)((e,t)=>{i(r=>{if(!r.data)return r;let a=[...r.data];return a[e]=t,{...r,data:a}})},[]),o=(0,a.useCallback)(e=>{i(t=>{if(!t.data)return t;let r=t.data.filter((t,r)=>r!==e);return{...t,data:r}})},[]),[l,i]=(0,a.useState)({data:null,loading:!1,error:null});return{...t,append:r,updateItem:n,removeItem:o}}function o(e){let[t,r]=(0,a.useState)(!1),[s,n]=(0,a.useState)(null);return{mutate:(0,a.useCallback)(async t=>{r(!0),n(null);try{let a=await e(t);if(a.success&&a.data)return r(!1),a.data;return n(a.error||"An unknown error occurred"),r(!1),null}catch(e){return n(e instanceof Error?e.message:"An unknown error occurred"),r(!1),null}},[e]),loading:t,error:s,reset:(0,a.useCallback)(()=>{r(!1),n(null)},[])}}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},7960:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(5155);function s(e){let{isVisible:t,message:r="Loading products, please wait...",error:s,onRetry:n}=e;return t?(0,a.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,a.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:s?"Connection Error":r}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:s}),n&&(0,a.jsx)("button",{onClick:n,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},8283:(e,t,r)=>{Promise.resolve().then(r.bind(r,8823))},8823:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(5155),s=r(2115),n=r(5695),o=r(9053),l=r(7960),i=r(3213);function c(){let e=(0,n.useRouter)(),[t,r]=(0,s.useState)(""),[c,d]=(0,s.useState)(!1),[m,h]=(0,s.useState)(!1),[g,x]=(0,s.useState)(null),{data:f=[],loading:b,error:p,refetch:y}=(0,i.U9)("/rooms"),w=(0,i.L2)("/rooms","POST"),v=(0,i.L2)("/rooms","PUT"),j=(0,i.L2)("/rooms","DELETE"),N=f.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.description&&e.description.toLowerCase().includes(t.toLowerCase())),k=async e=>{try{await w.mutate(e),d(!1),y()}catch(e){console.error("Error adding room:",e)}},S=async e=>{if(g)try{await v.mutate(e,{endpoint:"/rooms/".concat(g.id)}),h(!1),x(null),y()}catch(e){console.error("Error updating room:",e)}},C=async e=>{if(confirm("Are you sure you want to delete this room? This action cannot be undone."))try{await j.mutate(void 0,{endpoint:"/rooms/".concat(e)}),y()}catch(e){console.error("Error deleting room:",e)}},A=e=>{x(e),h(!0)};return b?(0,a.jsx)(o.A,{requireAdmin:!0,children:(0,a.jsx)(l.A,{isVisible:!0,message:"Loading rooms..."})}):(0,a.jsx)(o.A,{requireAdmin:!0,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Rooms"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[(0,a.jsxs)("button",{onClick:()=>d(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"+"})," Add Room"]}),(0,a.jsx)("div",{className:"w-full sm:w-auto",children:(0,a.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search rooms...",className:"w-full sm:w-64 border border-gray-300 rounded-md px-3 py-2 text-sm"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>A(e),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>C(e.id),className:"text-red-600 hover:text-red-800 text-sm",children:"Delete"})]})]}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.description}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["Room ID: ",e.id]})})]},e.id))}),0===N.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:t?"No rooms found matching your search.":"No rooms available. Add your first room to get started."})}),c&&(0,a.jsx)(u,{isOpen:c,onClose:()=>d(!1),onSubmit:k,title:"Add Room"}),m&&g&&(0,a.jsx)(u,{isOpen:m,onClose:()=>{h(!1),x(null)},onSubmit:S,title:"Edit Room",initialData:g})]})})})}function u(e){let{isOpen:t,onClose:r,onSubmit:n,title:o,initialData:l}=e,[i,c]=(0,s.useState)({name:(null==l?void 0:l.name)||"",description:(null==l?void 0:l.description)||""}),u=(e,t)=>{c(r=>({...r,[e]:t}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:o}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 text-2xl",children:"\xd7"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n(i)},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room Name *"}),(0,a.jsx)("input",{type:"text",value:i.name,onChange:e=>u("name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,placeholder:"Enter room name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:i.description,onChange:e=>u("description",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",rows:3,placeholder:"Enter room description (optional)"})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[l?"Update":"Add"," Room"]})]})]})]})})}):null}},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155),s=r(2115),n=r(5695),o=r(283);function l(e){let{children:t,requireAdmin:r=!1,redirectTo:l="/login"}=e,{user:i,isLoading:c,isAuthenticated:u}=(0,o.A)(),d=(0,n.useRouter)(),[m,h]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(!c){if(!u)return void d.push(l);if(r&&i&&!i.is_admin)return void d.push("/");h(!0)}},[u,c,i,r,d,l]),c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):m?(0,a.jsx)(a.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(8283)),_N_E=e.O()}]);