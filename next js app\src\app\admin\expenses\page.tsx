'use client';

import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';

export default function Expenses() {
  const router = useRouter();

  return (
    <ProtectedRoute requireAdmin>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/admin')}
              className="text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2"
            >
              ← Back to Admin Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Expenses</h1>
          </div>

          {/* Coming Soon */}
          <div className="bg-white rounded-lg shadow p-12 text-center">
            <div className="text-purple-600 text-6xl mb-4">🧾</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Expense Management</h3>
            <p className="text-gray-500 mb-6">
              This feature is coming soon. You&apos;ll be able to track and manage business expenses.
            </p>
            <div className="space-y-2 text-sm text-gray-600">
              <div>• Record business expenses</div>
              <div>• Categorize expenses</div>
              <div>• Generate expense reports</div>
              <div>• Track monthly/yearly totals</div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
