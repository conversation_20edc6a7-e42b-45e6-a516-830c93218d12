(()=>{var e={};e.id=520,e.ids=[520],e.modules={14:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(5239),n=r(8088),o=r(8170),a=r.n(o),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>a,IG:()=>n,JR:()=>s,ri:()=>i,z:()=>o});let s="https://shans-backend.onrender.com/api",n={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function o(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:a=2,timeout:i=8e3}=e,l=n.get("authToken"),d=n.get("userInfo");if(!l)return!1;if(d)try{JSON.parse(d);let e=n.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=a;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),i),r=`?_t=${Date.now()}`,o=await fetch(`${s}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),o.ok){let e=await o.json();return n.set("userInfo",JSON.stringify(e.user)),n.set("lastAuthCheck",Date.now().toString()),!0}if(401===o.status||403===o.status){n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${o.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===a){if(d)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function a(){let e=n.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=n.get("authToken");e&&fetch(`${s}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck")}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var s=r(687),n=r(3210),o=r(1439);let a=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,n.useState)(null),[i,l]=(0,n.useState)(!0),[d,c]=(0,n.useState)(!1),u=async()=>{try{if(l(!0),!await (0,o.z)({redirectOnFail:!1}))return r(null),c(!1),!1;{let e=(0,o.HW)();return r(e),c(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),c(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let s=await fetch(`${o.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),n=await s.json();if(s.ok)return o.IG.set("authToken",n.token),o.IG.set("userInfo",JSON.stringify(n.user)),o.IG.set("lastAuthCheck",Date.now().toString()),r(n.user),c(!0),{success:!0};return{success:!1,message:n.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,s.jsx)(a.Provider,{value:{user:t,isLoading:i,isAuthenticated:d,login:h,logout:()=>{r(null),c(!1),(0,o.ri)()},checkAuth:u},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4279:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var s=r(7413);r(1135);var n=r(9131);let o={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function a({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,s.jsx)(n.AuthProvider,{children:e})})})}},4543:(e,t,r)=>{Promise.resolve().then(r.bind(r,9488))},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\login\\page.tsx","default")},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(687),n=r(3210),o=r(6189),a=r(3213),i=r(1439);function l(){let[e,t]=(0,n.useState)(""),[r,l]=(0,n.useState)(""),[d,c]=(0,n.useState)(!1),[u,h]=(0,n.useState)(""),[m,p]=(0,n.useState)(""),[x,b]=(0,n.useState)("checking"),{login:f,isAuthenticated:g,user:v}=(0,a.A)();(0,o.useRouter)();let w=async()=>{try{b("checking");let e=new AbortController,t=setTimeout(()=>e.abort(),15e3);try{let r=await fetch(`${i.JR}/health`,{method:"GET",signal:e.signal,headers:{"Content-Type":"application/json"}});if(clearTimeout(t),r.ok)b("ready");else throw Error("Server responded with error")}catch(e){try{await fetch(`${i.JR}/products`,{method:"GET",headers:{"Content-Type":"application/json"}}),b("ready")}catch(e){console.error("Server check failed:",e),b("error")}}}catch(e){console.error("Server status check failed:",e),b("error")}},y=async t=>{t.preventDefault(),h(""),p(""),c(!0);let s=await f(e,r);s.success?p("Login successful! Redirecting..."):h(s.message||"Login failed"),c(!1)};return"checking"===x?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg text-center max-w-md w-full mx-4",children:[(0,s.jsx)("div",{className:"w-15 h-15 border-6 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-5"}),(0,s.jsx)("div",{className:"text-lg font-bold text-gray-800 mb-2",children:"Connecting to Server"}),(0,s.jsxs)("div",{className:"text-gray-600 text-sm leading-relaxed",children:["Please wait while we check the server status...",(0,s.jsx)("br",{}),"This may take a few moments."]})]})}):"error"===x?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg text-center max-w-md w-full mx-4",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-gray-800 mb-2",children:"Server Connection Failed"}),(0,s.jsxs)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-4 rounded mb-4",children:[(0,s.jsx)("strong",{children:"Server Connection Failed"}),(0,s.jsx)("br",{}),"Unable to connect to the server. Please check your internet connection or try again later."]}),(0,s.jsx)("button",{onClick:w,className:"bg-blue-600 text-white px-5 py-2 rounded hover:bg-blue-700 transition-colors",children:"Retry Connection"})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 p-5",children:(0,s.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg w-full max-w-md text-center border border-gray-200 animate-fadeInUp",children:[(0,s.jsx)("div",{className:"w-18 h-18 mx-auto mb-6 bg-blue-600 rounded-lg flex items-center justify-center text-white text-3xl font-bold shadow-lg",children:"S"}),(0,s.jsx)("h1",{className:"text-gray-800 mb-2 text-2xl font-normal",children:"Shans System"}),(0,s.jsxs)("div",{className:"text-gray-600 text-sm mb-8 leading-relaxed",children:["Please sign in to your account",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Note:"})," Admins will be automatically redirected to the admin dashboard"]}),d?(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Signing you in..."})]}):(0,s.jsxs)("form",{onSubmit:y,className:"space-y-5",children:[(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("label",{htmlFor:"email",className:"block mb-1 text-gray-800",children:"Email Address"}),(0,s.jsx)("input",{type:"email",id:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded text-base transition-all duration-300 focus:outline-none focus:border-blue-600 focus:shadow-md"})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("label",{htmlFor:"password",className:"block mb-1 text-gray-800",children:"Password"}),(0,s.jsx)("input",{type:"password",id:"password",value:r,onChange:e=>l(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded text-base transition-all duration-300 focus:outline-none focus:border-blue-600 focus:shadow-md"})]}),(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full p-3 bg-blue-600 text-white border-none rounded text-base font-normal cursor-pointer transition-colors duration-300 mt-2 hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed",children:"Sign In"})]}),u&&(0,s.jsx)("div",{className:"text-red-600 mt-4 p-3 bg-red-100 border border-red-300 rounded text-sm",children:u}),m&&(0,s.jsx)("div",{className:"text-green-700 mt-4 p-3 bg-green-100 border border-green-300 rounded text-sm",children:m}),(0,s.jsx)("div",{className:"mt-8 text-gray-500 text-xs border-t border-gray-200 pt-5",children:(0,s.jsx)("p",{children:"Shans System \xa9 2024"})})]})})}},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[825],()=>r(14));module.exports=s})();