{"version": 3, "sources": ["../../../../src/build/webpack/config/helpers.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nexport const loader = curry(function loader(\n  rule: webpack.RuleSetRule,\n  config: webpack.Configuration\n) {\n  if (!config.module) {\n    config.module = { rules: [] }\n  }\n\n  if (rule.oneOf) {\n    const existing = config.module.rules?.find(\n      (arrayRule) =>\n        arrayRule && typeof arrayRule === 'object' && arrayRule.oneOf\n    )\n    if (existing && typeof existing === 'object') {\n      existing.oneOf!.push(...rule.oneOf)\n      return config\n    }\n  }\n\n  config.module.rules?.push(rule)\n  return config\n})\n\nexport const unshiftLoader = curry(function unshiftLoader(\n  rule: webpack.RuleSetRule,\n  config: webpack.Configuration\n) {\n  if (!config.module) {\n    config.module = { rules: [] }\n  }\n\n  if (rule.oneOf) {\n    const existing = config.module.rules?.find(\n      (arrayRule) =>\n        arrayRule && typeof arrayRule === 'object' && arrayRule.oneOf\n    )\n    if (existing && typeof existing === 'object') {\n      existing.oneOf?.unshift(...rule.oneOf)\n      return config\n    }\n  }\n\n  config.module.rules?.unshift(rule)\n  return config\n})\n\nexport const plugin = curry(function plugin(\n  p: webpack.WebpackPluginInstance,\n  config: webpack.Configuration\n) {\n  if (!config.plugins) {\n    config.plugins = []\n  }\n  config.plugins.push(p)\n  return config\n})\n"], "names": ["loader", "plugin", "unshiftLoader", "curry", "rule", "config", "module", "rules", "oneOf", "existing", "find", "arrayRule", "push", "unshift", "p", "plugins"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,MAAM;eAANA;;IA8CAC,MAAM;eAANA;;IAvBAC,aAAa;eAAbA;;;oEA1BK;;;;;;AAGX,MAAMF,SAASG,IAAAA,oBAAK,EAAC,SAASH,OACnCI,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;YAC5CA,SAASD,KAAK,CAAEI,IAAI,IAAIR,KAAKI,KAAK;YAClC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBO,IAAI,CAACR;IAC1B,OAAOC;AACT;AAEO,MAAMH,gBAAgBC,IAAAA,oBAAK,EAAC,SAASD,cAC1CE,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;gBAC5CA;aAAAA,kBAAAA,SAASD,KAAK,qBAAdC,gBAAgBI,OAAO,IAAIT,KAAKI,KAAK;YACrC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBQ,OAAO,CAACT;IAC7B,OAAOC;AACT;AAEO,MAAMJ,SAASE,IAAAA,oBAAK,EAAC,SAASF,OACnCa,CAAgC,EAChCT,MAA6B;IAE7B,IAAI,CAACA,OAAOU,OAAO,EAAE;QACnBV,OAAOU,OAAO,GAAG,EAAE;IACrB;IACAV,OAAOU,OAAO,CAACH,IAAI,CAACE;IACpB,OAAOT;AACT"}