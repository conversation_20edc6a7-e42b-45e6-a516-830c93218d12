/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff82097a3125\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZmODIwOTdhMzEyNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\nconst metadata = {\n    title: 'Shans System',\n    description: 'Receipt and Quotation Generator',\n    viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',\n    other: {\n        'mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-capable': 'yes',\n        'theme-color': '#ffffff',\n        'format-detection': 'telephone=no'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans leading-relaxed m-0 p-0 bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQjtBQUMrQjtBQUU5QyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsT0FBTztRQUNMLDBCQUEwQjtRQUMxQixnQ0FBZ0M7UUFDaEMsZUFBZTtRQUNmLG9CQUFvQjtJQUN0QjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDZCw0RUFBQ1gsK0RBQVlBOzBCQUNWTzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTaGFucyBTeXN0ZW0nLFxuICBkZXNjcmlwdGlvbjogJ1JlY2VpcHQgYW5kIFF1b3RhdGlvbiBHZW5lcmF0b3InLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAsIG1heGltdW0tc2NhbGU9MS4wLCB1c2VyLXNjYWxhYmxlPW5vJyxcbiAgb3RoZXI6IHtcbiAgICAnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZSc6ICd5ZXMnLFxuICAgICdhcHBsZS1tb2JpbGUtd2ViLWFwcC1jYXBhYmxlJzogJ3llcycsXG4gICAgJ3RoZW1lLWNvbG9yJzogJyNmZmZmZmYnLFxuICAgICdmb3JtYXQtZGV0ZWN0aW9uJzogJ3RlbGVwaG9uZT1ubycsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGxlYWRpbmctcmVsYXhlZCBtLTAgcC0wIGJnLWdyYXktMTAwXCI+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2aWV3cG9ydCIsIm90aGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkAuth = async ()=>{\n        try {\n            setIsLoading(true);\n            const authenticated = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.checkAuthStatus)({\n                redirectOnFail: false\n            });\n            if (authenticated) {\n                const currentUser = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                setUser(currentUser);\n                setIsAuthenticated(true);\n                return true;\n            } else {\n                setUser(null);\n                setIsAuthenticated(false);\n                return false;\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n            setUser(null);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(`${_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.API_BASE_URL}/auth/login`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Store authentication token and user info\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('authToken', data.token);\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('userInfo', JSON.stringify(data.user));\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('lastAuthCheck', Date.now().toString());\n                setUser(data.user);\n                setIsAuthenticated(true);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    message: data.message || 'Login failed'\n                };\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                message: 'Network error. Please try again.'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setIsAuthenticated(false);\n        (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.logout)();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   initAuth: () => (/* binding */ initAuth),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n// Shared authentication utilities for Next.js app\nconst API_BASE_URL = 'https://shans-backend.onrender.com/api';\n// Storage utility with fallback\nconst storage = {\n    get: function(key) {\n        if (true) return null;\n        try {\n            return localStorage.getItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            return sessionStorage.getItem(key);\n        }\n    },\n    set: function(key, value) {\n        if (true) return;\n        try {\n            localStorage.setItem(key, value);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.setItem(key, value);\n        }\n    },\n    remove: function(key) {\n        if (true) return;\n        try {\n            localStorage.removeItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.removeItem(key);\n        }\n    }\n};\n// Enhanced authentication check with better error handling\nasync function checkAuthStatus(options = {}) {\n    const { redirectOnFail = true, showLoading = false, retryCount = 2, timeout = 8000 } = options;\n    const token = storage.get('authToken');\n    const userInfo = storage.get('userInfo');\n    // If no token, redirect immediately\n    if (!token) {\n        if (redirectOnFail && \"undefined\" !== 'undefined') {}\n        return false;\n    }\n    // If we have userInfo cached and it's recent, trust it temporarily\n    if (userInfo) {\n        try {\n            const user = JSON.parse(userInfo);\n            const lastCheck = storage.get('lastAuthCheck');\n            const now = Date.now();\n            // If last check was less than 5 minutes ago, skip server validation\n            if (lastCheck && now - parseInt(lastCheck) < 5 * 60 * 1000) {\n                return true;\n            }\n        } catch (e) {\n            console.warn('Error parsing cached user info:', e);\n        }\n    }\n    // Validate token with server\n    for(let attempt = 0; attempt <= retryCount; attempt++){\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            // Add cache busting parameter to prevent cached responses\n            const cacheBuster = `?_t=${Date.now()}`;\n            const response = await fetch(`${API_BASE_URL}/auth/me${cacheBuster}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                // Update cached user info and timestamp\n                storage.set('userInfo', JSON.stringify(data.user));\n                storage.set('lastAuthCheck', Date.now().toString());\n                return true;\n            } else if (response.status === 401 || response.status === 403) {\n                // Token is invalid, clear storage and redirect\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            } else {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n        } catch (error) {\n            console.warn(`Auth check attempt ${attempt + 1} failed:`, error);\n            // If this is the last attempt, handle the failure\n            if (attempt === retryCount) {\n                // If we have cached user info, use it as fallback\n                if (userInfo) {\n                    console.log('Using cached authentication due to network issues');\n                    return true;\n                }\n                // No cached info and all attempts failed\n                console.error('Authentication failed after all retries:', error);\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            }\n            // Wait before retry (exponential backoff)\n            await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n    }\n    return false;\n}\n// Check if user is admin\nfunction isAdmin() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return false;\n    try {\n        const user = JSON.parse(userInfo);\n        return Boolean(user.is_admin);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return false;\n    }\n}\n// Get current user info\nfunction getCurrentUser() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return null;\n    try {\n        return JSON.parse(userInfo);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return null;\n    }\n}\n// Logout function\nfunction logout() {\n    const token = storage.get('authToken');\n    // Call logout endpoint (don't wait for response)\n    if (token) {\n        fetch(`${API_BASE_URL}/auth/logout`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`\n            }\n        }).catch((error)=>{\n            console.warn('Logout API call failed:', error);\n        });\n    }\n    // Clear storage and redirect\n    storage.remove('authToken');\n    storage.remove('userInfo');\n    storage.remove('lastAuthCheck');\n    if (false) {}\n}\n// Initialize authentication for a page\nasync function initAuth(options = {}) {\n    const { requireAuth = true, showLoading = false } = options;\n    if (!requireAuth) return true;\n    try {\n        const isAuthenticated = await checkAuthStatus({\n            showLoading,\n            redirectOnFail: true\n        });\n        return isAuthenticated;\n    } catch (error) {\n        console.error('Auth initialization failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();