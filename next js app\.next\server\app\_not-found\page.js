(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>i,IG:()=>o,JR:()=>n,ri:()=>a,z:()=>s});let n="https://shans-backend.onrender.com/api",o={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function s(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:i=2,timeout:a=8e3}=e,l=o.get("authToken"),u=o.get("userInfo");if(!l)return!1;if(u)try{JSON.parse(u);let e=o.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=i;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),a),r=`?_t=${Date.now()}`,s=await fetch(`${n}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),s.ok){let e=await s.json();return o.set("userInfo",JSON.stringify(e.user)),o.set("lastAuthCheck",Date.now().toString()),!0}if(401===s.status||403===s.status){o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${s.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===i){if(u)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function i(){let e=o.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function a(){let e=o.get("authToken");e&&fetch(`${n}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck")}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>l});var n=r(687),o=r(3210),s=r(1439);let i=(0,o.createContext)(void 0);function a(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(!0),[u,d]=(0,o.useState)(!1),c=async()=>{try{if(l(!0),!await (0,s.z)({redirectOnFail:!1}))return r(null),d(!1),!1;{let e=(0,s.HW)();return r(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),d(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let n=await fetch(`${s.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),o=await n.json();if(n.ok)return s.IG.set("authToken",o.token),s.IG.set("userInfo",JSON.stringify(o.user)),s.IG.set("lastAuthCheck",Date.now().toString()),r(o.user),d(!0),{success:!0};return{success:!1,message:o.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,n.jsx)(i.Provider,{value:{user:t,isLoading:a,isAuthenticated:u,login:h,logout:()=>{r(null),d(!1),(0,s.ri)()},checkAuth:c},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});var n=r(7413);r(1135);var o=r(9131);let s={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function i({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,n.jsx)(o.AuthProvider,{children:e})})})}},5072:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var n=r(5239),o=r(8088),s=r(8170),i=r.n(s),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],c={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var n=r(2907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[825],()=>r(5072));module.exports=n})();