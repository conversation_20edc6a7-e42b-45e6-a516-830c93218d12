{"version": 3, "sources": ["../../../src/build/analysis/get-page-static-info.ts"], "sourcesContent": ["import type { NextConfig } from '../../server/config-shared'\nimport type { RouteHas } from '../../lib/load-custom-routes'\n\nimport { promises as fs } from 'fs'\nimport { LRUCache } from '../../server/lib/lru-cache'\nimport {\n  extractExportedConstValue,\n  UnsupportedValueError,\n} from './extract-const-value'\nimport { parseModule } from './parse-module'\nimport * as Log from '../output/log'\nimport { SERVER_RUNTIME } from '../../lib/constants'\nimport { tryToParsePath } from '../../lib/try-to-parse-path'\nimport { isAPIRoute } from '../../lib/is-api-route'\nimport { isEdgeRuntime } from '../../lib/is-edge-runtime'\nimport { RSC_MODULE_TYPES } from '../../shared/lib/constants'\nimport type { RSCMeta } from '../webpack/loaders/get-module-build-info'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport {\n  AppSegmentConfigSchemaKeys,\n  parseAppSegmentConfig,\n  type AppSegmentConfig,\n} from '../segment-config/app/app-segment-config'\nimport { reportZodError } from '../../shared/lib/zod'\nimport {\n  PagesSegmentConfigSchemaKeys,\n  parsePagesSegmentConfig,\n  type PagesSegmentConfig,\n  type PagesSegmentConfigConfig,\n} from '../segment-config/pages/pages-segment-config'\nimport {\n  MiddlewareConfigInputSchema,\n  SourceSchema,\n  type MiddlewareConfigMatcherInput,\n} from '../segment-config/middleware/middleware-config'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\n\nconst PARSE_PATTERN =\n  /(?<!(_jsx|jsx-))runtime|preferredRegion|getStaticProps|getServerSideProps|generateStaticParams|export const|generateImageMetadata|generateSitemaps/\n\nexport type MiddlewareMatcher = {\n  regexp: string\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n  originalSource: string\n}\n\nexport type MiddlewareConfig = {\n  /**\n   * The matcher for the middleware. Read more: [Next.js Docs: Middleware `matcher`](https://nextjs.org/docs/app/api-reference/file-conventions/middleware#matcher),\n   * [Next.js Docs: Middleware matching paths](https://nextjs.org/docs/app/building-your-application/routing/middleware#matching-paths)\n   */\n  matchers?: MiddlewareMatcher[]\n\n  /**\n   * The regions that the middleware should run in.\n   */\n  regions?: string | string[]\n\n  /**\n   * A glob, or an array of globs, ignoring dynamic code evaluation for specific\n   * files. The globs are relative to your application root folder.\n   */\n  unstable_allowDynamic?: string[]\n}\n\nexport interface AppPageStaticInfo {\n  type: PAGE_TYPES.APP\n  ssg?: boolean\n  ssr?: boolean\n  rsc?: RSCModuleType\n  generateStaticParams?: boolean\n  generateSitemaps?: boolean\n  generateImageMetadata?: boolean\n  middleware?: MiddlewareConfig\n  config: Omit<AppSegmentConfig, 'runtime' | 'maxDuration'> | undefined\n  runtime: AppSegmentConfig['runtime'] | undefined\n  preferredRegion: AppSegmentConfig['preferredRegion'] | undefined\n  maxDuration: number | undefined\n}\n\nexport interface PagesPageStaticInfo {\n  type: PAGE_TYPES.PAGES\n  getStaticProps?: boolean\n  getServerSideProps?: boolean\n  rsc?: RSCModuleType\n  generateStaticParams?: boolean\n  generateSitemaps?: boolean\n  generateImageMetadata?: boolean\n  middleware?: MiddlewareConfig\n  config:\n    | (Omit<PagesSegmentConfig, 'runtime' | 'config' | 'maxDuration'> & {\n        config?: Omit<PagesSegmentConfigConfig, 'runtime' | 'maxDuration'>\n      })\n    | undefined\n  runtime: PagesSegmentConfig['runtime'] | undefined\n  preferredRegion: PagesSegmentConfigConfig['regions'] | undefined\n  maxDuration: number | undefined\n}\n\nexport type PageStaticInfo = AppPageStaticInfo | PagesPageStaticInfo\n\nconst CLIENT_MODULE_LABEL =\n  /\\/\\* __next_internal_client_entry_do_not_use__ ([^ ]*) (cjs|auto) \\*\\//\n\nconst ACTION_MODULE_LABEL =\n  /\\/\\* __next_internal_action_entry_do_not_use__ (\\{[^}]+\\}) \\*\\//\n\nconst CLIENT_DIRECTIVE = 'use client'\nconst SERVER_ACTION_DIRECTIVE = 'use server'\n\nexport type RSCModuleType = 'server' | 'client'\nexport function getRSCModuleInformation(\n  source: string,\n  isReactServerLayer: boolean\n): RSCMeta {\n  const actionsJson = source.match(ACTION_MODULE_LABEL)\n  const parsedActionsMeta = actionsJson\n    ? (JSON.parse(actionsJson[1]) as Record<string, string>)\n    : undefined\n  const clientInfoMatch = source.match(CLIENT_MODULE_LABEL)\n  const isClientRef = !!clientInfoMatch\n\n  if (!isReactServerLayer) {\n    return {\n      type: RSC_MODULE_TYPES.client,\n      actionIds: parsedActionsMeta,\n      isClientRef,\n    }\n  }\n\n  const clientRefsString = clientInfoMatch?.[1]\n  const clientRefs = clientRefsString ? clientRefsString.split(',') : []\n  const clientEntryType = clientInfoMatch?.[2] as 'cjs' | 'auto' | undefined\n\n  const type = clientInfoMatch\n    ? RSC_MODULE_TYPES.client\n    : RSC_MODULE_TYPES.server\n\n  return {\n    type,\n    actionIds: parsedActionsMeta,\n    clientRefs,\n    clientEntryType,\n    isClientRef,\n  }\n}\n\n/**\n * Receives a parsed AST from SWC and checks if it belongs to a module that\n * requires a runtime to be specified. Those are:\n *   - Modules with `export function getStaticProps | getServerSideProps`\n *   - Modules with `export { getStaticProps | getServerSideProps } <from ...>`\n *   - Modules with `export const runtime = ...`\n */\nfunction checkExports(\n  ast: any,\n  expectedExports: string[],\n  page: string\n): {\n  getStaticProps?: boolean\n  getServerSideProps?: boolean\n  generateImageMetadata?: boolean\n  generateSitemaps?: boolean\n  generateStaticParams?: boolean\n  directives?: Set<string>\n  exports?: Set<string>\n} {\n  const exportsSet = new Set<string>([\n    'getStaticProps',\n    'getServerSideProps',\n    'generateImageMetadata',\n    'generateSitemaps',\n    'generateStaticParams',\n  ])\n  if (!Array.isArray(ast?.body)) {\n    return {}\n  }\n\n  try {\n    let getStaticProps: boolean = false\n    let getServerSideProps: boolean = false\n    let generateImageMetadata: boolean = false\n    let generateSitemaps: boolean = false\n    let generateStaticParams = false\n    let exports = new Set<string>()\n    let directives = new Set<string>()\n    let hasLeadingNonDirectiveNode = false\n\n    for (const node of ast.body) {\n      // There should be no non-string literals nodes before directives\n      if (\n        node.type === 'ExpressionStatement' &&\n        node.expression.type === 'StringLiteral'\n      ) {\n        if (!hasLeadingNonDirectiveNode) {\n          const directive = node.expression.value\n          if (CLIENT_DIRECTIVE === directive) {\n            directives.add('client')\n          }\n          if (SERVER_ACTION_DIRECTIVE === directive) {\n            directives.add('server')\n          }\n        }\n      } else {\n        hasLeadingNonDirectiveNode = true\n      }\n      if (\n        node.type === 'ExportDeclaration' &&\n        node.declaration?.type === 'VariableDeclaration'\n      ) {\n        for (const declaration of node.declaration?.declarations) {\n          if (expectedExports.includes(declaration.id.value)) {\n            exports.add(declaration.id.value)\n          }\n        }\n      }\n\n      if (\n        node.type === 'ExportDeclaration' &&\n        node.declaration?.type === 'FunctionDeclaration' &&\n        exportsSet.has(node.declaration.identifier?.value)\n      ) {\n        const id = node.declaration.identifier.value\n        getServerSideProps = id === 'getServerSideProps'\n        getStaticProps = id === 'getStaticProps'\n        generateImageMetadata = id === 'generateImageMetadata'\n        generateSitemaps = id === 'generateSitemaps'\n        generateStaticParams = id === 'generateStaticParams'\n      }\n\n      if (\n        node.type === 'ExportDeclaration' &&\n        node.declaration?.type === 'VariableDeclaration'\n      ) {\n        const id = node.declaration?.declarations[0]?.id.value\n        if (exportsSet.has(id)) {\n          getServerSideProps = id === 'getServerSideProps'\n          getStaticProps = id === 'getStaticProps'\n          generateImageMetadata = id === 'generateImageMetadata'\n          generateSitemaps = id === 'generateSitemaps'\n          generateStaticParams = id === 'generateStaticParams'\n        }\n      }\n\n      if (node.type === 'ExportNamedDeclaration') {\n        for (const specifier of node.specifiers) {\n          if (\n            specifier.type === 'ExportSpecifier' &&\n            specifier.orig?.type === 'Identifier'\n          ) {\n            const value = specifier.orig.value\n\n            if (!getServerSideProps && value === 'getServerSideProps') {\n              getServerSideProps = true\n            }\n            if (!getStaticProps && value === 'getStaticProps') {\n              getStaticProps = true\n            }\n            if (!generateImageMetadata && value === 'generateImageMetadata') {\n              generateImageMetadata = true\n            }\n            if (!generateSitemaps && value === 'generateSitemaps') {\n              generateSitemaps = true\n            }\n            if (!generateStaticParams && value === 'generateStaticParams') {\n              generateStaticParams = true\n            }\n            if (expectedExports.includes(value) && !exports.has(value)) {\n              // An export was found that was actually a re-export, and not a\n              // literal value. We should warn here.\n              Log.warn(\n                `Next.js can't recognize the exported \\`${value}\\` field in \"${page}\", it may be re-exported from another file. The default config will be used instead.`\n              )\n            }\n          }\n        }\n      }\n    }\n\n    return {\n      getStaticProps,\n      getServerSideProps,\n      generateImageMetadata,\n      generateSitemaps,\n      generateStaticParams,\n      directives,\n      exports,\n    }\n  } catch {}\n\n  return {}\n}\n\nasync function tryToReadFile(filePath: string, shouldThrow: boolean) {\n  try {\n    return await fs.readFile(filePath, {\n      encoding: 'utf8',\n    })\n  } catch (error: any) {\n    if (shouldThrow) {\n      error.message = `Next.js ERROR: Failed to read file ${filePath}:\\n${error.message}`\n      throw error\n    }\n  }\n}\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport function getMiddlewareMatchers(\n  matcherOrMatchers: MiddlewareConfigMatcherInput,\n  nextConfig: Pick<NextConfig, 'basePath' | 'i18n'>\n): MiddlewareMatcher[] {\n  const matchers = Array.isArray(matcherOrMatchers)\n    ? matcherOrMatchers\n    : [matcherOrMatchers]\n\n  const { i18n } = nextConfig\n\n  return matchers.map((matcher) => {\n    matcher = typeof matcher === 'string' ? { source: matcher } : matcher\n\n    const originalSource = matcher.source\n\n    let { source, ...rest } = matcher\n\n    const isRoot = source === '/'\n\n    if (i18n?.locales && matcher.locale !== false) {\n      source = `/:nextInternalLocale((?!_next/)[^/.]{1,})${\n        isRoot ? '' : source\n      }`\n    }\n\n    source = `/:nextData(_next/data/[^/]{1,})?${source}${\n      isRoot\n        ? `(${nextConfig.i18n ? '|\\\\.json|' : ''}/?index|/?index\\\\.json)?`\n        : '{(\\\\.json)}?'\n    }`\n\n    if (nextConfig.basePath) {\n      source = `${nextConfig.basePath}${source}`\n    }\n\n    // Validate that the source is still.\n    const result = SourceSchema.safeParse(source)\n    if (!result.success) {\n      reportZodError('Failed to parse middleware source', result.error)\n\n      // We need to exit here because middleware being built occurs before we\n      // finish setting up the server. Exiting here is the only way to ensure\n      // that we don't hang.\n      process.exit(1)\n    }\n\n    return {\n      ...rest,\n      // We know that parsed.regexStr is not undefined because we already\n      // checked that the source is valid.\n      regexp: tryToParsePath(result.data).regexStr!,\n      originalSource: originalSource || source,\n    }\n  })\n}\n\nfunction parseMiddlewareConfig(\n  page: string,\n  rawConfig: unknown,\n  nextConfig: NextConfig\n): MiddlewareConfig {\n  // If there's no config to parse, then return nothing.\n  if (typeof rawConfig !== 'object' || !rawConfig) return {}\n\n  const input = MiddlewareConfigInputSchema.safeParse(rawConfig)\n  if (!input.success) {\n    reportZodError(`${page} contains invalid middleware config`, input.error)\n\n    // We need to exit here because middleware being built occurs before we\n    // finish setting up the server. Exiting here is the only way to ensure\n    // that we don't hang.\n    process.exit(1)\n  }\n\n  const config: MiddlewareConfig = {}\n\n  if (input.data.matcher) {\n    config.matchers = getMiddlewareMatchers(input.data.matcher, nextConfig)\n  }\n\n  if (input.data.unstable_allowDynamic) {\n    config.unstable_allowDynamic = Array.isArray(\n      input.data.unstable_allowDynamic\n    )\n      ? input.data.unstable_allowDynamic\n      : [input.data.unstable_allowDynamic]\n  }\n\n  if (input.data.regions) {\n    config.regions = input.data.regions\n  }\n\n  return config\n}\n\nconst apiRouteWarnings = new LRUCache(250)\nfunction warnAboutExperimentalEdge(apiRoute: string | null) {\n  if (\n    process.env.NODE_ENV === 'production' &&\n    process.env.NEXT_PRIVATE_BUILD_WORKER === '1'\n  ) {\n    return\n  }\n\n  if (apiRouteWarnings.has(apiRoute)) {\n    return\n  }\n\n  Log.warn(\n    apiRoute\n      ? `${apiRoute} provided runtime 'experimental-edge'. It can be updated to 'edge' instead.`\n      : `You are using an experimental edge runtime, the API might change.`\n  )\n  apiRouteWarnings.set(apiRoute, 1)\n}\n\nexport let hadUnsupportedValue = false\nconst warnedUnsupportedValueMap = new LRUCache<boolean>(250, () => 1)\n\nfunction warnAboutUnsupportedValue(\n  pageFilePath: string,\n  page: string | undefined,\n  error: UnsupportedValueError\n) {\n  hadUnsupportedValue = true\n  const isProductionBuild = process.env.NODE_ENV === 'production'\n  if (\n    // we only log for the server compilation so it's not\n    // duplicated due to webpack build worker having fresh\n    // module scope for each compiler\n    process.env.NEXT_COMPILER_NAME !== 'server' ||\n    (isProductionBuild && warnedUnsupportedValueMap.has(pageFilePath))\n  ) {\n    return\n  }\n  warnedUnsupportedValueMap.set(pageFilePath, true)\n\n  const message =\n    `Next.js can't recognize the exported \\`config\\` field in ` +\n    (page ? `route \"${page}\"` : `\"${pageFilePath}\"`) +\n    ':\\n' +\n    error.message +\n    (error.path ? ` at \"${error.path}\"` : '') +\n    '.\\n' +\n    'Read More - https://nextjs.org/docs/messages/invalid-page-config'\n\n  // for a build we use `Log.error` instead of throwing\n  // so that all errors can be logged before exiting the process\n  if (isProductionBuild) {\n    Log.error(message)\n  } else {\n    throw new Error(message)\n  }\n}\n\ntype GetPageStaticInfoParams = {\n  pageFilePath: string\n  nextConfig: Partial<NextConfig>\n  isDev?: boolean\n  page: string\n  pageType: PAGE_TYPES\n}\n\nexport async function getAppPageStaticInfo({\n  pageFilePath,\n  nextConfig,\n  isDev,\n  page,\n}: GetPageStaticInfoParams): Promise<AppPageStaticInfo> {\n  const content = await tryToReadFile(pageFilePath, !isDev)\n  if (!content || !PARSE_PATTERN.test(content)) {\n    return {\n      type: PAGE_TYPES.APP,\n      config: undefined,\n      runtime: undefined,\n      preferredRegion: undefined,\n      maxDuration: undefined,\n    }\n  }\n\n  const ast = await parseModule(pageFilePath, content)\n\n  const {\n    generateStaticParams,\n    generateImageMetadata,\n    generateSitemaps,\n    exports,\n    directives,\n  } = checkExports(ast, AppSegmentConfigSchemaKeys, page)\n\n  const { type: rsc } = getRSCModuleInformation(content, true)\n\n  const exportedConfig: Record<string, unknown> = {}\n  if (exports) {\n    for (const property of exports) {\n      try {\n        exportedConfig[property] = extractExportedConstValue(ast, property)\n      } catch (e) {\n        if (e instanceof UnsupportedValueError) {\n          warnAboutUnsupportedValue(pageFilePath, page, e)\n        }\n      }\n    }\n  }\n\n  try {\n    exportedConfig.config = extractExportedConstValue(ast, 'config')\n  } catch (e) {\n    if (e instanceof UnsupportedValueError) {\n      warnAboutUnsupportedValue(pageFilePath, page, e)\n    }\n    // `export config` doesn't exist, or other unknown error thrown by swc, silence them\n  }\n\n  const route = normalizeAppPath(page)\n  const config = parseAppSegmentConfig(exportedConfig, route)\n\n  // Prevent edge runtime and generateStaticParams in the same file.\n  if (isEdgeRuntime(config.runtime) && generateStaticParams) {\n    throw new Error(\n      `Page \"${page}\" cannot use both \\`export const runtime = 'edge'\\` and export \\`generateStaticParams\\`.`\n    )\n  }\n\n  // Prevent use client and generateStaticParams in the same file.\n  if (directives?.has('client') && generateStaticParams) {\n    throw new Error(\n      `Page \"${page}\" cannot use both \"use client\" and export function \"generateStaticParams()\".`\n    )\n  }\n\n  return {\n    type: PAGE_TYPES.APP,\n    rsc,\n    generateImageMetadata,\n    generateSitemaps,\n    generateStaticParams,\n    config,\n    middleware: parseMiddlewareConfig(page, exportedConfig.config, nextConfig),\n    runtime: config.runtime,\n    preferredRegion: config.preferredRegion,\n    maxDuration: config.maxDuration,\n  }\n}\n\nexport async function getPagesPageStaticInfo({\n  pageFilePath,\n  nextConfig,\n  isDev,\n  page,\n}: GetPageStaticInfoParams): Promise<PagesPageStaticInfo> {\n  const content = await tryToReadFile(pageFilePath, !isDev)\n  if (!content || !PARSE_PATTERN.test(content)) {\n    return {\n      type: PAGE_TYPES.PAGES,\n      config: undefined,\n      runtime: undefined,\n      preferredRegion: undefined,\n      maxDuration: undefined,\n    }\n  }\n\n  const ast = await parseModule(pageFilePath, content)\n\n  const { getServerSideProps, getStaticProps, exports } = checkExports(\n    ast,\n    PagesSegmentConfigSchemaKeys,\n    page\n  )\n\n  const { type: rsc } = getRSCModuleInformation(content, true)\n\n  const exportedConfig: Record<string, unknown> = {}\n  if (exports) {\n    for (const property of exports) {\n      try {\n        exportedConfig[property] = extractExportedConstValue(ast, property)\n      } catch (e) {\n        if (e instanceof UnsupportedValueError) {\n          warnAboutUnsupportedValue(pageFilePath, page, e)\n        }\n      }\n    }\n  }\n\n  try {\n    exportedConfig.config = extractExportedConstValue(ast, 'config')\n  } catch (e) {\n    if (e instanceof UnsupportedValueError) {\n      warnAboutUnsupportedValue(pageFilePath, page, e)\n    }\n    // `export config` doesn't exist, or other unknown error thrown by swc, silence them\n  }\n\n  // Validate the config.\n  const route = normalizePagePath(page)\n  const config = parsePagesSegmentConfig(exportedConfig, route)\n  const isAnAPIRoute = isAPIRoute(route)\n\n  const resolvedRuntime = config.runtime ?? config.config?.runtime\n\n  if (resolvedRuntime === SERVER_RUNTIME.experimentalEdge) {\n    warnAboutExperimentalEdge(isAnAPIRoute ? page! : null)\n  }\n\n  if (resolvedRuntime === SERVER_RUNTIME.edge && page && !isAnAPIRoute) {\n    const message = `Page ${page} provided runtime 'edge', the edge runtime for rendering is currently experimental. Use runtime 'experimental-edge' instead.`\n    if (isDev) {\n      Log.error(message)\n    } else {\n      throw new Error(message)\n    }\n  }\n\n  return {\n    type: PAGE_TYPES.PAGES,\n    getStaticProps,\n    getServerSideProps,\n    rsc,\n    config,\n    middleware: parseMiddlewareConfig(page, exportedConfig.config, nextConfig),\n    runtime: resolvedRuntime,\n    preferredRegion: config.config?.regions,\n    maxDuration: config.maxDuration ?? config.config?.maxDuration,\n  }\n}\n\n/**\n * For a given pageFilePath and nextConfig, if the config supports it, this\n * function will read the file and return the runtime that should be used.\n * It will look into the file content only if the page *requires* a runtime\n * to be specified, that is, when gSSP or gSP is used.\n * Related discussion: https://github.com/vercel/next.js/discussions/34179\n */\nexport async function getPageStaticInfo(\n  params: GetPageStaticInfoParams\n): Promise<PageStaticInfo> {\n  if (params.pageType === PAGE_TYPES.APP) {\n    return getAppPageStaticInfo(params)\n  }\n\n  return getPagesPageStaticInfo(params)\n}\n"], "names": ["getAppPageStaticInfo", "getMiddlewareMatchers", "getPageStaticInfo", "getPagesPageStaticInfo", "getRSCModuleInformation", "hadUnsupportedValue", "PARSE_PATTERN", "CLIENT_MODULE_LABEL", "ACTION_MODULE_LABEL", "CLIENT_DIRECTIVE", "SERVER_ACTION_DIRECTIVE", "source", "isReactServerLayer", "actions<PERSON>son", "match", "parsedActionsMeta", "JSON", "parse", "undefined", "clientInfoMatch", "isClientRef", "type", "RSC_MODULE_TYPES", "client", "actionIds", "clientRefsString", "clientRefs", "split", "clientEntryType", "server", "checkExports", "ast", "expectedExports", "page", "exportsSet", "Set", "Array", "isArray", "body", "getStaticProps", "getServerSideProps", "generateImageMetadata", "generateSitemaps", "generateStaticParams", "exports", "directives", "hasLeadingNonDirectiveNode", "node", "expression", "directive", "value", "add", "declaration", "declarations", "includes", "id", "has", "identifier", "specifier", "specifiers", "orig", "Log", "warn", "tryToReadFile", "filePath", "shouldThrow", "fs", "readFile", "encoding", "error", "message", "matcherOrMatchers", "nextConfig", "matchers", "i18n", "map", "matcher", "originalSource", "rest", "isRoot", "locales", "locale", "basePath", "result", "SourceSchema", "safeParse", "success", "reportZodError", "process", "exit", "regexp", "tryToParsePath", "data", "regexStr", "parseMiddlewareConfig", "rawConfig", "input", "MiddlewareConfigInputSchema", "config", "unstable_allowDynamic", "regions", "apiRouteWarnings", "L<PERSON><PERSON><PERSON>", "warnAboutExperimentalEdge", "apiRoute", "env", "NODE_ENV", "NEXT_PRIVATE_BUILD_WORKER", "set", "warnedUnsupportedValueMap", "warnAboutUnsupportedValue", "pageFilePath", "isProductionBuild", "NEXT_COMPILER_NAME", "path", "Error", "isDev", "content", "test", "PAGE_TYPES", "APP", "runtime", "preferredRegion", "maxDuration", "parseModule", "AppSegmentConfigSchemaKeys", "rsc", "exportedConfig", "property", "extractExportedConstValue", "e", "UnsupportedValueError", "route", "normalizeAppPath", "parseAppSegmentConfig", "isEdgeRuntime", "middleware", "PAGES", "PagesSegmentConfigSchemaKeys", "normalizePagePath", "parsePagesSegmentConfig", "isAnAPIRoute", "isAPIRoute", "resolvedRuntime", "SERVER_RUNTIME", "experimentalEdge", "edge", "params", "pageType"], "mappings": ";;;;;;;;;;;;;;;;;;;IA2dsBA,oBAAoB;eAApBA;;IAnKNC,qBAAqB;eAArBA;;IA8UMC,iBAAiB;eAAjBA;;IAzFAC,sBAAsB;eAAtBA;;IA3bNC,uBAAuB;eAAvBA;;IA0TLC,mBAAmB;eAAnBA;;;oBAzaoB;0BACN;mCAIlB;6BACqB;6DACP;2BACU;gCACA;4BACJ;+BACG;4BACG;2BAEN;kCAKpB;qBACwB;oCAMxB;kCAKA;0BAC0B;mCACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,MAAMC,gBACJ;AAiEF,MAAMC,sBACJ;AAEF,MAAMC,sBACJ;AAEF,MAAMC,mBAAmB;AACzB,MAAMC,0BAA0B;AAGzB,SAASN,wBACdO,MAAc,EACdC,kBAA2B;IAE3B,MAAMC,cAAcF,OAAOG,KAAK,CAACN;IACjC,MAAMO,oBAAoBF,cACrBG,KAAKC,KAAK,CAACJ,WAAW,CAAC,EAAE,IAC1BK;IACJ,MAAMC,kBAAkBR,OAAOG,KAAK,CAACP;IACrC,MAAMa,cAAc,CAAC,CAACD;IAEtB,IAAI,CAACP,oBAAoB;QACvB,OAAO;YACLS,MAAMC,4BAAgB,CAACC,MAAM;YAC7BC,WAAWT;YACXK;QACF;IACF;IAEA,MAAMK,mBAAmBN,mCAAAA,eAAiB,CAAC,EAAE;IAC7C,MAAMO,aAAaD,mBAAmBA,iBAAiBE,KAAK,CAAC,OAAO,EAAE;IACtE,MAAMC,kBAAkBT,mCAAAA,eAAiB,CAAC,EAAE;IAE5C,MAAME,OAAOF,kBACTG,4BAAgB,CAACC,MAAM,GACvBD,4BAAgB,CAACO,MAAM;IAE3B,OAAO;QACLR;QACAG,WAAWT;QACXW;QACAE;QACAR;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAASU,aACPC,GAAQ,EACRC,eAAyB,EACzBC,IAAY;IAUZ,MAAMC,aAAa,IAAIC,IAAY;QACjC;QACA;QACA;QACA;QACA;KACD;IACD,IAAI,CAACC,MAAMC,OAAO,CAACN,uBAAAA,IAAKO,IAAI,GAAG;QAC7B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,IAAIC,iBAA0B;QAC9B,IAAIC,qBAA8B;QAClC,IAAIC,wBAAiC;QACrC,IAAIC,mBAA4B;QAChC,IAAIC,uBAAuB;QAC3B,IAAIC,WAAU,IAAIT;QAClB,IAAIU,aAAa,IAAIV;QACrB,IAAIW,6BAA6B;QAEjC,KAAK,MAAMC,QAAQhB,IAAIO,IAAI,CAAE;gBAoBzBS,mBAWAA,oBACeA,8BAYfA;YA3CF,iEAAiE;YACjE,IACEA,KAAK1B,IAAI,KAAK,yBACd0B,KAAKC,UAAU,CAAC3B,IAAI,KAAK,iBACzB;gBACA,IAAI,CAACyB,4BAA4B;oBAC/B,MAAMG,YAAYF,KAAKC,UAAU,CAACE,KAAK;oBACvC,IAAIzC,qBAAqBwC,WAAW;wBAClCJ,WAAWM,GAAG,CAAC;oBACjB;oBACA,IAAIzC,4BAA4BuC,WAAW;wBACzCJ,WAAWM,GAAG,CAAC;oBACjB;gBACF;YACF,OAAO;gBACLL,6BAA6B;YAC/B;YACA,IACEC,KAAK1B,IAAI,KAAK,uBACd0B,EAAAA,oBAAAA,KAAKK,WAAW,qBAAhBL,kBAAkB1B,IAAI,MAAK,uBAC3B;oBAC0B0B;gBAA1B,KAAK,MAAMK,gBAAeL,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkBM,YAAY,CAAE;oBACxD,IAAIrB,gBAAgBsB,QAAQ,CAACF,YAAYG,EAAE,CAACL,KAAK,GAAG;wBAClDN,SAAQO,GAAG,CAACC,YAAYG,EAAE,CAACL,KAAK;oBAClC;gBACF;YACF;YAEA,IACEH,KAAK1B,IAAI,KAAK,uBACd0B,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkB1B,IAAI,MAAK,yBAC3Ba,WAAWsB,GAAG,EAACT,+BAAAA,KAAKK,WAAW,CAACK,UAAU,qBAA3BV,6BAA6BG,KAAK,GACjD;gBACA,MAAMK,KAAKR,KAAKK,WAAW,CAACK,UAAU,CAACP,KAAK;gBAC5CV,qBAAqBe,OAAO;gBAC5BhB,iBAAiBgB,OAAO;gBACxBd,wBAAwBc,OAAO;gBAC/Bb,mBAAmBa,OAAO;gBAC1BZ,uBAAuBY,OAAO;YAChC;YAEA,IACER,KAAK1B,IAAI,KAAK,uBACd0B,EAAAA,qBAAAA,KAAKK,WAAW,qBAAhBL,mBAAkB1B,IAAI,MAAK,uBAC3B;oBACW0B,iCAAAA;gBAAX,MAAMQ,MAAKR,qBAAAA,KAAKK,WAAW,sBAAhBL,kCAAAA,mBAAkBM,YAAY,CAAC,EAAE,qBAAjCN,gCAAmCQ,EAAE,CAACL,KAAK;gBACtD,IAAIhB,WAAWsB,GAAG,CAACD,KAAK;oBACtBf,qBAAqBe,OAAO;oBAC5BhB,iBAAiBgB,OAAO;oBACxBd,wBAAwBc,OAAO;oBAC/Bb,mBAAmBa,OAAO;oBAC1BZ,uBAAuBY,OAAO;gBAChC;YACF;YAEA,IAAIR,KAAK1B,IAAI,KAAK,0BAA0B;gBAC1C,KAAK,MAAMqC,aAAaX,KAAKY,UAAU,CAAE;wBAGrCD;oBAFF,IACEA,UAAUrC,IAAI,KAAK,qBACnBqC,EAAAA,kBAAAA,UAAUE,IAAI,qBAAdF,gBAAgBrC,IAAI,MAAK,cACzB;wBACA,MAAM6B,QAAQQ,UAAUE,IAAI,CAACV,KAAK;wBAElC,IAAI,CAACV,sBAAsBU,UAAU,sBAAsB;4BACzDV,qBAAqB;wBACvB;wBACA,IAAI,CAACD,kBAAkBW,UAAU,kBAAkB;4BACjDX,iBAAiB;wBACnB;wBACA,IAAI,CAACE,yBAAyBS,UAAU,yBAAyB;4BAC/DT,wBAAwB;wBAC1B;wBACA,IAAI,CAACC,oBAAoBQ,UAAU,oBAAoB;4BACrDR,mBAAmB;wBACrB;wBACA,IAAI,CAACC,wBAAwBO,UAAU,wBAAwB;4BAC7DP,uBAAuB;wBACzB;wBACA,IAAIX,gBAAgBsB,QAAQ,CAACJ,UAAU,CAACN,SAAQY,GAAG,CAACN,QAAQ;4BAC1D,+DAA+D;4BAC/D,sCAAsC;4BACtCW,KAAIC,IAAI,CACN,CAAC,uCAAuC,EAAEZ,MAAM,aAAa,EAAEjB,KAAK,oFAAoF,CAAC;wBAE7J;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACLM;YACAC;YACAC;YACAC;YACAC;YACAE;YACAD,SAAAA;QACF;IACF,EAAE,OAAM,CAAC;IAET,OAAO,CAAC;AACV;AAEA,eAAemB,cAAcC,QAAgB,EAAEC,WAAoB;IACjE,IAAI;QACF,OAAO,MAAMC,YAAE,CAACC,QAAQ,CAACH,UAAU;YACjCI,UAAU;QACZ;IACF,EAAE,OAAOC,OAAY;QACnB,IAAIJ,aAAa;YACfI,MAAMC,OAAO,GAAG,CAAC,mCAAmC,EAAEN,SAAS,GAAG,EAAEK,MAAMC,OAAO,EAAE;YACnF,MAAMD;QACR;IACF;AACF;AAKO,SAASpE,sBACdsE,iBAA+C,EAC/CC,UAAiD;IAEjD,MAAMC,WAAWrC,MAAMC,OAAO,CAACkC,qBAC3BA,oBACA;QAACA;KAAkB;IAEvB,MAAM,EAAEG,IAAI,EAAE,GAAGF;IAEjB,OAAOC,SAASE,GAAG,CAAC,CAACC;QACnBA,UAAU,OAAOA,YAAY,WAAW;YAAEjE,QAAQiE;QAAQ,IAAIA;QAE9D,MAAMC,iBAAiBD,QAAQjE,MAAM;QAErC,IAAI,EAAEA,MAAM,EAAE,GAAGmE,MAAM,GAAGF;QAE1B,MAAMG,SAASpE,WAAW;QAE1B,IAAI+D,CAAAA,wBAAAA,KAAMM,OAAO,KAAIJ,QAAQK,MAAM,KAAK,OAAO;YAC7CtE,SAAS,CAAC,yCAAyC,EACjDoE,SAAS,KAAKpE,QACd;QACJ;QAEAA,SAAS,CAAC,gCAAgC,EAAEA,SAC1CoE,SACI,CAAC,CAAC,EAAEP,WAAWE,IAAI,GAAG,cAAc,GAAG,wBAAwB,CAAC,GAChE,gBACJ;QAEF,IAAIF,WAAWU,QAAQ,EAAE;YACvBvE,SAAS,GAAG6D,WAAWU,QAAQ,GAAGvE,QAAQ;QAC5C;QAEA,qCAAqC;QACrC,MAAMwE,SAASC,8BAAY,CAACC,SAAS,CAAC1E;QACtC,IAAI,CAACwE,OAAOG,OAAO,EAAE;YACnBC,IAAAA,mBAAc,EAAC,qCAAqCJ,OAAOd,KAAK;YAEhE,uEAAuE;YACvE,uEAAuE;YACvE,sBAAsB;YACtBmB,QAAQC,IAAI,CAAC;QACf;QAEA,OAAO;YACL,GAAGX,IAAI;YACP,mEAAmE;YACnE,oCAAoC;YACpCY,QAAQC,IAAAA,8BAAc,EAACR,OAAOS,IAAI,EAAEC,QAAQ;YAC5ChB,gBAAgBA,kBAAkBlE;QACpC;IACF;AACF;AAEA,SAASmF,sBACP7D,IAAY,EACZ8D,SAAkB,EAClBvB,UAAsB;IAEtB,sDAAsD;IACtD,IAAI,OAAOuB,cAAc,YAAY,CAACA,WAAW,OAAO,CAAC;IAEzD,MAAMC,QAAQC,6CAA2B,CAACZ,SAAS,CAACU;IACpD,IAAI,CAACC,MAAMV,OAAO,EAAE;QAClBC,IAAAA,mBAAc,EAAC,GAAGtD,KAAK,mCAAmC,CAAC,EAAE+D,MAAM3B,KAAK;QAExE,uEAAuE;QACvE,uEAAuE;QACvE,sBAAsB;QACtBmB,QAAQC,IAAI,CAAC;IACf;IAEA,MAAMS,SAA2B,CAAC;IAElC,IAAIF,MAAMJ,IAAI,CAAChB,OAAO,EAAE;QACtBsB,OAAOzB,QAAQ,GAAGxE,sBAAsB+F,MAAMJ,IAAI,CAAChB,OAAO,EAAEJ;IAC9D;IAEA,IAAIwB,MAAMJ,IAAI,CAACO,qBAAqB,EAAE;QACpCD,OAAOC,qBAAqB,GAAG/D,MAAMC,OAAO,CAC1C2D,MAAMJ,IAAI,CAACO,qBAAqB,IAE9BH,MAAMJ,IAAI,CAACO,qBAAqB,GAChC;YAACH,MAAMJ,IAAI,CAACO,qBAAqB;SAAC;IACxC;IAEA,IAAIH,MAAMJ,IAAI,CAACQ,OAAO,EAAE;QACtBF,OAAOE,OAAO,GAAGJ,MAAMJ,IAAI,CAACQ,OAAO;IACrC;IAEA,OAAOF;AACT;AAEA,MAAMG,mBAAmB,IAAIC,kBAAQ,CAAC;AACtC,SAASC,0BAA0BC,QAAuB;IACxD,IACEhB,QAAQiB,GAAG,CAACC,QAAQ,KAAK,gBACzBlB,QAAQiB,GAAG,CAACE,yBAAyB,KAAK,KAC1C;QACA;IACF;IAEA,IAAIN,iBAAiB7C,GAAG,CAACgD,WAAW;QAClC;IACF;IAEA3C,KAAIC,IAAI,CACN0C,WACI,GAAGA,SAAS,2EAA2E,CAAC,GACxF,CAAC,iEAAiE,CAAC;IAEzEH,iBAAiBO,GAAG,CAACJ,UAAU;AACjC;AAEO,IAAInG,sBAAsB;AACjC,MAAMwG,4BAA4B,IAAIP,kBAAQ,CAAU,KAAK,IAAM;AAEnE,SAASQ,0BACPC,YAAoB,EACpB9E,IAAwB,EACxBoC,KAA4B;IAE5BhE,sBAAsB;IACtB,MAAM2G,oBAAoBxB,QAAQiB,GAAG,CAACC,QAAQ,KAAK;IACnD,IACE,qDAAqD;IACrD,sDAAsD;IACtD,iCAAiC;IACjClB,QAAQiB,GAAG,CAACQ,kBAAkB,KAAK,YAClCD,qBAAqBH,0BAA0BrD,GAAG,CAACuD,eACpD;QACA;IACF;IACAF,0BAA0BD,GAAG,CAACG,cAAc;IAE5C,MAAMzC,UACJ,CAAC,yDAAyD,CAAC,GAC1DrC,CAAAA,OAAO,CAAC,OAAO,EAAEA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE8E,aAAa,CAAC,CAAC,AAAD,IAC9C,QACA1C,MAAMC,OAAO,GACZD,CAAAA,MAAM6C,IAAI,GAAG,CAAC,KAAK,EAAE7C,MAAM6C,IAAI,CAAC,CAAC,CAAC,GAAG,EAAC,IACvC,QACA;IAEF,qDAAqD;IACrD,8DAA8D;IAC9D,IAAIF,mBAAmB;QACrBnD,KAAIQ,KAAK,CAACC;IACZ,OAAO;QACL,MAAM,qBAAkB,CAAlB,IAAI6C,MAAM7C,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;AACF;AAUO,eAAetE,qBAAqB,EACzC+G,YAAY,EACZvC,UAAU,EACV4C,KAAK,EACLnF,IAAI,EACoB;IACxB,MAAMoF,UAAU,MAAMtD,cAAcgD,cAAc,CAACK;IACnD,IAAI,CAACC,WAAW,CAAC/G,cAAcgH,IAAI,CAACD,UAAU;QAC5C,OAAO;YACLhG,MAAMkG,qBAAU,CAACC,GAAG;YACpBtB,QAAQhF;YACRuG,SAASvG;YACTwG,iBAAiBxG;YACjByG,aAAazG;QACf;IACF;IAEA,MAAMa,MAAM,MAAM6F,IAAAA,wBAAW,EAACb,cAAcM;IAE5C,MAAM,EACJ1E,oBAAoB,EACpBF,qBAAqB,EACrBC,gBAAgB,EAChBE,SAAAA,QAAO,EACPC,UAAU,EACX,GAAGf,aAAaC,KAAK8F,4CAA0B,EAAE5F;IAElD,MAAM,EAAEZ,MAAMyG,GAAG,EAAE,GAAG1H,wBAAwBiH,SAAS;IAEvD,MAAMU,iBAA0C,CAAC;IACjD,IAAInF,UAAS;QACX,KAAK,MAAMoF,YAAYpF,SAAS;YAC9B,IAAI;gBACFmF,cAAc,CAACC,SAAS,GAAGC,IAAAA,4CAAyB,EAAClG,KAAKiG;YAC5D,EAAE,OAAOE,GAAG;gBACV,IAAIA,aAAaC,wCAAqB,EAAE;oBACtCrB,0BAA0BC,cAAc9E,MAAMiG;gBAChD;YACF;QACF;IACF;IAEA,IAAI;QACFH,eAAe7B,MAAM,GAAG+B,IAAAA,4CAAyB,EAAClG,KAAK;IACzD,EAAE,OAAOmG,GAAG;QACV,IAAIA,aAAaC,wCAAqB,EAAE;YACtCrB,0BAA0BC,cAAc9E,MAAMiG;QAChD;IACA,oFAAoF;IACtF;IAEA,MAAME,QAAQC,IAAAA,0BAAgB,EAACpG;IAC/B,MAAMiE,SAASoC,IAAAA,uCAAqB,EAACP,gBAAgBK;IAErD,kEAAkE;IAClE,IAAIG,IAAAA,4BAAa,EAACrC,OAAOuB,OAAO,KAAK9E,sBAAsB;QACzD,MAAM,qBAEL,CAFK,IAAIwE,MACR,CAAC,MAAM,EAAElF,KAAK,wFAAwF,CAAC,GADnG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,gEAAgE;IAChE,IAAIY,CAAAA,8BAAAA,WAAYW,GAAG,CAAC,cAAab,sBAAsB;QACrD,MAAM,qBAEL,CAFK,IAAIwE,MACR,CAAC,MAAM,EAAElF,KAAK,4EAA4E,CAAC,GADvF,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAO;QACLZ,MAAMkG,qBAAU,CAACC,GAAG;QACpBM;QACArF;QACAC;QACAC;QACAuD;QACAsC,YAAY1C,sBAAsB7D,MAAM8F,eAAe7B,MAAM,EAAE1B;QAC/DiD,SAASvB,OAAOuB,OAAO;QACvBC,iBAAiBxB,OAAOwB,eAAe;QACvCC,aAAazB,OAAOyB,WAAW;IACjC;AACF;AAEO,eAAexH,uBAAuB,EAC3C4G,YAAY,EACZvC,UAAU,EACV4C,KAAK,EACLnF,IAAI,EACoB;QAiDkBiE,gBAuBvBA,iBACkBA;IAxErC,MAAMmB,UAAU,MAAMtD,cAAcgD,cAAc,CAACK;IACnD,IAAI,CAACC,WAAW,CAAC/G,cAAcgH,IAAI,CAACD,UAAU;QAC5C,OAAO;YACLhG,MAAMkG,qBAAU,CAACkB,KAAK;YACtBvC,QAAQhF;YACRuG,SAASvG;YACTwG,iBAAiBxG;YACjByG,aAAazG;QACf;IACF;IAEA,MAAMa,MAAM,MAAM6F,IAAAA,wBAAW,EAACb,cAAcM;IAE5C,MAAM,EAAE7E,kBAAkB,EAAED,cAAc,EAAEK,SAAAA,QAAO,EAAE,GAAGd,aACtDC,KACA2G,gDAA4B,EAC5BzG;IAGF,MAAM,EAAEZ,MAAMyG,GAAG,EAAE,GAAG1H,wBAAwBiH,SAAS;IAEvD,MAAMU,iBAA0C,CAAC;IACjD,IAAInF,UAAS;QACX,KAAK,MAAMoF,YAAYpF,SAAS;YAC9B,IAAI;gBACFmF,cAAc,CAACC,SAAS,GAAGC,IAAAA,4CAAyB,EAAClG,KAAKiG;YAC5D,EAAE,OAAOE,GAAG;gBACV,IAAIA,aAAaC,wCAAqB,EAAE;oBACtCrB,0BAA0BC,cAAc9E,MAAMiG;gBAChD;YACF;QACF;IACF;IAEA,IAAI;QACFH,eAAe7B,MAAM,GAAG+B,IAAAA,4CAAyB,EAAClG,KAAK;IACzD,EAAE,OAAOmG,GAAG;QACV,IAAIA,aAAaC,wCAAqB,EAAE;YACtCrB,0BAA0BC,cAAc9E,MAAMiG;QAChD;IACA,oFAAoF;IACtF;IAEA,uBAAuB;IACvB,MAAME,QAAQO,IAAAA,oCAAiB,EAAC1G;IAChC,MAAMiE,SAAS0C,IAAAA,2CAAuB,EAACb,gBAAgBK;IACvD,MAAMS,eAAeC,IAAAA,sBAAU,EAACV;IAEhC,MAAMW,kBAAkB7C,OAAOuB,OAAO,MAAIvB,iBAAAA,OAAOA,MAAM,qBAAbA,eAAeuB,OAAO;IAEhE,IAAIsB,oBAAoBC,yBAAc,CAACC,gBAAgB,EAAE;QACvD1C,0BAA0BsC,eAAe5G,OAAQ;IACnD;IAEA,IAAI8G,oBAAoBC,yBAAc,CAACE,IAAI,IAAIjH,QAAQ,CAAC4G,cAAc;QACpE,MAAMvE,UAAU,CAAC,KAAK,EAAErC,KAAK,4HAA4H,CAAC;QAC1J,IAAImF,OAAO;YACTvD,KAAIQ,KAAK,CAACC;QACZ,OAAO;YACL,MAAM,qBAAkB,CAAlB,IAAI6C,MAAM7C,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IAEA,OAAO;QACLjD,MAAMkG,qBAAU,CAACkB,KAAK;QACtBlG;QACAC;QACAsF;QACA5B;QACAsC,YAAY1C,sBAAsB7D,MAAM8F,eAAe7B,MAAM,EAAE1B;QAC/DiD,SAASsB;QACTrB,eAAe,GAAExB,kBAAAA,OAAOA,MAAM,qBAAbA,gBAAeE,OAAO;QACvCuB,aAAazB,OAAOyB,WAAW,MAAIzB,kBAAAA,OAAOA,MAAM,qBAAbA,gBAAeyB,WAAW;IAC/D;AACF;AASO,eAAezH,kBACpBiJ,MAA+B;IAE/B,IAAIA,OAAOC,QAAQ,KAAK7B,qBAAU,CAACC,GAAG,EAAE;QACtC,OAAOxH,qBAAqBmJ;IAC9B;IAEA,OAAOhJ,uBAAuBgJ;AAChC"}