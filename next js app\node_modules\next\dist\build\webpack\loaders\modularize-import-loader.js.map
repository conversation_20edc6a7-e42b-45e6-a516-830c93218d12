{"version": 3, "sources": ["../../../../src/build/webpack/loaders/modularize-import-loader.ts"], "sourcesContent": ["import path from 'path'\n\nexport type ModularizeImportLoaderOptions = {\n  name: string\n  join?: string\n  from: 'default' | 'named'\n  as: 'default' | 'named'\n}\n\n/**\n * This loader is to create special re-exports from a specific file.\n * For example, the following loader:\n *\n * modularize-import-loader?name=Arrow&from=Arrow&as=default&join=./icons/Arrow!lucide-react\n *\n * will be used to create a re-export of:\n *\n * export { Arrow as default } from \"join(resolve_path('lucide-react'), '/icons/Arrow')\"\n *\n * This works even if there's no export field in the package.json of the package.\n */\nexport default function transformSource(this: any) {\n  const { name, from, as, join }: ModularizeImportLoaderOptions =\n    this.getOptions()\n  const { resourcePath } = this\n  const fullPath = join\n    ? path.join(path.dirname(resourcePath), join)\n    : resourcePath\n\n  return `\nexport {\n  ${from === 'default' ? 'default' : name} as ${\n    as === 'default' ? 'default' : name\n  }\n} from ${JSON.stringify(fullPath)}\n`\n}\n"], "names": ["transformSource", "name", "from", "as", "join", "getOptions", "resourcePath", "fullPath", "path", "dirname", "JSON", "stringify"], "mappings": ";;;;+BASA;;;;;;;;;;;CAWC,GACD;;;eAAwBA;;;6DArBP;;;;;;AAqBF,SAASA;IACtB,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAC5B,IAAI,CAACC,UAAU;IACjB,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAI;IAC7B,MAAMC,WAAWH,OACbI,aAAI,CAACJ,IAAI,CAACI,aAAI,CAACC,OAAO,CAACH,eAAeF,QACtCE;IAEJ,OAAO,CAAC;;EAER,EAAEJ,SAAS,YAAY,YAAYD,KAAK,IAAI,EAC1CE,OAAO,YAAY,YAAYF,KAChC;OACI,EAAES,KAAKC,SAAS,CAACJ,UAAU;AAClC,CAAC;AACD"}