(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[497],{1224:(e,s,t)=>{Promise.resolve().then(t.bind(t,2871))},2871:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),n=t(2115),a=t(5695),i=t(9053),c=t(7389),l=t(2799),o=t(5731),d=t(3213);let m={company1:{key:"company1",name:"Shans Accessories PTY LTD",bankingInformation:"\n      First National Bank<br>\n      Account :  ***********<br>\n      Branch code 257705<br>\n      Swift code FIRNZAJJ\n    "},company2:{key:"company2",name:"Shans Autosport PTY LTD",bankingInformation:"\n      Business Account<br>\n      Capitec Current Account<br>\n      Account: **********\n    "},company3:{key:"company3",name:"Shans Motorstyle PTY LTD",bankingInformation:"\n      SHANS MOTORSTYLE (PTY) LTD<br>\n      Gold Business Account<br>\n      Account Number: ***********<br>\n      Branch Code: 250655<br>\n      Swift Code: FIRNZAJJ\n    "}};function x(){var e;let s=(0,a.useRouter)(),{messages:t,showToast:x,removeToast:h}=(0,c.d)(),[u,p]=(0,n.useState)(null),[g,b]=(0,n.useState)(!0),[j,y]=(0,n.useState)(""),{mutate:N,loading:f,error:v}=(0,d.L2)(o.FH.receipts.create),I=e=>"".concat("R").concat(parseFloat(e.toString()).toLocaleString("en-ZA",{minimumFractionDigits:0,maximumFractionDigits:0})),T=()=>Math.floor(1e6+9e6*Math.random()).toString(),_=()=>{try{let e=l.IG.get("customerInfo"),t=l.IG.get("selectedProducts"),r=l.IG.get("paymentMethod"),n=l.IG.get("comments"),a=l.IG.get("salespersonName"),i=l.IG.get("selectedCompany"),c=l.IG.get("includeTax");if(!e||!t){x("No order data found. Please create an order first.","error"),s.push("/");return}let o=JSON.parse(e),d=JSON.parse(t),m=JSON.parse(i||"{}").key||"company1";p({customerInfo:o,selectedProducts:d,paymentMethod:r||"Cash",comments:n||"",salespersonName:a||"",companyKey:m,includeTax:"true"===c}),y(T())}catch(e){console.error("Error loading order data:",e),x("Error loading order data","error"),s.push("/")}finally{b(!1)}},S=()=>{if(!u)return{subtotal:0,tax:0,total:0};let e=0,s=0,t=0;return u.selectedProducts.forEach(r=>{let n=r.price*r.quantity;if(u.includeTax){let a=r.price/1.15,i=r.price-a;e+=a*r.quantity,s+=i*r.quantity,t+=n}else e+=n,t+=n}),{subtotal:Math.round(e),tax:Math.round(s),total:Math.round(t)}},w=async()=>{if(u)try{let e=S(),t=m[u.companyKey],r={reference_number:j,company:{name:t.name,bankingInformation:t.bankingInformation},billing:u.customerInfo.billing,shipping:u.customerInfo.shipping,items:u.selectedProducts.map(e=>({item_code:e.item_code,item_name:e.name,room_name:e.room_name,quantity:e.quantity,unit_price_including_tax:u.includeTax?e.price:1.15*e.price,unit_price_excluding_tax:u.includeTax?e.price/1.15:e.price,total_price:e.price*e.quantity,tax_per_product:u.includeTax?e.price-e.price/1.15:.15*e.price})),subtotal:e.subtotal,tax:e.tax,total:e.total,payment_method:u.paymentMethod,comments:u.comments,salesperson_name:u.salespersonName,include_tax:u.includeTax};await N(r)&&(x("Receipt created successfully!","success"),l.IG.remove("customerInfo"),l.IG.remove("selectedProducts"),l.IG.remove("paymentMethod"),l.IG.remove("comments"),l.IG.remove("salespersonName"),l.IG.remove("includeTax"),setTimeout(()=>{s.push("/")},2e3))}catch(e){console.error("Error creating receipt:",e),x("Error: ".concat(e instanceof Error?e.message:"Failed to create receipt"),"error")}};if((0,n.useEffect)(()=>{_()},[]),(0,n.useEffect)(()=>{v&&x("Error: ".concat(v),"error")},[v,x]),g)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading receipt..."})]})})});if(!u)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"No order data found"}),(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Go to Home"})]})})});let k=S(),E=m[u.companyKey];return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100 p-5",children:[(0,r.jsx)(c.A,{messages:t,onRemove:h}),(0,r.jsxs)("div",{className:"flex gap-5 mb-5",children:[(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Home"}),(0,r.jsx)("button",{onClick:()=>{sessionStorage.setItem("editingOrder","true"),s.push("/")},className:"text-blue-600 hover:text-blue-800 transition-colors",children:"✏️ Edit Order"})]}),(0,r.jsxs)("div",{id:"pdf-1",className:"max-w-4xl mx-auto bg-white p-8 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-green-600 mb-2",children:"RECEIPT"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Reference: ",j]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Date: ",(e=new Date,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e))]}),(0,r.jsx)("p",{className:"text-green-600 font-semibold",children:"PAYMENT RECEIVED"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:E.name}),(0,r.jsx)("div",{className:"text-sm text-gray-600",dangerouslySetInnerHTML:{__html:E.bankingInformation}})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"BILL TO"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[(0,r.jsx)("p",{children:u.customerInfo.billing.name}),(0,r.jsx)("p",{children:u.customerInfo.billing.email}),(0,r.jsx)("p",{children:u.customerInfo.billing.address}),(0,r.jsx)("p",{children:u.customerInfo.billing.phone})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"SHIP TO"}),(0,r.jsx)("div",{className:"text-gray-600",children:u.customerInfo.shipping?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:u.customerInfo.shipping.name}),(0,r.jsx)("p",{children:u.customerInfo.shipping.email}),(0,r.jsx)("p",{children:u.customerInfo.shipping.address}),(0,r.jsx)("p",{children:u.customerInfo.shipping.phone})]}):(0,r.jsx)("p",{children:"Same as billing address"})})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"border border-gray-300",children:[(0,r.jsxs)("div",{className:"bg-green-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300",children:[(0,r.jsx)("div",{className:"col-span-4",children:"DESCRIPTION"}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:"QTY"}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:"UNIT PRICE"}),u.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:"TAX"}),(0,r.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right"),children:"TOTAL"})]}),u.selectedProducts.map((e,s)=>{let t=e.price*e.quantity,n=u.includeTax?e.price/1.15:e.price,a=u.includeTax?e.price-n:0;return(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0",children:[(0,r.jsxs)("div",{className:"col-span-4",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Room: ",e.room_name]})]}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:e.quantity}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:I(u.includeTax?n:e.price)}),u.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:I(a)}),(0,r.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right font-medium"),children:I(t)})]},s)})]})}),(0,r.jsx)("div",{className:"flex justify-end mb-8",children:(0,r.jsxs)("div",{className:"w-64",children:[(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Subtotal:"}),(0,r.jsx)("span",{children:I(k.subtotal)})]}),u.includeTax&&(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Tax (15%):"}),(0,r.jsx)("span",{children:I(k.tax)})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2 font-bold text-lg bg-green-100 px-2 rounded",children:[(0,r.jsx)("span",{children:"Total Paid:"}),(0,r.jsx)("span",{className:"text-green-600",children:I(k.total)})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Method"}),(0,r.jsx)("p",{className:"text-gray-600",children:u.paymentMethod})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Salesperson"}),(0,r.jsx)("p",{className:"text-gray-600",children:u.salespersonName||"N/A"})]})]}),u.comments&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Comments"}),(0,r.jsx)("p",{className:"text-gray-600",children:u.comments})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:w,disabled:f,className:"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2",children:[f&&(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),f?"Creating...":"Confirm Receipt"]}),(0,r.jsx)("button",{onClick:()=>window.print(),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"\uD83D\uDCF8 Print/Save"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[535,441,684,358],()=>s(1224)),_N_E=e.O()}]);