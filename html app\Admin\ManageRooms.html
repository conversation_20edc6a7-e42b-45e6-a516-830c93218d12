<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Shans Inventory System - Manage Rooms</title>
  <style>
    /* Reset some default browser styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 1200px;
      margin: auto;
    }

    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }

    .room-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .room-block {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
      position: relative;
    }

    .room-block:hover {
      transform: translateY(-5px);
    }

    .add-room {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 48px;
      color: #888;
      cursor: pointer;
      background-color: #e0e0e0;
    }

    .add-room:hover {
      background-color: #d5d5d5;
    }

    .modal {
      display: none; /* Hidden by default */
      position: fixed; /* Stay in place */
      z-index: 1000; /* Sit on top */
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto; /* Enable scroll if needed */
      background-color: rgba(0,0,0,0.5); /* Black w/ opacity */
    }

    .modal-content {
      background-color: #fefefe;
      margin: 10% auto; /* 10% from the top and centered */
      padding: 20px;
      border: 1px solid #888;
      width: 90%;
      max-width: 500px;
      border-radius: 5px;
      position: relative;
    }

    .close {
      color: #aaa;
      position: absolute;
      top: 10px;
      right: 20px;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close:hover,
    .close:focus {
      color: #000;
      text-decoration: none;
    }

    form {
      margin-top: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    input[type="text"],
    input[type="color"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }

    input[type="color"] {
      height: 40px;
    }

    button {
      display: inline-block;
      padding: 10px 20px;
      background: #333;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }

    button:hover {
      background: #555;
    }

    /* Edit and Delete Button Styles */
    .room-block button {
      margin: 5px 5px 0 5px;
      padding: 5px 10px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }

    .edit-button {
      background-color: #4CAF50;
      color: white;
    }

    .edit-button:hover {
      background-color: #45a049;
    }

    .delete-button {
      background-color: #f44336;
      color: white;
    }

    .delete-button:hover {
      background-color: #da190b;
    }

    /* Responsive Design */
    @media (max-width: 600px) {
      .room-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      }
    }

    /* Toast Container and Styles */
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #333;
      color: #fff;
      padding: 10px 20px;
      border-radius: 4px;
      opacity: 1;
      z-index: 9999;
      font-size: 14px;
      animation: fadeInOut 4s forwards; /* Show, then fade out automatically */
    }

    .toast-success {
      background-color: #4CAF50;
    }

    .toast-error {
      background-color: #f44336;
    }

    .toast-info {
      background-color: #2196F3;
    }

    /* Fade in/out animation */
    @keyframes fadeInOut {
      0% {
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }

    /* Confirmation and Verification Modal Styles */
    .confirmation-modal, .verification-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .confirmation-modal-content, .verification-modal-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      max-width: 400px;
      width: 90%;
      text-align: center;
      animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
      from {
        transform: translateY(-50px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .verification-code-input {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 5px;
      font-size: 18px;
      text-align: center;
      letter-spacing: 2px;
      margin-bottom: 20px;
      box-sizing: border-box;
      transition: border-color 0.3s ease;
    }

    .verification-code-input:focus {
      outline: none;
      border-color: #4CAF50;
    }

    .modal-buttons {
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    .modal-button {
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s ease;
    }

    .modal-button.delete {
      background-color: #d32f2f;
      color: white;
    }

    .modal-button.delete:hover {
      background-color: #b71c1c;
    }

    .modal-button.cancel {
      background-color: #666;
      color: white;
    }

    .modal-button.cancel:hover {
      background-color: #555;
    }

    /* Mobile responsive for modals */
    @media (max-width: 600px) {
      .confirmation-modal-content, .verification-modal-content {
        padding: 20px;
        margin: 20px;
        max-width: none;
      }

      .modal-buttons {
        flex-direction: column;
      }

      .modal-button {
        width: 100%;
        margin-bottom: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div>
        <h1 style="margin: 0;">Inventory Rooms</h1>
        <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px;">&larr; Back to Home</a>
      </div>
      <button onclick="logout()" style="background-color: #e74c3c; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">Logout</button>
    </div>
    <div class="room-grid" id="roomGrid">
      <div class="room-block add-room" id="addRoomBlock" aria-label="Add new room" title="Add New Room">
        +
      </div>
    </div>
  </div>

  <!-- Add Room Modal -->
  <div id="addRoomModal" class="modal" aria-hidden="true" role="dialog" aria-labelledby="addRoomModalTitle">
    <div class="modal-content">
      <span class="close" id="closeAddModal" aria-label="Close modal">&times;</span>
      <h2 id="addRoomModalTitle">Add New Room</h2>
      <form id="addRoomForm">
        <div class="form-group">
          <label for="roomName">Room Name:</label>
          <input type="text" id="roomName" name="roomName" required>
        </div>
        <div class="form-group">
          <label for="roomColor">Room Color:</label>
          <input type="color" id="roomColor" name="roomColor" value="#ffffff" required>
        </div>
        <div class="form-group">
          <label for="roomLocation">Room Location:</label>
          <input type="text" id="roomLocation" name="roomLocation" required>
        </div>
        <button type="submit">Add Room</button>
      </form>
    </div>
  </div>

  <!-- Edit Room Modal -->
  <div id="editRoomModal" class="modal" aria-hidden="true" role="dialog" aria-labelledby="editRoomModalTitle">
    <div class="modal-content">
      <span class="close" id="closeEditModal" aria-label="Close modal">&times;</span>
      <h2 id="editRoomModalTitle">Edit Room</h2>
      <form id="editRoomForm">
        <input type="hidden" id="editRoomId" name="editRoomId">
        <div class="form-group">
          <label for="editRoomName">Room Name:</label>
          <input type="text" id="editRoomName" name="editRoomName" required>
        </div>
        <div class="form-group">
          <label for="editRoomColor">Room Color:</label>
          <input type="color" id="editRoomColor" name="editRoomColor" value="#ffffff" required>
        </div>
        <div class="form-group">
          <label for="editRoomLocation">Room Location:</label>
          <input type="text" id="editRoomLocation" name="editRoomLocation" required>
        </div>
        <button type="submit">Update Room</button>
      </form>
    </div>
  </div>

  <script src="../auth-utils.js"></script>
  <script>
    // Define the API URLs
    const ROOMS_API_URL = 'https://shans-backend.onrender.com/api/rooms';
    const PRODUCTS_API_URL = 'https://shans-backend.onrender.com/api/products';
    // API_BASE_URL is now defined in auth-utils.js

    // Enhanced authentication check with better error handling
    async function checkAuthStatus() {
      const token = storage.get('authToken');
      const userInfo = storage.get('userInfo');

      if (!token || !userInfo) {
        showToast('Please log in to access this page', 'error');
        setTimeout(() => {
          window.location.href = '../login.html';
        }, 2000);
        return false;
      }

      try {
        const user = JSON.parse(userInfo);
        if (!user.is_admin) {
          showToast('Admin access required', 'error');
          storage.remove('authToken');
          storage.remove('userInfo');
          setTimeout(() => {
            window.location.href = '../login.html';
          }, 2000);
          return false;
        }

        // Check if we have a recent auth check (within 5 minutes)
        const lastCheck = storage.get('lastAuthCheck');
        const now = Date.now();
        if (lastCheck && (now - parseInt(lastCheck)) < 5 * 60 * 1000) {
          return true; // Skip server validation if recent check
        }

        // Verify token with backend with timeout and retry
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        try {
          const response = await fetch(`${API_BASE_URL}/auth/me`, {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            if (data.user.is_admin) {
              storage.set('lastAuthCheck', now.toString());
              return true;
            } else {
              throw new Error('Admin privileges required');
            }
          } else if (response.status === 401 || response.status === 403) {
            throw new Error('Authentication failed');
          } else {
            throw new Error(`Server error: ${response.status}`);
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);

          // If it's a network error and we have cached user info, allow access
          if (fetchError.name === 'AbortError' || fetchError.message.includes('fetch')) {
            console.warn('Network error during auth check, using cached credentials');
            showToast('Using cached authentication (network issues)', 'info');
            return true; // Allow access with cached credentials
          }
          throw fetchError;
        }

      } catch (error) {
        console.error('Auth check failed:', error);
        showToast(`Authentication error: ${error.message}`, 'error');
        storage.remove('authToken');
        storage.remove('userInfo');
        storage.remove('lastAuthCheck');
        setTimeout(() => {
          window.location.href = '../login.html';
        }, 3000);
        return false;
      }
    }

    // Logout functionality with better error handling
    function logout() {
      showToast('Logging out...', 'info');

      const token = storage.get('authToken');

      // Call logout endpoint (don't wait for response)
      if (token) {
        fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }).catch(error => {
          console.warn('Logout API call failed:', error);
        });
      }

      // Clear storage and redirect
      storage.remove('authToken');
      storage.remove('userInfo');
      storage.remove('lastAuthCheck');

      showToast('Logged out successfully', 'success');
      setTimeout(() => {
        window.location.href = 'login.html';
      }, 1000);
    }

    // Get DOM elements
    const roomGrid = document.getElementById('roomGrid');
    const addRoomBlock = document.getElementById('addRoomBlock');
    const addRoomModal = document.getElementById('addRoomModal');
    const closeAddModalBtn = document.getElementById('closeAddModal');
    const addRoomForm = document.getElementById('addRoomForm');

    const editRoomModal = document.getElementById('editRoomModal');
    const closeEditModalBtn = document.getElementById('closeEditModal');
    const editRoomForm = document.getElementById('editRoomForm');

    /**
     * Show a toast message
     * @param {string} message - The toast message
     * @param {string} type - The type of message ('success', 'error', 'info')
     */
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.classList.add('toast', `toast-${type}`);
      toast.textContent = message;
      document.body.appendChild(toast);

      // The toast uses a CSS animation to fade out after some time
      // If you want to remove it after the animation, you can do so with a timer:
      setTimeout(() => {
        toast.remove();
      }, 4000); // 4s matches the animation duration
    }

    /**
     * Function to create a room block element
     * @param {Object} room - The room data
     * @returns {HTMLElement} - The room block element
     */
    function createRoomBlock(room) {
      const roomBlock = document.createElement('div');
      roomBlock.className = 'room-block';
      roomBlock.style.backgroundColor = room.color;
      roomBlock.innerHTML = `
        <h3>${room.name}</h3>
        <p>${room.location}</p>
        <button class="edit-button" data-id="${room.id}">Edit</button>
        <button class="delete-button" data-id="${room.id}">Delete</button>
      `;
      return roomBlock;
    }

    /**
     * Fetch all rooms from the backend and display them
     */
    async function fetchRooms() {
      try {
        const token = storage.get('authToken');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch(ROOMS_API_URL, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 401 || response.status === 403) {
            showToast('Authentication expired. Please log in again.', 'error');
            setTimeout(() => {
              window.location.href = '../login.html';
            }, 2000);
            return;
          }
          throw new Error(`Server error: ${response.status}`);
        }

        const rooms = await response.json();

        // Clear existing room blocks except the add-room block
        roomGrid.querySelectorAll('.room-block:not(.add-room)').forEach(block => block.remove());

        // Add each room to the grid
        rooms.forEach(room => {
          const roomElement = createRoomBlock(room);
          roomGrid.insertBefore(roomElement, addRoomBlock);
        });

        showToast(`Loaded ${rooms.length} rooms successfully`, 'success');

      } catch (error) {
        console.error('Error fetching rooms:', error);

        if (error.name === 'AbortError') {
          showToast('Request timed out. Please check your connection and try again.', 'error');
        } else if (error.message.includes('fetch')) {
          showToast('Network error. Please check if the server is running.', 'error');
        } else {
          showToast(`Failed to load rooms: ${error.message}`, 'error');
        }
      }
    }

    /**
     * Open the Add Room modal
     */
    function openAddModal() {
      addRoomModal.style.display = 'block';
      addRoomModal.setAttribute('aria-hidden', 'false');
    }

    /**
     * Close the Add Room modal
     */
    function closeAddModal() {
      addRoomModal.style.display = 'none';
      addRoomModal.setAttribute('aria-hidden', 'true');
      addRoomForm.reset();
    }

    /**
     * Open the Edit Room modal with pre-filled data
     * @param {number} id - The ID of the room to edit
     */
    async function openEditModal(id) {
      try {
        const response = await fetch(`${ROOMS_API_URL}/${id}`);
        if (!response.ok) {
          throw new Error(`Room not found (Status: ${response.status})`);
        }
        const room = await response.json();

        // Fill the form with existing room data
        document.getElementById('editRoomId').value = room.id;
        document.getElementById('editRoomName').value = room.name;
        document.getElementById('editRoomColor').value = room.color;
        document.getElementById('editRoomLocation').value = room.location;

        // Show the edit modal
        editRoomModal.style.display = 'block';
        editRoomModal.setAttribute('aria-hidden', 'false');
      } catch (error) {
        console.error('Error fetching room:', error);
        // You could show a toast error here if desired:
        // showToast('Failed to load room details.', 'error');
      }
    }

    /**
     * Close the Edit Room modal
     */
    function closeEditModal() {
      editRoomModal.style.display = 'none';
      editRoomModal.setAttribute('aria-hidden', 'true');
      editRoomForm.reset();
    }

    /**
     * Handle the submission of the Add Room form
     */
    addRoomForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const roomName = document.getElementById('roomName').value.trim();
      const roomColor = document.getElementById('roomColor').value;
      const roomLocation = document.getElementById('roomLocation').value.trim();

      if (!roomName || !roomLocation) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      const newRoom = {
        name: roomName,
        color: roomColor,
        location: roomLocation
      };

      try {
        const response = await fetch(ROOMS_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(newRoom)
        });

        if (response.ok) {
          const createdRoom = await response.json();
          const roomElement = createRoomBlock(createdRoom);
          roomGrid.insertBefore(roomElement, addRoomBlock);
          closeAddModal();
          showToast('Room added successfully.', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to add room.');
        }
      } catch (error) {
        console.error('Error adding room:', error);
        showToast(`An error occurred while adding the room: ${error.message}`, 'error');
      }
    });

    /**
     * Handle the submission of the Edit Room form
     */
    editRoomForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const id = document.getElementById('editRoomId').value;
      const roomName = document.getElementById('editRoomName').value.trim();
      const roomColor = document.getElementById('editRoomColor').value;
      const roomLocation = document.getElementById('editRoomLocation').value.trim();

      if (!roomName || !roomLocation) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      const updatedRoom = {
        name: roomName,
        color: roomColor,
        location: roomLocation
      };

      try {
        const response = await fetch(`${ROOMS_API_URL}/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updatedRoom)
        });

        if (response.ok) {
          const room = await response.json();
          // Update the room block in the UI
          const roomBlock = document.querySelector(`.edit-button[data-id='${id}']`).parentElement;
          roomBlock.style.backgroundColor = room.color;
          roomBlock.querySelector('h3').textContent = room.name;
          roomBlock.querySelector('p').textContent = room.location;
          closeEditModal();
          showToast('Room updated successfully.', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to update room.');
        }
      } catch (error) {
        console.error('Error updating room:', error);
        showToast(`An error occurred while updating the room: ${error.message}`, 'error');
      }
    });

    /**
     * Handle Delete Room action with email verification
     * @param {number} id - The ID of the room to delete
     */
    async function deleteRoom(id) {
      const roomBlock = document.querySelector(`.delete-button[data-id='${id}']`).parentElement;
      const roomName = roomBlock.querySelector('h3').textContent;

      // Show custom confirmation modal instead of browser alert
      showConfirmationModal(id, roomName);
    }

    /**
     * Show custom confirmation modal
     */
    function showConfirmationModal(roomId, roomName) {
      const modalHTML = `
        <div id="confirmationModal" style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        ">
          <div style="
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 450px;
            width: 90%;
            text-align: center;
          ">
            <h3 style="color: #d32f2f; margin-bottom: 20px;">⚠️ Delete Room Confirmation</h3>
            <p style="margin-bottom: 15px; font-size: 16px;">
              Are you sure you want to delete <strong>"${roomName}"</strong>?
            </p>
            <p style="margin-bottom: 20px; color: #666; font-size: 14px;">
              This will also delete all products linked to this room.
            </p>
            <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <p style="margin: 0; font-size: 14px; color: #1976d2;">
                📧 A verification code will be sent to<br>
                <strong><EMAIL></strong> for confirmation
              </p>
            </div>
            <div style="display: flex; gap: 10px; justify-content: center;">
              <button
                onclick="proceedWithDeletion(${roomId}, '${roomName}')"
                style="
                  background-color: #d32f2f;
                  color: white;
                  padding: 12px 24px;
                  border: none;
                  border-radius: 5px;
                  cursor: pointer;
                  font-size: 16px;
                  font-weight: bold;
                "
              >
                Yes, Delete Room
              </button>
              <button
                onclick="closeConfirmationModal()"
                style="
                  background-color: #666;
                  color: white;
                  padding: 12px 24px;
                  border: none;
                  border-radius: 5px;
                  cursor: pointer;
                  font-size: 16px;
                "
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Close confirmation modal
     */
    function closeConfirmationModal() {
      const modal = document.getElementById('confirmationModal');
      if (modal) {
        modal.remove();
      }
    }

    /**
     * Proceed with room deletion after confirmation
     */
    async function proceedWithDeletion(id, roomName) {
      // Close confirmation modal
      closeConfirmationModal();

      try {
        // Request verification code
        showToast('Sending verification code...', 'info');

        const response = await fetch(`${ROOMS_API_URL}/${id}/request-deletion`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to request verification code');
        }

        const data = await response.json();
        showToast('Verification code <NAME_EMAIL>', 'success');

        // Show verification modal
        showVerificationModal(id, roomName);

      } catch (error) {
        console.error('Error requesting room deletion:', error);
        showToast(`Failed to send verification code: ${error.message}`, 'error');
      }
    }

    /**
     * Show verification code input modal
     */
    function showVerificationModal(roomId, roomName) {
      // Create modal HTML
      const modalHTML = `
        <div id="verificationModal" style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        ">
          <div style="
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            text-align: center;
          ">
            <h3 style="color: #d32f2f; margin-bottom: 20px;">Delete Room Verification</h3>
            <p style="margin-bottom: 20px;">
              Enter the verification code sent to<br>
              <strong><EMAIL></strong>
            </p>
            <p style="margin-bottom: 20px; color: #666;">
              Room: <strong>${roomName}</strong>
            </p>
            <input
              type="text"
              id="verificationCodeInput"
              placeholder="Enter 6-digit code"
              maxlength="6"
              style="
                width: 100%;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 18px;
                text-align: center;
                letter-spacing: 2px;
                margin-bottom: 20px;
                box-sizing: border-box;
              "
            >
            <div style="display: flex; gap: 10px; justify-content: center;">
              <button
                onclick="verifyAndDeleteRoom(${roomId})"
                style="
                  background-color: #d32f2f;
                  color: white;
                  padding: 10px 20px;
                  border: none;
                  border-radius: 5px;
                  cursor: pointer;
                  font-size: 16px;
                "
              >
                Verify & Delete
              </button>
              <button
                onclick="closeVerificationModal()"
                style="
                  background-color: #666;
                  color: white;
                  padding: 10px 20px;
                  border: none;
                  border-radius: 5px;
                  cursor: pointer;
                  font-size: 16px;
                "
              >
                Cancel
              </button>
            </div>
            <p style="margin-top: 15px; font-size: 12px; color: #666;">
              Code expires in 5 minutes
            </p>
          </div>
        </div>
      `;

      // Add modal to page
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Focus on input
      document.getElementById('verificationCodeInput').focus();

      // Add enter key listener
      document.getElementById('verificationCodeInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          verifyAndDeleteRoom(roomId);
        }
      });
    }
    /**
     * Verify code and delete room
     */
    async function verifyAndDeleteRoom(roomId) {
      const codeInput = document.getElementById('verificationCodeInput');
      const verificationCode = codeInput.value.trim();

      if (!verificationCode) {
        showToast('Please enter the verification code', 'error');
        return;
      }

      if (verificationCode.length !== 6) {
        showToast('Verification code must be 6 digits', 'error');
        return;
      }

      try {
        showToast('Verifying code...', 'info');

        const response = await fetch(`${ROOMS_API_URL}/${roomId}/verify-deletion`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
          },
          body: JSON.stringify({ verificationCode })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'Verification failed');
        }

        // Success - remove room from UI
        const roomBlock = document.querySelector(`.delete-button[data-id='${roomId}']`).parentElement;
        if (roomBlock) {
          roomBlock.remove();
        }

        closeVerificationModal();
        showToast(data.message, 'success');

      } catch (error) {
        console.error('Error verifying deletion:', error);
        showToast(`Verification failed: ${error.message}`, 'error');
      }
    }

    /**
     * Close verification modal
     */
    function closeVerificationModal() {
      const modal = document.getElementById('verificationModal');
      if (modal) {
        modal.remove();
      }
    }

    /**
     * Handle Delete Room form submission
     * @param {number} id - The ID of the room to delete
     */
    async function handleDeleteRoom(id) {
      await deleteRoom(id);
    }

    /**
     * Event delegation for Edit and Delete buttons
     */
    roomGrid.addEventListener('click', (e) => {
      if (e.target.classList.contains('edit-button')) {
        const id = e.target.getAttribute('data-id');
        openEditModal(id);
      }

      if (e.target.classList.contains('delete-button')) {
        const id = e.target.getAttribute('data-id');
        handleDeleteRoom(id);
      }
    });

    /**
     * Event listeners for opening and closing modals
     */
    addRoomBlock.addEventListener('click', openAddModal);
    closeAddModalBtn.addEventListener('click', closeAddModal);
    closeEditModalBtn.addEventListener('click', closeEditModal);

    window.addEventListener('click', (event) => {
      if (event.target === addRoomModal) {
        closeAddModal();
      }
      if (event.target === editRoomModal) {
        closeEditModal();
      }
    });

    // Initialize page with better error handling
    window.onload = async function() {
      try {
        showToast('Loading rooms...', 'info');

        const isAuthenticated = await checkAuthStatus();
        if (!isAuthenticated) {
          return; // checkAuthStatus will handle the redirect
        }

        // Fetch and display rooms on page load
        await fetchRooms();

      } catch (error) {
        console.error('Page initialization error:', error);
        showToast('Failed to initialize page. Please refresh and try again.', 'error');
      }
    };

    // Also handle page visibility changes to re-check auth when user returns
    document.addEventListener('visibilitychange', async function() {
      if (!document.hidden) {
        // Page became visible, check if we're still authenticated
        const token = storage.get('authToken');
        if (token) {
          const lastCheck = storage.get('lastAuthCheck');
          const now = Date.now();
          // If last check was more than 10 minutes ago, re-verify
          if (!lastCheck || (now - parseInt(lastCheck)) > 10 * 60 * 1000) {
            console.log('Re-checking authentication after page visibility change');
            await checkAuthStatus();
          }
        }
      }
    });
  </script>
</body>
</html>
