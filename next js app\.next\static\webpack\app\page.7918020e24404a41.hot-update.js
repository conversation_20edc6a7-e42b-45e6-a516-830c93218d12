"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/auth-utils */ \"(app-pages-browser)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CURRENCY_SYMBOL = 'R';\nconst TAX_RATE = 0.15;\nconst DEBOUNCE_DELAY = 300;\nconst PRODUCTS_PER_PAGE = 20;\nconst companies = {\n    company1: {\n        key: 'company1',\n        name: \"Shans Accessories PTY LTD\",\n        bankingInformation: \"\\n      First National Bank<br>\\n      Account :  ***********<br>\\n      Branch code 257705<br>\\n      Swift code FIRNZAJJ\\n    \"\n    },\n    company2: {\n        key: 'company2',\n        name: \"Shans Autosport PTY LTD\",\n        bankingInformation: \"\\n      Business Account<br>\\n      Capitec Current Account<br>\\n      Account: **********\\n    \"\n    },\n    company3: {\n        key: 'company3',\n        name: \"Shans Motorstyle PTY LTD\",\n        bankingInformation: \"\\n      SHANS MOTORSTYLE (PTY) LTD<br>\\n      Gold Business Account<br>\\n      Account Number: ***********<br>\\n      Branch Code: 250655<br>\\n      Swift Code: FIRNZAJJ\\n    \"\n    }\n};\nfunction HomePage() {\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { messages, showToast, removeToast } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [allProducts, setAllProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingError, setLoadingError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreProducts, setHasMoreProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [newProductCounter, setNewProductCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Form state\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerEmail, setCustomerEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerAddress, setCustomerAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerPhone, setCustomerPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [salespersonName, setSalespersonName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sameAsBilling, setSameAsBilling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [shippingName, setShippingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingEmail, setShippingEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingAddress, setShippingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingPhone, setShippingPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [productSearch, setProductSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [price, setPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [includeTax, setIncludeTax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Cash');\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Product search state\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showProductList, setShowProductList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductData, setSelectedProductData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSelectingProduct, setIsSelectingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const searchTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Debounced search function\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[debouncedSearch]\": ()=>{\n            if (searchTimeoutRef.current) {\n                clearTimeout(searchTimeoutRef.current);\n            }\n            searchTimeoutRef.current = setTimeout({\n                \"HomePage.useCallback[debouncedSearch]\": ()=>{\n                    displayFilteredProducts();\n                }\n            }[\"HomePage.useCallback[debouncedSearch]\"], DEBOUNCE_DELAY);\n        }\n    }[\"HomePage.useCallback[debouncedSearch]\"], [\n        productSearch,\n        allProducts\n    ]);\n    // Fetch products from API\n    const fetchAllProducts = async ()=>{\n        try {\n            setIsLoading(true);\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const cacheBuster = \"&_t=\".concat(Date.now());\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/products?page=\").concat(currentPage, \"&limit=\").concat(PRODUCTS_PER_PAGE).concat(cacheBuster), {\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const newProducts = await response.json();\n            if (newProducts.length < PRODUCTS_PER_PAGE) {\n                setHasMoreProducts(false);\n            }\n            // Filter out duplicates\n            const existingItemCodes = new Set(allProducts.map((p)=>p.item_code));\n            const uniqueNewProducts = newProducts.filter((product)=>!existingItemCodes.has(product.item_code));\n            setAllProducts((prev)=>[\n                    ...prev,\n                    ...uniqueNewProducts\n                ]);\n            setCurrentPage((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            if (error instanceof Error && error.name === 'AbortError') {\n                setLoadingError('Request timed out. Please check your connection.');\n            } else {\n                setLoadingError('Failed to load products. Please try again.');\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Display filtered products\n    const displayFilteredProducts = ()=>{\n        const searchTerm = productSearch.toLowerCase().trim();\n        if (searchTerm.length === 0) {\n            setFilteredProducts([]);\n            setShowProductList(false);\n            return;\n        }\n        const filtered = allProducts.filter((product)=>product.item_name && product.item_name.toLowerCase().includes(searchTerm));\n        setFilteredProducts(filtered);\n        setShowProductList(true);\n    };\n    // Initialize app\n    const initializeApp = async ()=>{\n        try {\n            setIsLoading(true);\n            setLoadingError('');\n            await fetchAllProducts();\n            // Load saved company selection\n            const savedCompany = _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__.storage.get('selectedCompany');\n            if (savedCompany) {\n                try {\n                    const company = JSON.parse(savedCompany);\n                    setSelectedCompany(company.key);\n                } catch (e) {\n                    console.warn('Error parsing saved company:', e);\n                }\n            }\n            // Load existing order data\n            loadExistingOrderData();\n        } catch (error) {\n            console.error('Initialization error:', error);\n            setLoadingError('Failed to initialize the application. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Load existing order data from storage\n    const loadExistingOrderData = ()=>{\n    // Implementation for loading saved order data\n    // This would restore form fields from localStorage if needed\n    };\n    // Retry initialization\n    const retryInitialization = ()=>{\n        setLoadingError('');\n        setAllProducts([]);\n        setCurrentPage(1);\n        setHasMoreProducts(true);\n        initializeApp();\n    };\n    // Add product to cart\n    const addProduct = ()=>{\n        if (!productSearch.trim()) {\n            showToast('Please enter a product name', 'error');\n            return;\n        }\n        if (price <= 0) {\n            showToast('Please enter a valid price', 'error');\n            return;\n        }\n        if (quantity <= 0) {\n            showToast('Please enter a valid quantity', 'error');\n            return;\n        }\n        const newProduct = {\n            item_code: (selectedProductData === null || selectedProductData === void 0 ? void 0 : selectedProductData.item_code) || \"NEW_\".concat(newProductCounter),\n            name: productSearch.trim(),\n            room_name: room.trim() || 'N/A',\n            quantity: quantity,\n            price: price,\n            tax_per_product: includeTax ? price * TAX_RATE : 0,\n            is_new: !selectedProductData\n        };\n        setSelectedProducts((prev)=>[\n                ...prev,\n                newProduct\n            ]);\n        // Reset form\n        setProductSearch('');\n        setQuantity(1);\n        setPrice(0);\n        setRoom('');\n        setSelectedProductData(null);\n        setShowProductList(false);\n        if (!selectedProductData) {\n            setNewProductCounter((prev)=>prev + 1);\n        }\n        showToast('Product added to cart', 'success');\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            initializeApp();\n        }\n    }[\"HomePage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            debouncedSearch();\n        }\n    }[\"HomePage.useEffect\"], [\n        productSearch,\n        debouncedSearch\n    ]);\n    // Calculate totals\n    const calculateTotals = ()=>{\n        let subtotal = 0;\n        let tax = 0;\n        let total = 0;\n        selectedProducts.forEach((product)=>{\n            if (includeTax) {\n                subtotal += product.price * (1 - TAX_RATE) * product.quantity;\n                tax += product.price * TAX_RATE * product.quantity;\n                total += product.price * product.quantity;\n            } else {\n                subtotal += product.price * product.quantity;\n                total += product.price * product.quantity;\n            }\n        });\n        if (!includeTax) {\n            tax = 0;\n        }\n        return {\n            subtotal: Math.round(subtotal),\n            tax: Math.round(tax),\n            total: Math.round(total)\n        };\n    };\n    const totals = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isVisible: isLoading,\n                    error: loadingError,\n                    onRetry: retryInitialization\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    messages: messages,\n                    onRemove: removeToast\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto bg-white p-5 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-5 flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-gray-800 text-2xl font-normal m-0\",\n                                    children: \"Receipt and Quotation Generator\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 flex-wrap\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/convert-to-receipt\",\n                                            className: \"bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors\",\n                                            children: \"Convert to Receipt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        (user === null || user === void 0 ? void 0 : user.is_admin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            className: \"bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors\",\n                                            children: \"Admin Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Select Company\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"companySelect\",\n                                    className: \"block mb-1\",\n                                    children: \"Choose a company:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"companySelect\",\n                                    value: selectedCompany,\n                                    onChange: (e)=>setSelectedCompany(e.target.value),\n                                    required: true,\n                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Select a company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company1\",\n                                            children: \"Shans Accessories PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company2\",\n                                            children: \"Shans Autosport PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company3\",\n                                            children: \"Shans Motorstyle PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Customer Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerName\",\n                                    className: \"block mb-1\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerName\",\n                                    value: customerName,\n                                    onChange: (e)=>setCustomerName(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerEmail\",\n                                    className: \"block mb-1\",\n                                    children: \"Email:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"customerEmail\",\n                                    value: customerEmail,\n                                    onChange: (e)=>setCustomerEmail(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerAddress\",\n                                    className: \"block mb-1\",\n                                    children: \"Address:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerAddress\",\n                                    value: customerAddress,\n                                    onChange: (e)=>setCustomerAddress(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerPhone\",\n                                    className: \"block mb-1\",\n                                    children: \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"customerPhone\",\n                                    value: customerPhone,\n                                    onChange: (e)=>setCustomerPhone(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"salespersonName\",\n                                    className: \"block mb-1\",\n                                    children: \"Salesperson Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"salespersonName\",\n                                    value: salespersonName,\n                                    onChange: (e)=>setSalespersonName(e.target.value),\n                                    placeholder: \"Enter your name...\",\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Shipping Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: sameAsBilling,\n                                                onChange: (e)=>setSameAsBilling(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Same as billing address\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                !sameAsBilling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingName\",\n                                            className: \"block mb-1\",\n                                            children: \"Name:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingName\",\n                                            value: shippingName,\n                                            onChange: (e)=>setShippingName(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingEmail\",\n                                            className: \"block mb-1\",\n                                            children: \"Email:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"shippingEmail\",\n                                            value: shippingEmail,\n                                            onChange: (e)=>setShippingEmail(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingAddress\",\n                                            className: \"block mb-1\",\n                                            children: \"Address:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingAddress\",\n                                            value: shippingAddress,\n                                            onChange: (e)=>setShippingAddress(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingPhone\",\n                                            className: \"block mb-1\",\n                                            children: \"Phone:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            id: \"shippingPhone\",\n                                            value: shippingPhone,\n                                            onChange: (e)=>setShippingPhone(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Add Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"productSearch\",\n                                            className: \"block mb-1\",\n                                            children: \"Search Products:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"productSearch\",\n                                            value: productSearch,\n                                            onChange: (e)=>setProductSearch(e.target.value),\n                                            placeholder: \"Type to search products...\",\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        showProductList && filteredProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg\",\n                                            children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors\",\n                                                    onClick: ()=>{\n                                                        setSelectedProductData(product);\n                                                        setProductSearch(product.item_name);\n                                                        setPrice(product.unit_retail_price);\n                                                        setRoom(product.room_name);\n                                                        setShowProductList(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: product.item_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Code: \",\n                                                                product.item_code,\n                                                                \" | Price: \",\n                                                                CURRENCY_SYMBOL,\n                                                                product.unit_retail_price,\n                                                                \" | Stock: \",\n                                                                product.available_stock,\n                                                                \" | Room: \",\n                                                                product.room_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, product.item_code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"quantity\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Quantity:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"quantity\",\n                                                    value: quantity,\n                                                    onChange: (e)=>setQuantity(Math.max(1, parseInt(e.target.value) || 1)),\n                                                    min: \"1\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"price\",\n                                                    className: \"block mb-1\",\n                                                    children: [\n                                                        \"Price (\",\n                                                        CURRENCY_SYMBOL,\n                                                        \"):\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"price\",\n                                                    value: price,\n                                                    onChange: (e)=>setPrice(parseFloat(e.target.value) || 0),\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"room\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Room:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"room\",\n                                                    value: room,\n                                                    onChange: (e)=>setRoom(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: addProduct,\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                    children: \"Add Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Selected Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                selectedProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"No products selected yet.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Room\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Qty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: selectedProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.room_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price * product.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Remove product logic\n                                                                        setSelectedProducts((prev)=>prev.filter((_, i)=>i !== index));\n                                                                        showToast('Product removed', 'info');\n                                                                    },\n                                                                    className: \"bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Order Summary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"paymentMethod\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Payment Method:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"paymentMethod\",\n                                                    value: paymentMethod,\n                                                    onChange: (e)=>setPaymentMethod(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Cash\",\n                                                            children: \"Cash\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Card\",\n                                                            children: \"Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Bank Transfer\",\n                                                            children: \"Bank Transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Other\",\n                                                            children: \"Other\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: includeTax,\n                                                        onChange: (e)=>setIncludeTax(e.target.checked),\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Include Tax (15%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"comments\",\n                                            className: \"block mb-1\",\n                                            children: \"Comments:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"comments\",\n                                            value: comments,\n                                            onChange: (e)=>setComments(e.target.value),\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border resize-vertical\",\n                                            placeholder: \"Additional comments or notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 bg-gray-100 p-3 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtotal:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.subtotal\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        includeTax && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tax (15%):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.tax\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between font-bold text-lg border-t border-gray-300 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Receipt functionality coming soon', 'info'),\n                                    className: \"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors\",\n                                    children: \"Generate Receipt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Quotation functionality coming soon', 'info'),\n                                    className: \"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"Generate Quotation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Invoice functionality coming soon', 'info'),\n                                    className: \"bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"Generate Invoice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"zkpdqlB5oUXCsCdxZs59SzvbmMM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});