export interface Product {
  item_code: string;
  item_name: string;
  unit_retail_price: number;
  available_stock: number;
  room_id: string;
  room_name: string;
}

export interface SelectedProduct {
  item_code: string;
  name: string;
  room_name: string;
  quantity: number;
  price: number;
  tax_per_product: number;
  is_new: boolean;
}

export interface CustomerInfo {
  name: string;
  email: string;
  address: string;
  phone: string;
}

export interface ShippingInfo extends CustomerInfo {}

export interface Company {
  key: string;
  name: string;
  bankingInformation: string;
}

export interface OrderData {
  customerInfo: {
    billing: CustomerInfo;
    shipping: ShippingInfo | null;
  };
  selectedProducts: SelectedProduct[];
  paymentMethod: string;
  comments: string;
  salespersonName: string;
  companyKey: string;
  includeTax: boolean;
}
