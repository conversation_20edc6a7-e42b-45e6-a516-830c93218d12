(()=>{var e={};e.id=698,e.ids=[698],e.modules={769:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(687),o=r(3210),n=r(6189),i=r(3213);function a({children:e,requireAdmin:t=!1,redirectTo:r="/login"}){let{user:a,isLoading:l,isAuthenticated:d}=(0,i.A)();(0,n.useRouter)();let[c,u]=(0,o.useState)(!1);return l?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):c?(0,s.jsx)(s.Fragment,{children:e}):null}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(687);function o({isVisible:e,message:t="Loading products, please wait...",error:r,onRetry:o}){return e?(0,s.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,s.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:r?"Connection Error":t}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:r}),o&&(0,s.jsx)("button",{onClick:o,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\page.tsx","default")},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>i,IG:()=>o,JR:()=>s,ri:()=>a,z:()=>n});let s="https://shans-backend.onrender.com/api",o={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function n(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:i=2,timeout:a=8e3}=e,l=o.get("authToken"),d=o.get("userInfo");if(!l)return!1;if(d)try{JSON.parse(d);let e=o.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=i;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),a),r=`?_t=${Date.now()}`,n=await fetch(`${s}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),n.ok){let e=await n.json();return o.set("userInfo",JSON.stringify(e.user)),o.set("lastAuthCheck",Date.now().toString()),!0}if(401===n.status||403===n.status){o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${n.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===i){if(d)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function i(){let e=o.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function a(){let e=o.get("authToken");e&&fetch(`${s}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck")}},2285:(e,t,r)=>{Promise.resolve().then(r.bind(r,2454))},2454:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(687),o=r(3213),n=r(6189),i=r(3210),a=r(769),l=r(854);function d(){let{user:e,logout:t}=(0,o.A)(),r=(0,n.useRouter)(),[d,c]=(0,i.useState)(!0);return d?(0,s.jsx)(a.A,{requireAdmin:!0,children:(0,s.jsx)(l.A,{isVisible:!0,message:"Loading dashboard, please wait..."})}):(0,s.jsx)(a.A,{requireAdmin:!0,children:(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4",children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 m-0",children:"Welcome, Admin"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,s.jsx)("button",{onClick:()=>r.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors",children:"Go to User Page"}),(0,s.jsx)("button",{onClick:t,className:"bg-red-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-700 transition-colors",children:"Logout"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[{id:"manageRooms",title:"Manage Rooms",icon:"\uD83D\uDEAA",href:"/admin/manage-rooms",color:"from-blue-500 to-blue-600",hoverColor:"hover:from-blue-600 hover:to-blue-700"},{id:"manageProducts",title:"Manage Products",icon:"\uD83D\uDCE6",href:"/admin/manage-products",color:"from-green-500 to-green-600",hoverColor:"hover:from-green-600 hover:to-green-700"},{id:"lowStock",title:"Low Stock",icon:"⚠️",href:"/admin/low-stock",color:"from-orange-500 to-orange-600",hoverColor:"hover:from-orange-600 hover:to-orange-700"},{id:"salesHistory",title:"Sales History",icon:"\uD83D\uDCC8",href:"/admin/sales-history",color:"from-red-500 to-red-600",hoverColor:"hover:from-red-600 hover:to-red-700"},{id:"manageUsers",title:"Manage Users",icon:"\uD83D\uDC65",href:"/admin/manage-users",color:"from-purple-500 to-purple-600",hoverColor:"hover:from-purple-600 hover:to-purple-700"},{id:"backupFiles",title:"Backup Files",icon:"☁️",href:"/admin/backup-files",color:"from-indigo-500 to-indigo-600",hoverColor:"hover:from-indigo-600 hover:to-indigo-700"},{id:"expenses",title:"Expenses",icon:"\uD83E\uDDFE",href:"/admin/expenses",color:"from-pink-500 to-pink-600",hoverColor:"hover:from-pink-600 hover:to-pink-700"}].map(e=>(0,s.jsxs)("div",{onClick:()=>r.push(e.href),className:`
                  bg-gradient-to-br ${e.color} ${e.hoverColor}
                  rounded-2xl p-6 text-center cursor-pointer
                  transform transition-all duration-300 ease-in-out
                  hover:-translate-y-2 hover:shadow-xl
                  shadow-lg text-white
                  min-h-[160px] flex flex-col justify-center items-center
                `,children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,s.jsx)("h2",{className:"text-lg font-semibold",children:e.title})]},e.id))}),(0,s.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Quick Actions"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Access frequently used admin functions"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"System Status"}),(0,s.jsx)("p",{className:"text-green-600 text-sm",children:"All systems operational"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Recent Activity"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Monitor latest admin actions"})]})]})]})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>l});var s=r(687),o=r(3210),n=r(1439);let i=(0,o.createContext)(void 0);function a(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(!0),[d,c]=(0,o.useState)(!1),u=async()=>{try{if(l(!0),!await (0,n.z)({redirectOnFail:!1}))return r(null),c(!1),!1;{let e=(0,n.HW)();return r(e),c(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),c(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let s=await fetch(`${n.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),o=await s.json();if(s.ok)return n.IG.set("authToken",o.token),n.IG.set("userInfo",JSON.stringify(o.user)),n.IG.set("lastAuthCheck",Date.now().toString()),r(o.user),c(!0),{success:!0};return{success:!1,message:o.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,s.jsx)(i.Provider,{value:{user:t,isLoading:a,isAuthenticated:d,login:h,logout:()=>{r(null),c(!1),(0,n.ri)()},checkAuth:u},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4086:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(5239),o=r(8088),n=r(8170),i=r.n(n),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var s=r(7413);r(1135);var o=r(9131);let n={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,s.jsx)(o.AuthProvider,{children:e})})})}},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7021:(e,t,r)=>{Promise.resolve().then(r.bind(r,1132))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[825],()=>r(4086));module.exports=s})();