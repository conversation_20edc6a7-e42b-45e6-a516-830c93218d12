exports.id=227,exports.ids=[227],exports.modules={769:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(687),o=r(3210),s=r(6189),a=r(3213);function i({children:e,requireAdmin:t=!1,redirectTo:r="/login"}){let{user:i,isLoading:l,isAuthenticated:u}=(0,a.A)();(0,s.useRouter)();let[c,d]=(0,o.useState)(!1);return l?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):c?(0,n.jsx)(n.Fragment,{children:e}):null}},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>a,IG:()=>o,JR:()=>n,ri:()=>i,z:()=>s});let n="https://shans-backend.onrender.com/api",o={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function s(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:a=2,timeout:i=8e3}=e,l=o.get("authToken"),u=o.get("userInfo");if(!l)return!1;if(u)try{JSON.parse(u);let e=o.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=a;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),i),r=`?_t=${Date.now()}`,s=await fetch(`${n}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),s.ok){let e=await s.json();return o.set("userInfo",JSON.stringify(e.user)),o.set("lastAuthCheck",Date.now().toString()),!0}if(401===s.status||403===s.status){o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${s.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===a){if(u)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function a(){let e=o.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=o.get("authToken");e&&fetch(`${n}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),o.remove("authToken"),o.remove("userInfo"),o.remove("lastAuthCheck")}},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var n=r(687),o=r(3210),s=r(1439);let a=(0,o.createContext)(void 0);function i(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,o.useState)(null),[i,l]=(0,o.useState)(!0),[u,c]=(0,o.useState)(!1),d=async()=>{try{if(l(!0),!await (0,s.z)({redirectOnFail:!1}))return r(null),c(!1),!1;{let e=(0,s.HW)();return r(e),c(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),c(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let n=await fetch(`${s.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),o=await n.json();if(n.ok)return s.IG.set("authToken",o.token),s.IG.set("userInfo",JSON.stringify(o.user)),s.IG.set("lastAuthCheck",Date.now().toString()),r(o.user),c(!0),{success:!0};return{success:!1,message:o.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,n.jsx)(a.Provider,{value:{user:t,isLoading:i,isAuthenticated:u,login:h,logout:()=>{r(null),c(!1),(0,s.ri)()},checkAuth:d},children:e})}},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>s});var n=r(7413);r(1135);var o=r(9131);let s={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function a({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,n.jsx)(o.AuthProvider,{children:e})})})}},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6189:(e,t,r)=>{"use strict";var n=r(5773);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var n=r(2907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9265:(e,t,r)=>{"use strict";r.d(t,{L2:()=>a,U9:()=>s,gf:()=>o});var n=r(3210);function o(e){let[t,r]=(0,n.useState)({data:null,loading:!1,error:null}),o=(0,n.useCallback)(async(...t)=>{r(e=>({...e,loading:!0,error:null}));try{let n=await e(...t);if(n.success&&n.data)return r({data:n.data,loading:!1,error:null}),n.data;return r({data:null,loading:!1,error:n.error||"An unknown error occurred"}),null}catch(e){return r({data:null,loading:!1,error:e instanceof Error?e.message:"An unknown error occurred"}),null}},[e]),s=(0,n.useCallback)(()=>{r({data:null,loading:!1,error:null})},[]);return{...t,execute:o,reset:s}}function s(e){let t=o(e),r=(0,n.useCallback)(e=>{t.data&&l(t=>({...t,data:t.data?[...t.data,...e]:e}))},[t.data]),s=(0,n.useCallback)((e,t)=>{l(r=>{if(!r.data)return r;let n=[...r.data];return n[e]=t,{...r,data:n}})},[]),a=(0,n.useCallback)(e=>{l(t=>{if(!t.data)return t;let r=t.data.filter((t,r)=>r!==e);return{...t,data:r}})},[]),[i,l]=(0,n.useState)({data:null,loading:!1,error:null});return{...t,append:r,updateItem:s,removeItem:a}}function a(e){let[t,r]=(0,n.useState)(!1),[o,s]=(0,n.useState)(null);return{mutate:(0,n.useCallback)(async t=>{r(!0),s(null);try{let n=await e(t);if(n.success&&n.data)return r(!1),n.data;return s(n.error||"An unknown error occurred"),r(!1),null}catch(e){return s(e instanceof Error?e.message:"An unknown error occurred"),r(!1),null}},[e]),loading:t,error:o,reset:(0,n.useCallback)(()=>{r(!1),s(null)},[])}}},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};