'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';
import LoadingScreen from '@/components/LoadingScreen';
import { Product } from '@/types';
import { API_BASE_URL } from '@/lib/auth-utils';

export default function LowStock() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [thresholdFilter, setThresholdFilter] = useState<number | ''>('');
  const [products, setProducts] = useState<Product[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);

  // Fetch products function
  const fetchProducts = async () => {
    try {
      setProductsLoading(true);
      setProductsError(null);

      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/products`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();
      if (data.success && data.data) {
        setProducts(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch products');
      }
    } catch (error) {
      setProductsError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setProductsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchProducts();
  }, []);

  // Filter products that are low in stock
  const lowStockProducts = products.filter(product => {
    const threshold = product.low_stock_threshold || 5;
    const isLowStock = product.available_stock <= threshold;
    
    const matchesSearch = product.item_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.item_code.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesThreshold = thresholdFilter === '' || threshold <= thresholdFilter;

    return isLowStock && matchesSearch && matchesThreshold;
  });

  // Sort by urgency (lowest stock first)
  const sortedLowStockProducts = lowStockProducts.sort((a, b) => {
    const aUrgency = a.available_stock / (a.low_stock_threshold || 5);
    const bUrgency = b.available_stock / (b.low_stock_threshold || 5);
    return aUrgency - bUrgency;
  });

  const getStockStatus = (product: Product) => {
    const threshold = product.low_stock_threshold || 5;
    const ratio = product.available_stock / threshold;
    
    if (product.available_stock === 0) {
      return { status: 'Out of Stock', color: 'bg-red-600', textColor: 'text-red-600' };
    } else if (ratio <= 0.5) {
      return { status: 'Critical', color: 'bg-red-500', textColor: 'text-red-500' };
    } else if (ratio <= 1) {
      return { status: 'Low Stock', color: 'bg-orange-500', textColor: 'text-orange-500' };
    }
    return { status: 'Normal', color: 'bg-green-500', textColor: 'text-green-500' };
  };

  const isLoading = productsLoading;

  if (isLoading) {
    return (
      <ProtectedRoute requireAdmin>
        <LoadingScreen isVisible={true} message="Loading low stock data..." />
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/admin')}
              className="text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2"
            >
              ← Back to Admin Dashboard
            </button>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <h1 className="text-3xl font-bold text-gray-900">Low Stock Alert</h1>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="inline-block w-3 h-3 bg-red-600 rounded-full"></span>
                <span>Critical</span>
                <span className="inline-block w-3 h-3 bg-orange-500 rounded-full ml-4"></span>
                <span>Low Stock</span>
                <span className="inline-block w-3 h-3 bg-red-500 rounded-full ml-4"></span>
                <span>Out of Stock</span>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white p-4 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Search Products</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by name or code..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Threshold</label>
                <input
                  type="number"
                  value={thresholdFilter}
                  onChange={(e) => setThresholdFilter(e.target.value ? parseInt(e.target.value) : '')}
                  placeholder="Filter by threshold..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  min="0"
                />
              </div>
              <div className="flex items-end">
                <div className="text-sm text-gray-600">
                  <div className="font-medium">Total Low Stock Items: {sortedLowStockProducts.length}</div>
                  <div>Out of Stock: {sortedLowStockProducts.filter(p => p.available_stock === 0).length}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Low Stock Products */}
          {sortedLowStockProducts.length > 0 ? (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Threshold</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car Brand</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sortedLowStockProducts.map((product) => {
                      const stockStatus = getStockStatus(product);
                      return (
                        <tr key={product.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <span className={`inline-block w-3 h-3 rounded-full ${stockStatus.color}`}></span>
                              <span className={`text-sm font-medium ${stockStatus.textColor}`}>
                                {stockStatus.status}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {product.item_code}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {product.item_name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            Room {product.room_id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                            {product.available_stock}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {product.low_stock_threshold || 5}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {product.car_brand_id ? `Brand ${product.car_brand_id}` : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {product.product_category?.replace(/_/g, ' ') || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => router.push(`/admin/manage-products?edit=${product.id}`)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Update Stock
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-12 text-center">
              <div className="text-green-600 text-6xl mb-4">✓</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">All Stock Levels Normal</h3>
              <p className="text-gray-500">
                {searchTerm || thresholdFilter 
                  ? 'No products match your search criteria with low stock levels.'
                  : 'All products are currently above their low stock thresholds.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
