'use client';

interface LoadingScreenProps {
  isVisible: boolean;
  message?: string;
  error?: string;
  onRetry?: () => void;
}

export default function LoadingScreen({ 
  isVisible, 
  message = "Loading products, please wait...", 
  error,
  onRetry 
}: LoadingScreenProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm">
      <div className="w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"></div>
      
      <p className="text-gray-800 text-lg font-semibold text-center text-shadow-sm">
        {error ? 'Connection Error' : message}
      </p>
      
      {error && (
        <>
          <div className="bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium">
            {error}
          </div>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg"
            >
              Retry
            </button>
          )}
        </>
      )}
    </div>
  );
}
