import { useState, useCallback } from 'react';
import { ApiResponse } from '@/lib/api';

export interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

/**
 * Custom hook for handling API calls with loading states and error handling
 * @param apiFunction - The API function to call
 * @returns Object containing data, loading state, error, execute function, and reset function
 */
export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T>>
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const response = await apiFunction(...args);
        
        if (response.success && response.data) {
          setState({
            data: response.data,
            loading: false,
            error: null,
          });
          return response.data;
        } else {
          setState({
            data: null,
            loading: false,
            error: response.error || 'An unknown error occurred',
          });
          return null;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });
        return null;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

/**
 * Custom hook for handling API calls that return arrays/lists
 * Includes additional functionality for pagination and filtering
 */
export function useApiList<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T[]>>
): UseApiReturn<T[]> & {
  append: (newItems: T[]) => void;
  updateItem: (index: number, updatedItem: T) => void;
  removeItem: (index: number) => void;
} {
  const baseHook = useApi<T[]>(apiFunction);

  const append = useCallback((newItems: T[]) => {
    baseHook.data && setState(prev => ({
      ...prev,
      data: prev.data ? [...prev.data, ...newItems] : newItems,
    }));
  }, [baseHook.data]);

  const updateItem = useCallback((index: number, updatedItem: T) => {
    setState(prev => {
      if (!prev.data) return prev;
      const newData = [...prev.data];
      newData[index] = updatedItem;
      return {
        ...prev,
        data: newData,
      };
    });
  }, []);

  const removeItem = useCallback((index: number) => {
    setState(prev => {
      if (!prev.data) return prev;
      const newData = prev.data.filter((_, i) => i !== index);
      return {
        ...prev,
        data: newData,
      };
    });
  }, []);

  const [state, setState] = useState<UseApiState<T[]>>({
    data: null,
    loading: false,
    error: null,
  });

  return {
    ...baseHook,
    append,
    updateItem,
    removeItem,
  };
}

/**
 * Custom hook for handling mutations (POST, PUT, DELETE operations)
 * Provides additional functionality for optimistic updates
 */
export function useApiMutation<TData, TVariables = any>(
  apiFunction: (variables: TVariables) => Promise<ApiResponse<TData>>
): {
  mutate: (variables: TVariables) => Promise<TData | null>;
  loading: boolean;
  error: string | null;
  reset: () => void;
} {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = useCallback(
    async (variables: TVariables): Promise<TData | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiFunction(variables);
        
        if (response.success && response.data) {
          setLoading(false);
          return response.data;
        } else {
          setError(response.error || 'An unknown error occurred');
          setLoading(false);
          return null;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        setError(errorMessage);
        setLoading(false);
        return null;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  return {
    mutate,
    loading,
    error,
    reset,
  };
}

/**
 * Custom hook for handling paginated API calls
 */
export function useApiPagination<T>(
  apiFunction: (page: number, limit: number, ...args: any[]) => Promise<ApiResponse<T[]>>,
  limit: number = 20
): UseApiReturn<T[]> & {
  page: number;
  hasMore: boolean;
  loadMore: (...args: any[]) => Promise<void>;
  refresh: (...args: any[]) => Promise<void>;
} {
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [allData, setAllData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPage = useCallback(
    async (pageNum: number, append: boolean = false, ...args: any[]): Promise<T[] | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await apiFunction(pageNum, limit, ...args);
        
        if (response.success && response.data) {
          const newData = response.data;
          
          if (append) {
            setAllData(prev => [...prev, ...newData]);
          } else {
            setAllData(newData);
          }
          
          setHasMore(newData.length === limit);
          setLoading(false);
          return newData;
        } else {
          setError(response.error || 'An unknown error occurred');
          setLoading(false);
          return null;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        setError(errorMessage);
        setLoading(false);
        return null;
      }
    },
    [apiFunction, limit]
  );

  const execute = useCallback(
    async (...args: any[]): Promise<T[] | null> => {
      setPage(1);
      return loadPage(1, false, ...args);
    },
    [loadPage]
  );

  const loadMore = useCallback(
    async (...args: any[]): Promise<void> => {
      if (!hasMore || loading) return;
      const nextPage = page + 1;
      setPage(nextPage);
      await loadPage(nextPage, true, ...args);
    },
    [hasMore, loading, page, loadPage]
  );

  const refresh = useCallback(
    async (...args: any[]): Promise<void> => {
      setPage(1);
      await loadPage(1, false, ...args);
    },
    [loadPage]
  );

  const reset = useCallback(() => {
    setPage(1);
    setHasMore(true);
    setAllData([]);
    setLoading(false);
    setError(null);
  }, []);

  return {
    data: allData,
    loading,
    error,
    execute,
    reset,
    page,
    hasMore,
    loadMore,
    refresh,
  };
}

export default useApi;
