(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[276],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>c});var s=r(5155),a=r(2115),o=r(2799);let l=(0,a.createContext)(void 0);function n(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[r,n]=(0,a.useState)(null),[c,i]=(0,a.useState)(!0),[d,u]=(0,a.useState)(!1),h=async()=>{try{if(i(!0),!await (0,o.z)({redirectOnFail:!1}))return n(null),u(!1),!1;{let e=(0,o.HW)();return n(e),u(!0),!0}}catch(e){return console.error("Auth check failed:",e),n(null),u(!1),!1}finally{i(!1)}},m=async(e,t)=>{try{i(!0);let r=await fetch("".concat(o.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),s=await r.json();if(r.ok)return o.IG.set("authToken",s.token),o.IG.set("userInfo",JSON.stringify(s.user)),o.IG.set("lastAuthCheck",Date.now().toString()),n(s.user),u(!0),{success:!0};return{success:!1,message:s.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{i(!1)}};return(0,a.useEffect)(()=>{h()},[]),(0,s.jsx)(l.Provider,{value:{user:r,isLoading:c,isAuthenticated:d,login:m,logout:()=>{n(null),u(!1),(0,o.ri)()},checkAuth:h},children:t})}},1048:(e,t,r)=>{Promise.resolve().then(r.bind(r,1166))},1166:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(5155),a=r(2115),o=r(5695),l=r(9053),n=r(7960),c=r(2799);function i(){let e=(0,o.useRouter)(),[t,r]=(0,a.useState)(""),[i,d]=(0,a.useState)(""),[u,h]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0),[g,p]=(0,a.useState)(null),f=async()=>{try{x(!0),p(null);let e=localStorage.getItem("token"),t=await fetch("".concat(c.JR,"/products"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch products");let r=await t.json();if(r.success&&r.data)h(r.data);else throw Error(r.error||"Failed to fetch products")}catch(e){p(e instanceof Error?e.message:"An error occurred")}finally{x(!1)}};(0,a.useEffect)(()=>{f()},[]);let w=u.filter(e=>{let r=e.low_stock_threshold||5,s=e.available_stock<=r,a=e.item_name.toLowerCase().includes(t.toLowerCase())||e.item_code.toLowerCase().includes(t.toLowerCase()),o=""===i||r<=i;return s&&a&&o}).sort((e,t)=>e.available_stock/(e.low_stock_threshold||5)-t.available_stock/(t.low_stock_threshold||5)),b=e=>{let t=e.low_stock_threshold||5,r=e.available_stock/t;return 0===e.available_stock?{status:"Out of Stock",color:"bg-red-600",textColor:"text-red-600"}:r<=.5?{status:"Critical",color:"bg-red-500",textColor:"text-red-500"}:r<=1?{status:"Low Stock",color:"bg-orange-500",textColor:"text-orange-500"}:{status:"Normal",color:"bg-green-500",textColor:"text-green-500"}};return m?(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(n.A,{isVisible:!0,message:"Loading low stock data..."})}):(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Low Stock Alert"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-red-600 rounded-full"}),(0,s.jsx)("span",{children:"Critical"}),(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-orange-500 rounded-full ml-4"}),(0,s.jsx)("span",{children:"Low Stock"}),(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-red-500 rounded-full ml-4"}),(0,s.jsx)("span",{children:"Out of Stock"})]})]})]}),(0,s.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Products"}),(0,s.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search by name or code...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Threshold"}),(0,s.jsx)("input",{type:"number",value:i,onChange:e=>d(e.target.value?parseInt(e.target.value):""),placeholder:"Filter by threshold...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",min:"0"})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"font-medium",children:["Total Low Stock Items: ",w.length]}),(0,s.jsxs)("div",{children:["Out of Stock: ",w.filter(e=>0===e.available_stock).length]})]})})]})}),w.length>0?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Room"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Stock"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Threshold"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Brand"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map(t=>{var r;let a=b(t);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 rounded-full ".concat(a.color)}),(0,s.jsx)("span",{className:"text-sm font-medium ".concat(a.textColor),children:a.status})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.item_code}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t.item_name}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["Room ",t.room_id]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium",children:t.available_stock}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.low_stock_threshold||5}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.car_brand_id?"Brand ".concat(t.car_brand_id):"N/A"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(r=t.product_category)?void 0:r.replace(/_/g," "))||"N/A"}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsx)("button",{onClick:()=>e.push("/admin/manage-products?edit=".concat(t.id)),className:"text-blue-600 hover:text-blue-900",children:"Update Stock"})})]},t.id)})})]})})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,s.jsx)("div",{className:"text-green-600 text-6xl mb-4",children:"✓"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"All Stock Levels Normal"}),(0,s.jsx)("p",{className:"text-gray-500",children:t||i?"No products match your search criteria with low stock levels.":"All products are currently above their low stock thresholds."})]})]})})})}},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>l,IG:()=>a,JR:()=>s,ri:()=>n,z:()=>o});let s="https://shans-backend.onrender.com/api",a={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:o=2,timeout:l=8e3}=e,n=a.get("authToken"),c=a.get("userInfo");if(!n)return t&&(window.location.href="/login"),!1;if(c)try{JSON.parse(c);let e=a.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=o;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),l),o="?_t=".concat(Date.now()),c=await fetch("".concat(s,"/auth/me").concat(o),{headers:{Authorization:"Bearer ".concat(n)},signal:e.signal});if(clearTimeout(r),c.ok){let e=await c.json();return a.set("userInfo",JSON.stringify(e.user)),a.set("lastAuthCheck",Date.now().toString()),!0}if(401===c.status||403===c.status){a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(c.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===o){if(c)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function l(){let e=a.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function n(){let e=a.get("authToken");e&&fetch("".concat(s,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),window.location.href="/login"}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7960:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(5155);function a(e){let{isVisible:t,message:r="Loading products, please wait...",error:a,onRetry:o}=e;return t?(0,s.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,s.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:a?"Connection Error":r}),a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:a}),o&&(0,s.jsx)("button",{onClick:o,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5155),a=r(2115),o=r(5695),l=r(283);function n(e){let{children:t,requireAdmin:r=!1,redirectTo:n="/login"}=e,{user:c,isLoading:i,isAuthenticated:d}=(0,l.A)(),u=(0,o.useRouter)(),[h,m]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{if(!i){if(!d)return void u.push(n);if(r&&c&&!c.is_admin)return void u.push("/");m(!0)}},[d,i,c,r,u,n]),i)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):h?(0,s.jsx)(s.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(1048)),_N_E=e.O()}]);