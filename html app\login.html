<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ffffff">
    <meta name="format-detection" content="telephone=no">
    <title>Shans System - Login Portal</title>
    <link rel="stylesheet" href="Assets/performance.css">
    <script src="Assets/performance.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
            border: 1px solid #ddd;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            width: 70px;
            height: 70px;
            margin: 0 auto 25px;
            background: #4a90e2;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 26px;
            font-weight: normal;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: normal;
        }

        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .login-btn {
            width: 100%;
            padding: 12px 15px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: normal;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            background-color: #357abd;
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #4a90e2;
        }

        .error-message {
            color: #dc3545;
            margin-top: 15px;
            padding: 12px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            display: none;
            font-size: 14px;
        }

        .success-message {
            color: #155724;
            margin-top: 15px;
            padding: 12px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            display: none;
            font-size: 14px;
        }

        .loading {
            display: none;
            margin-top: 20px;
            text-align: center;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4a90e2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        .loading p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        /* Server Status Loader */
        .server-status-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(244, 244, 244, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .server-status-content {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .server-status-spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #4a90e2;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .server-status-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .server-status-message {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .server-status-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }

        .retry-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }

        .retry-button:hover {
            background-color: #357abd;
        }

        .login-form-disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 30px;
            color: #888;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }

        /* Responsive Design */
        @media (max-width: 650px) {
            body {
                padding: 10px;
            }

            .login-container {
                padding: 30px 25px;
                margin: 10px;
            }

            .logo {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            h1 {
                font-size: 22px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 5px;
            }

            .login-container {
                padding: 25px 20px;
                margin: 5px;
            }

            input[type="email"],
            input[type="password"],
            .login-btn {
                padding: 14px 12px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Server Status Overlay -->
    <div id="serverStatusOverlay" class="server-status-overlay">
        <div class="server-status-content">
            <div class="server-status-spinner"></div>
            <div class="server-status-title">Connecting to Server</div>
            <div class="server-status-message">
                Please wait while we check the server status...<br>
                This may take a few moments.
            </div>
            <div id="serverStatusError" class="server-status-error" style="display: none;">
                <strong>Server Connection Failed</strong><br>
                Unable to connect to the server. Please check your internet connection or try again later.
                <button id="retryServerCheck" class="retry-button">Retry Connection</button>
            </div>
        </div>
    </div>

    <div class="login-container" id="loginContainer">
        <div class="logo">S</div>
        <h1>Shans System</h1>
        <div class="subtitle">
            Please sign in to your account<br>
            <strong>Note:</strong> Admins will be automatically redirected to the admin dashboard
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                Sign In
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Signing you in...</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div class="footer">
            <p>Shans System &copy; 2024</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://shans-backend.onrender.com/api';

        // Server status elements
        const serverStatusOverlay = document.getElementById('serverStatusOverlay');
        const serverStatusError = document.getElementById('serverStatusError');
        const retryServerCheckBtn = document.getElementById('retryServerCheck');
        const loginContainer = document.getElementById('loginContainer');

        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // Server status check functions
        async function checkServerStatus() {
            try {
                // Show the overlay and disable the login form
                showServerStatusOverlay();

                // Try to ping the server with a simple health check
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    // Server is up and running
                    hideServerStatusOverlay();
                    enableLoginForm();
                    // Check auth status after server is confirmed to be running
                    checkAuthStatus();
                } else {
                    throw new Error('Server responded with error');
                }

            } catch (error) {
                console.error('Server status check failed:', error);

                // If the health endpoint doesn't exist, try a different endpoint
                try {
                    const fallbackResponse = await fetch(`${API_BASE_URL}/products`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    // Even if it returns 401 or other error, it means server is running
                    hideServerStatusOverlay();
                    enableLoginForm();
                    checkAuthStatus();

                } catch (fallbackError) {
                    console.error('Fallback server check also failed:', fallbackError);
                    showServerStatusError();
                }
            }
        }

        function showServerStatusOverlay() {
            serverStatusOverlay.style.display = 'flex';
            serverStatusError.style.display = 'none';
            loginContainer.classList.add('login-form-disabled');
        }

        function hideServerStatusOverlay() {
            serverStatusOverlay.style.display = 'none';
            loginContainer.classList.remove('login-form-disabled');
        }

        function showServerStatusError() {
            serverStatusError.style.display = 'block';
        }

        function enableLoginForm() {
            loginContainer.classList.remove('login-form-disabled');
        }

        // Retry server check when button is clicked
        if (retryServerCheckBtn) {
            retryServerCheckBtn.addEventListener('click', function() {
                serverStatusError.style.display = 'none';
                checkServerStatus();
            });
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        function setLoading(isLoading) {
            if (isLoading) {
                loginForm.style.display = 'none';
                loading.style.display = 'block';
                loginBtn.disabled = true;
            } else {
                loginForm.style.display = 'block';
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        }

        // Check if user is already logged in
        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (token && userInfo) {
                // Verify token with backend
                fetch(`${API_BASE_URL}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error('Token invalid');
                    }
                })
                .then(data => {
                    // User is already authenticated, redirect based on role
                    if (data.user.is_admin) {
                        window.location.href = 'Admin/index.html';
                    } else {
                        window.location.href = 'index.html';
                    }
                })
                .catch(error => {
                    console.error('Auth check failed:', error);
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                });
            }
        }

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideMessages();
            setLoading(true);

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();

                if (response.ok) {
                    // Store authentication token and user info
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userInfo', JSON.stringify(data.user));

                    // Show success message based on user role
                    if (data.user.is_admin) {
                        showSuccess('Admin login successful! Redirecting to dashboard...');
                    } else {
                        showSuccess('Login successful! Redirecting...');
                    }

                    // Redirect after a short delay based on user role
                    setTimeout(() => {
                        if (data.user.is_admin) {
                            window.location.href = 'Admin/index.html';
                        } else {
                            window.location.href = 'index.html';
                        }
                    }, 1500);
                } else {
                    showError(data.message || 'Login failed');
                    setLoading(false);
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Network error. Please try again.');
                setLoading(false);
            }
        });

        // Check server status on page load (which will then check auth status)
        window.addEventListener('load', checkServerStatus);
    </script>
</body>
</html>
