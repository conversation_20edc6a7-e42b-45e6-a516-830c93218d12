[{"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\backup-files\\page.tsx": "1", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\expenses\\page.tsx": "2", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\low-stock\\page.tsx": "3", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-products\\page.tsx": "4", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-rooms\\page.tsx": "5", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-users\\page.tsx": "6", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\page.tsx": "7", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\sales-history\\page.tsx": "8", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\convert-to-receipt\\page.tsx": "9", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\invoice\\page.tsx": "10", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx": "11", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\login\\page.tsx": "12", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx": "13", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\quotation\\page.tsx": "14", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\receipt\\page.tsx": "15", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\LoadingScreen.tsx": "16", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\ProtectedRoute.tsx": "17", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\Toast.tsx": "18", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx": "19", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\hooks\\useApi.ts": "20", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\lib\\api.ts": "21", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\lib\\auth-utils.ts": "22", "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\types\\index.ts": "23"}, {"size": 1537, "mtime": 1751028635343, "results": "24", "hashOfConfig": "25"}, {"size": 1538, "mtime": 1751028648358, "results": "26", "hashOfConfig": "25"}, {"size": 11086, "mtime": 1751029209945, "results": "27", "hashOfConfig": "25"}, {"size": 26115, "mtime": 1751027862223, "results": "28", "hashOfConfig": "25"}, {"size": 9413, "mtime": 1751027937488, "results": "29", "hashOfConfig": "25"}, {"size": 1552, "mtime": 1751028660187, "results": "30", "hashOfConfig": "25"}, {"size": 5556, "mtime": 1751027699235, "results": "31", "hashOfConfig": "25"}, {"size": 9796, "mtime": 1751028016517, "results": "32", "hashOfConfig": "25"}, {"size": 9443, "mtime": 1751026760608, "results": "33", "hashOfConfig": "25"}, {"size": 15769, "mtime": 1751027037214, "results": "34", "hashOfConfig": "25"}, {"size": 775, "mtime": 1750955469268, "results": "35", "hashOfConfig": "25"}, {"size": 7448, "mtime": 1750955456619, "results": "36", "hashOfConfig": "25"}, {"size": 25171, "mtime": 1751025148105, "results": "37", "hashOfConfig": "25"}, {"size": 15388, "mtime": 1751026836502, "results": "38", "hashOfConfig": "25"}, {"size": 15432, "mtime": 1751026947738, "results": "39", "hashOfConfig": "25"}, {"size": 1354, "mtime": 1751024484576, "results": "40", "hashOfConfig": "25"}, {"size": 1435, "mtime": 1750955418850, "results": "41", "hashOfConfig": "25"}, {"size": 1626, "mtime": 1751024471814, "results": "42", "hashOfConfig": "25"}, {"size": 3137, "mtime": 1750955407700, "results": "43", "hashOfConfig": "25"}, {"size": 7208, "mtime": 1751025114105, "results": "44", "hashOfConfig": "25"}, {"size": 6695, "mtime": 1751025079472, "results": "45", "hashOfConfig": "25"}, {"size": 6373, "mtime": 1750955388154, "results": "46", "hashOfConfig": "25"}, {"size": 2003, "mtime": 1751027897016, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zc9k49", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\backup-files\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\expenses\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\low-stock\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-products\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-rooms\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-users\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\sales-history\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\convert-to-receipt\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\invoice\\page.tsx", ["117"], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx", ["118", "119"], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\quotation\\page.tsx", ["120"], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\receipt\\page.tsx", ["121"], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\LoadingScreen.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\components\\Toast.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\hooks\\useApi.ts", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\lib\\auth-utils.ts", [], [], "C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\types\\index.ts", [], [], {"ruleId": "122", "severity": 1, "message": "123", "line": 216, "column": 6, "nodeType": "124", "endLine": 216, "endColumn": 8, "suggestions": "125"}, {"ruleId": "122", "severity": 1, "message": "126", "line": 104, "column": 6, "nodeType": "124", "endLine": 104, "endColumn": 34, "suggestions": "127"}, {"ruleId": "122", "severity": 1, "message": "128", "line": 260, "column": 6, "nodeType": "124", "endLine": 260, "endColumn": 8, "suggestions": "129"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 217, "column": 6, "nodeType": "124", "endLine": 217, "endColumn": 8, "suggestions": "130"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 216, "column": 6, "nodeType": "124", "endLine": 216, "endColumn": 8, "suggestions": "131"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadOrderData'. Either include it or remove the dependency array.", "ArrayExpression", ["132"], "React Hook useCallback has a missing dependency: 'displayFilteredProducts'. Either include it or remove the dependency array.", ["133"], "React Hook useEffect has a missing dependency: 'initializeApp'. Either include it or remove the dependency array.", ["134"], ["135"], ["136"], {"desc": "137", "fix": "138"}, {"desc": "139", "fix": "140"}, {"desc": "141", "fix": "142"}, {"desc": "137", "fix": "143"}, {"desc": "137", "fix": "144"}, "Update the dependencies array to be: [loadOrderData]", {"range": "145", "text": "146"}, "Update the dependencies array to be: [displayFilteredProducts]", {"range": "147", "text": "148"}, "Update the dependencies array to be: [initializeApp]", {"range": "149", "text": "150"}, {"range": "151", "text": "146"}, {"range": "152", "text": "146"}, [6796, 6798], "[loadOrderData]", [3748, 3776], "[displayFilteredProducts]", [8001, 8003], "[initializeApp]", [6877, 6879], [6796, 6798]]