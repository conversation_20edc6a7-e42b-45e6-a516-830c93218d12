(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{283:(e,r,t)=>{"use strict";t.d(r,{A:()=>n,AuthProvider:()=>i});var a=t(5155),s=t(2115),o=t(2799);let l=(0,s.createContext)(void 0);function n(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:r}=e,[t,n]=(0,s.useState)(null),[i,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(!1),m=async()=>{try{if(c(!0),!await (0,o.z)({redirectOnFail:!1}))return n(null),u(!1),!1;{let e=(0,o.HW)();return n(e),u(!0),!0}}catch(e){return console.error("Auth check failed:",e),n(null),u(!1),!1}finally{c(!1)}},h=async(e,r)=>{try{c(!0);let t=await fetch("".concat(o.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),a=await t.json();if(t.ok)return o.IG.set("authToken",a.token),o.IG.set("userInfo",JSON.stringify(a.user)),o.IG.set("lastAuthCheck",Date.now().toString()),n(a.user),u(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{c(!1)}};return(0,s.useEffect)(()=>{m()},[]),(0,a.jsx)(l.Provider,{value:{user:t,isLoading:i,isAuthenticated:d,login:h,logout:()=>{n(null),u(!1),(0,o.ri)()},checkAuth:m},children:r})}},1592:(e,r,t)=>{Promise.resolve().then(t.bind(t,3792))},2799:(e,r,t)=>{"use strict";t.d(r,{HW:()=>l,IG:()=>s,JR:()=>a,ri:()=>n,z:()=>o});let a="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(r){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,r){try{localStorage.setItem(e,r)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,r)}},remove:function(e){try{localStorage.removeItem(e)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:r=!0,showLoading:t=!1,retryCount:o=2,timeout:l=8e3}=e,n=s.get("authToken"),i=s.get("userInfo");if(!n)return r&&(window.location.href="/login"),!1;if(i)try{JSON.parse(i);let e=s.get("lastAuthCheck"),r=Date.now();if(e&&r-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=o;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),l),o="?_t=".concat(Date.now()),i=await fetch("".concat(a,"/auth/me").concat(o),{headers:{Authorization:"Bearer ".concat(n)},signal:e.signal});if(clearTimeout(t),i.ok){let e=await i.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===i.status||403===i.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),r&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(i.status))}catch(t){if(console.warn("Auth check attempt ".concat(e+1," failed:"),t),e===o){if(i)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),r&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(r=>setTimeout(r,1e3*Math.pow(2,e)))}return!1}function l(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function n(){let e=s.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},3792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),s=t(2115),o=t(5695),l=t(283),n=t(9053),i=t(7960),c=t(7389),d=t(2799);function u(){let e,r,t,{user:u,logout:m}=(0,l.A)();(0,o.useRouter)();let{messages:h,showToast:b,removeToast:x}=(0,c.d)(),[g,p]=(0,s.useState)([]),[f,y]=(0,s.useState)([]),[v,j]=(0,s.useState)(!0),[N,w]=(0,s.useState)(""),[S,k]=(0,s.useState)(1),[C,A]=(0,s.useState)(!0),[P,T]=(0,s.useState)(1),[_,R]=(0,s.useState)(""),[E,I]=(0,s.useState)(""),[F,O]=(0,s.useState)(""),[q,G]=(0,s.useState)(""),[L,z]=(0,s.useState)(""),[M,D]=(0,s.useState)(""),[J,B]=(0,s.useState)(!0),[Q,H]=(0,s.useState)(""),[W,Y]=(0,s.useState)(""),[U,V]=(0,s.useState)(""),[K,X]=(0,s.useState)(""),[Z,$]=(0,s.useState)(""),[ee,er]=(0,s.useState)(1),[et,ea]=(0,s.useState)(0),[es,eo]=(0,s.useState)(""),[el,en]=(0,s.useState)(!0),[ei,ec]=(0,s.useState)("Cash"),[ed,eu]=(0,s.useState)(""),[em,eh]=(0,s.useState)([]),[eb,ex]=(0,s.useState)(!1),[eg,ep]=(0,s.useState)(null),[ef,ey]=(0,s.useState)(!1),ev=(0,s.useRef)(),ej=(0,s.useCallback)(()=>{ev.current&&clearTimeout(ev.current),ev.current=setTimeout(()=>{ew()},300)},[Z,g]),eN=async()=>{try{j(!0);let e=new AbortController,r=setTimeout(()=>e.abort(),5e3),t="&_t=".concat(Date.now()),a=await fetch("".concat(API_BASE_URL,"/products?page=").concat(S,"&limit=").concat(20).concat(t),{signal:e.signal});if(clearTimeout(r),!a.ok)throw Error("HTTP error! status: ".concat(a.status));let s=await a.json();s.length<20&&A(!1);let o=new Set(g.map(e=>e.item_code)),l=s.filter(e=>!o.has(e.item_code));p(e=>[...e,...l]),k(e=>e+1)}catch(e){throw console.error("Error fetching products:",e),e instanceof Error&&"AbortError"===e.name?w("Request timed out. Please check your connection."):w("Failed to load products. Please try again."),e}finally{j(!1)}},ew=()=>{let e=Z.toLowerCase().trim();if(0===e.length){eh([]),ex(!1);return}eh(g.filter(r=>r.item_name&&r.item_name.toLowerCase().includes(e))),ex(!0)},eS=async()=>{try{j(!0),w(""),await eN();let e=d.IG.get("selectedCompany");if(e)try{let r=JSON.parse(e);R(r.key)}catch(e){console.warn("Error parsing saved company:",e)}ek()}catch(e){console.error("Initialization error:",e),w("Failed to initialize the application. Please try again.")}finally{j(!1)}},ek=()=>{};(0,s.useEffect)(()=>{eS()},[]),(0,s.useEffect)(()=>{ej()},[Z,ej]);let eC=(e=0,r=0,t=0,f.forEach(a=>{el?(e+=.85*a.price*a.quantity,r+=.15*a.price*a.quantity):e+=a.price*a.quantity,t+=a.price*a.quantity}),el||(r=0),{subtotal:Math.round(e),tax:Math.round(r),total:Math.round(t)});return(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)(i.A,{isVisible:v,error:N,onRetry:()=>{w(""),p([]),k(1),A(!0),eS()}}),(0,a.jsx)(c.A,{messages:h,onRemove:x}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-5 shadow-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-5 flex-wrap gap-4",children:[(0,a.jsx)("h1",{className:"text-gray-800 text-2xl font-normal m-0",children:"Receipt and Quotation Generator"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,a.jsx)("a",{href:"/convert-to-receipt",className:"bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors",children:"Convert to Receipt"}),(null==u?void 0:u.is_admin)&&(0,a.jsx)("a",{href:"/admin",className:"bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors",children:"Admin Dashboard"}),(0,a.jsx)("button",{onClick:m,className:"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors",children:"Logout"})]})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Select Company"}),(0,a.jsx)("label",{htmlFor:"companySelect",className:"block mb-1",children:"Choose a company:"}),(0,a.jsxs)("select",{id:"companySelect",value:_,onChange:e=>R(e.target.value),required:!0,className:"w-full p-2 border border-gray-300 rounded box-border",children:[(0,a.jsx)("option",{value:"",disabled:!0,children:"Select a company"}),(0,a.jsx)("option",{value:"company1",children:"Shans Accessories PTY LTD"}),(0,a.jsx)("option",{value:"company2",children:"Shans Autosport PTY LTD"}),(0,a.jsx)("option",{value:"company3",children:"Shans Motorstyle PTY LTD"})]})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Customer Information"}),(0,a.jsx)("label",{htmlFor:"customerName",className:"block mb-1",children:"Name:"}),(0,a.jsx)("input",{type:"text",id:"customerName",value:E,onChange:e=>I(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"customerEmail",className:"block mb-1",children:"Email:"}),(0,a.jsx)("input",{type:"email",id:"customerEmail",value:F,onChange:e=>O(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"customerAddress",className:"block mb-1",children:"Address:"}),(0,a.jsx)("input",{type:"text",id:"customerAddress",value:q,onChange:e=>G(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"customerPhone",className:"block mb-1",children:"Phone:"}),(0,a.jsx)("input",{type:"tel",id:"customerPhone",value:L,onChange:e=>z(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"salespersonName",className:"block mb-1",children:"Salesperson Name:"}),(0,a.jsx)("input",{type:"text",id:"salespersonName",value:M,onChange:e=>D(e.target.value),placeholder:"Enter your name...",className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Shipping Information"}),(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:J,onChange:e=>B(e.target.checked),className:"mr-2"}),"Same as billing address"]})}),!J&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"shippingName",className:"block mb-1",children:"Name:"}),(0,a.jsx)("input",{type:"text",id:"shippingName",value:Q,onChange:e=>H(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"shippingEmail",className:"block mb-1",children:"Email:"}),(0,a.jsx)("input",{type:"email",id:"shippingEmail",value:W,onChange:e=>Y(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"shippingAddress",className:"block mb-1",children:"Address:"}),(0,a.jsx)("input",{type:"text",id:"shippingAddress",value:U,onChange:e=>V(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,a.jsx)("label",{htmlFor:"shippingPhone",className:"block mb-1",children:"Phone:"}),(0,a.jsx)("input",{type:"tel",id:"shippingPhone",value:K,onChange:e=>X(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"})]})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Add Products"}),(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)("label",{htmlFor:"productSearch",className:"block mb-1",children:"Search Products:"}),(0,a.jsx)("input",{type:"text",id:"productSearch",value:Z,onChange:e=>$(e.target.value),placeholder:"Type to search products...",className:"w-full p-2 border border-gray-300 rounded box-border"}),eb&&em.length>0&&(0,a.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg",children:em.map(e=>(0,a.jsxs)("div",{className:"p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>{ep(e),$(e.item_name),ea(e.unit_retail_price),eo(e.room_name),ex(!1)},children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:e.item_name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Code: ",e.item_code," | Price: ","R",e.unit_retail_price," | Stock: ",e.available_stock," | Room: ",e.room_name]})]},e.item_code))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"quantity",className:"block mb-1",children:"Quantity:"}),(0,a.jsx)("input",{type:"number",id:"quantity",value:ee,onChange:e=>er(Math.max(1,parseInt(e.target.value)||1)),min:"1",className:"w-full p-2 border border-gray-300 rounded box-border"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"price",className:"block mb-1",children:["Price (","R","):"]}),(0,a.jsx)("input",{type:"number",id:"price",value:et,onChange:e=>ea(parseFloat(e.target.value)||0),min:"0",step:"0.01",className:"w-full p-2 border border-gray-300 rounded box-border"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"room",className:"block mb-1",children:"Room:"}),(0,a.jsx)("input",{type:"text",id:"room",value:es,onChange:e=>eo(e.target.value),className:"w-full p-2 border border-gray-300 rounded box-border"})]})]}),(0,a.jsx)("button",{onClick:()=>{if(!Z.trim())return void b("Please enter a product name","error");if(et<=0)return void b("Please enter a valid price","error");if(ee<=0)return void b("Please enter a valid quantity","error");let e={item_code:(null==eg?void 0:eg.item_code)||"NEW_".concat(P),name:Z.trim(),room_name:es.trim()||"N/A",quantity:ee,price:et,tax_per_product:el?.15*et:0,is_new:!eg};y(r=>[...r,e]),$(""),er(1),ea(0),eo(""),ep(null),ex(!1),eg||T(e=>e+1),b("Product added to cart","success")},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Add Product"})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Selected Products"}),0===f.length?(0,a.jsx)("p",{className:"text-gray-600",children:"No products selected yet."}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-100",children:[(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Product"}),(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Room"}),(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Qty"}),(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Price"}),(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Total"}),(0,a.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:f.map((e,r)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"border border-gray-300 p-2",children:e.name}),(0,a.jsx)("td",{className:"border border-gray-300 p-2",children:e.room_name}),(0,a.jsx)("td",{className:"border border-gray-300 p-2",children:e.quantity}),(0,a.jsxs)("td",{className:"border border-gray-300 p-2",children:["R",e.price]}),(0,a.jsxs)("td",{className:"border border-gray-300 p-2",children:["R",e.price*e.quantity]}),(0,a.jsx)("td",{className:"border border-gray-300 p-2",children:(0,a.jsx)("button",{onClick:()=>{y(e=>e.filter((e,t)=>t!==r)),b("Product removed","info")},className:"bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors",children:"Remove"})})]},r))})]})})]}),(0,a.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,a.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Order Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"paymentMethod",className:"block mb-1",children:"Payment Method:"}),(0,a.jsxs)("select",{id:"paymentMethod",value:ei,onChange:e=>ec(e.target.value),className:"w-full p-2 border border-gray-300 rounded box-border",children:[(0,a.jsx)("option",{value:"Cash",children:"Cash"}),(0,a.jsx)("option",{value:"Card",children:"Card"}),(0,a.jsx)("option",{value:"Bank Transfer",children:"Bank Transfer"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:el,onChange:e=>en(e.target.checked),className:"mr-2"}),"Include Tax (15%)"]})})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{htmlFor:"comments",className:"block mb-1",children:"Comments:"}),(0,a.jsx)("textarea",{id:"comments",value:ed,onChange:e=>eu(e.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded box-border resize-vertical",placeholder:"Additional comments or notes..."})]}),(0,a.jsxs)("div",{className:"mt-4 bg-gray-100 p-3 rounded",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{children:"Subtotal:"}),(0,a.jsxs)("span",{children:["R",eC.subtotal]})]}),el&&(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsx)("span",{children:"Tax (15%):"}),(0,a.jsxs)("span",{children:["R",eC.tax]})]}),(0,a.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t border-gray-300 pt-2",children:[(0,a.jsx)("span",{children:"Total:"}),(0,a.jsxs)("span",{children:["R",eC.total]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 justify-center",children:[(0,a.jsx)("button",{onClick:()=>b("Generate Receipt functionality coming soon","info"),className:"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors",children:"Generate Receipt"}),(0,a.jsx)("button",{onClick:()=>b("Generate Quotation functionality coming soon","info"),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"Generate Quotation"}),(0,a.jsx)("button",{onClick:()=>b("Generate Invoice functionality coming soon","info"),className:"bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors",children:"Generate Invoice"})]})]})]})})}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},7389:(e,r,t)=>{"use strict";t.d(r,{A:()=>l,d:()=>n});var a=t(5155),s=t(2115);function o(e){let{message:r,onRemove:t}=e;(0,s.useEffect)(()=>{let e=setTimeout(()=>{t(r.id)},3e3);return()=>clearTimeout(e)},[r.id,t]);let o={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[r.type];return(0,a.jsx)("div",{className:"".concat(o," text-white px-4 py-2 rounded opacity-95 text-sm toast"),children:r.message})}function l(e){let{messages:r,onRemove:t}=e;return(0,a.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:r.map(e=>(0,a.jsx)(o,{message:e,onRemove:t},e.id))})}function n(){let[e,r]=(0,s.useState)([]);return{messages:e,showToast:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",a=Math.random().toString(36).substr(2,9);r(r=>[...r,{id:a,message:e,type:t}])},removeToast:e=>{r(r=>r.filter(r=>r.id!==e))}}}},7960:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});var a=t(5155);function s(e){let{isVisible:r,message:t="Loading products, please wait...",error:s,onRetry:o}=e;return r?(0,a.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,a.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:s?"Connection Error":t}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:s}),o&&(0,a.jsx)("button",{onClick:o,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},9053:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(5155),s=t(2115),o=t(5695),l=t(283);function n(e){let{children:r,requireAdmin:t=!1,redirectTo:n="/login"}=e,{user:i,isLoading:c,isAuthenticated:d}=(0,l.A)(),u=(0,o.useRouter)(),[m,h]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(!c){if(!d)return void u.push(n);if(t&&i&&!i.is_admin)return void u.push("/");h(!0)}},[d,c,i,t,u,n]),c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):m?(0,a.jsx)(a.Fragment,{children:r}):null}}},e=>{var r=r=>e(e.s=r);e.O(0,[441,684,358],()=>r(1592)),_N_E=e.O()}]);