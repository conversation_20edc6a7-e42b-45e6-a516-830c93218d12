{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-client-pages-loader.ts"], "sourcesContent": ["import { stringifyRequest } from '../stringify-request'\n\nexport type ClientPagesLoaderOptions = {\n  absolutePagePath: string\n  page: string\n}\n\n// this parameter: https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\nfunction nextClientPagesLoader(this: any) {\n  const pagesLoaderSpan = this.currentTraceSpan.traceChild(\n    'next-client-pages-loader'\n  )\n\n  return pagesLoaderSpan.traceFn(() => {\n    const { absolutePagePath, page } =\n      this.getOptions() as ClientPagesLoaderOptions\n\n    pagesLoaderSpan.setAttribute('absolutePagePath', absolutePagePath)\n\n    const stringifiedPageRequest = stringifyRequest(this, absolutePagePath)\n    const stringifiedPage = JSON.stringify(page)\n\n    return `\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      ${stringifiedPage},\n      function () {\n        return require(${stringifiedPageRequest});\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([${stringifiedPage}])\n      });\n    }\n  `\n  })\n}\n\nexport default nextClientPagesLoader\n"], "names": ["nextClientPagesLoader", "pagesLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "absolutePagePath", "page", "getOptions", "setAttribute", "stringifiedPageRequest", "stringifyRequest", "stringifiedPage", "JSON", "stringify"], "mappings": ";;;;+BAsCA;;;eAAA;;;kCAtCiC;AAOjC,8FAA8F;AAC9F,SAASA;IACP,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CACtD;IAGF,OAAOF,gBAAgBG,OAAO,CAAC;QAC7B,MAAM,EAAEC,gBAAgB,EAAEC,IAAI,EAAE,GAC9B,IAAI,CAACC,UAAU;QAEjBN,gBAAgBO,YAAY,CAAC,oBAAoBH;QAEjD,MAAMI,yBAAyBC,IAAAA,kCAAgB,EAAC,IAAI,EAAEL;QACtD,MAAMM,kBAAkBC,KAAKC,SAAS,CAACP;QAEvC,OAAO,CAAC;;MAEN,EAAEK,gBAAgB;;uBAED,EAAEF,uBAAuB;;;;;8BAKlB,EAAEE,gBAAgB;;;EAG9C,CAAC;IACD;AACF;MAEA,WAAeX"}