(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[800],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,AuthProvider:()=>i});var a=r(5155),s=r(2115),n=r(2799);let l=(0,s.createContext)(void 0);function o(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:t}=e,[r,o]=(0,s.useState)(null),[i,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(!1),m=async()=>{try{if(c(!0),!await (0,n.z)({redirectOnFail:!1}))return o(null),u(!1),!1;{let e=(0,n.HW)();return o(e),u(!0),!0}}catch(e){return console.error("Auth check failed:",e),o(null),u(!1),!1}finally{c(!1)}},h=async(e,t)=>{try{c(!0);let r=await fetch("".concat(n.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return n.IG.set("authToken",a.token),n.IG.set("userInfo",JSON.stringify(a.user)),n.IG.set("lastAuthCheck",Date.now().toString()),o(a.user),u(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{c(!1)}};return(0,s.useEffect)(()=>{m()},[]),(0,a.jsx)(l.Provider,{value:{user:r,isLoading:i,isAuthenticated:d,login:h,logout:()=>{o(null),u(!1),(0,n.ri)()},checkAuth:m},children:t})}},500:(e,t,r)=>{Promise.resolve().then(r.bind(r,1114))},1114:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(5155),s=r(2115),n=r(5695),l=r(9053),o=r(7960),i=r(3213);function c(){let e=(0,n.useRouter)(),[t,r]=(0,s.useState)(""),[c,d]=(0,s.useState)(""),[u,m]=(0,s.useState)(""),{data:h=[],loading:x,error:g}=(0,i.U9)("/sales"),f=h.filter(e=>{let r=e.customer_name.toLowerCase().includes(t.toLowerCase())||e.order_id.toLowerCase().includes(t.toLowerCase())||e.salesperson_name.toLowerCase().includes(t.toLowerCase()),a=!c||e.created_at.startsWith(c),s=!u||e.payment_method===u;return r&&a&&s}),p=f.reduce((e,t)=>e+t.total_amount,0),y=f.length>0?p/f.length:0,b=[...new Set(h.map(e=>e.payment_method))];return x?(0,a.jsx)(l.A,{requireAdmin:!0,children:(0,a.jsx)(o.A,{isVisible:!0,message:"Loading sales history..."})}):(0,a.jsx)(l.A,{requireAdmin:!0,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Sales History"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Total Sales"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["R ",p.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Number of Orders"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:f.length})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Average Order"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["R ",y.toLocaleString()]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Period"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:c||"All Time"})]})]}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search by customer, order ID, or salesperson...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:c,onChange:e=>d(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,a.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Methods"}),b.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)("button",{onClick:()=>{r(""),d(""),m("")},className:"w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Clear Filters"})})]})}),f.length>0?(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salesperson"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment Method"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.order_id}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.customer_name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.salesperson_name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.payment_method})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["R ",e.total_amount.toLocaleString()]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900 mr-3",children:"View Details"}),(0,a.jsx)("button",{className:"text-green-600 hover:text-green-900",children:"Export"})]})]},e.id))})]})})}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCA"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Sales Records Found"}),(0,a.jsx)("p",{className:"text-gray-500",children:t||c||u?"No sales records match your current filters.":"No sales records are available yet."})]})]})})})}},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>l,IG:()=>s,JR:()=>a,ri:()=>o,z:()=>n});let a="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:n=2,timeout:l=8e3}=e,o=s.get("authToken"),i=s.get("userInfo");if(!o)return t&&(window.location.href="/login"),!1;if(i)try{JSON.parse(i);let e=s.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),l),n="?_t=".concat(Date.now()),i=await fetch("".concat(a,"/auth/me").concat(n),{headers:{Authorization:"Bearer ".concat(o)},signal:e.signal});if(clearTimeout(r),i.ok){let e=await i.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===i.status||403===i.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(i.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===n){if(i)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function l(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function o(){let e=s.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},3213:(e,t,r)=>{"use strict";r.d(t,{L2:()=>l,U9:()=>n,gf:()=>s});var a=r(2115);function s(e){let[t,r]=(0,a.useState)({data:null,loading:!1,error:null}),s=(0,a.useCallback)(async function(){for(var t=arguments.length,a=Array(t),s=0;s<t;s++)a[s]=arguments[s];r(e=>({...e,loading:!0,error:null}));try{let t=await e(...a);if(t.success&&t.data)return r({data:t.data,loading:!1,error:null}),t.data;return r({data:null,loading:!1,error:t.error||"An unknown error occurred"}),null}catch(e){return r({data:null,loading:!1,error:e instanceof Error?e.message:"An unknown error occurred"}),null}},[e]),n=(0,a.useCallback)(()=>{r({data:null,loading:!1,error:null})},[]);return{...t,execute:s,reset:n}}function n(e){let t=s(e),r=(0,a.useCallback)(e=>{t.data&&i(t=>({...t,data:t.data?[...t.data,...e]:e}))},[t.data]),n=(0,a.useCallback)((e,t)=>{i(r=>{if(!r.data)return r;let a=[...r.data];return a[e]=t,{...r,data:a}})},[]),l=(0,a.useCallback)(e=>{i(t=>{if(!t.data)return t;let r=t.data.filter((t,r)=>r!==e);return{...t,data:r}})},[]),[o,i]=(0,a.useState)({data:null,loading:!1,error:null});return{...t,append:r,updateItem:n,removeItem:l}}function l(e){let[t,r]=(0,a.useState)(!1),[s,n]=(0,a.useState)(null);return{mutate:(0,a.useCallback)(async t=>{r(!0),n(null);try{let a=await e(t);if(a.success&&a.data)return r(!1),a.data;return n(a.error||"An unknown error occurred"),r(!1),null}catch(e){return n(e instanceof Error?e.message:"An unknown error occurred"),r(!1),null}},[e]),loading:t,error:s,reset:(0,a.useCallback)(()=>{r(!1),n(null)},[])}}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},7960:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(5155);function s(e){let{isVisible:t,message:r="Loading products, please wait...",error:s,onRetry:n}=e;return t?(0,a.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,a.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:s?"Connection Error":r}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:s}),n&&(0,a.jsx)("button",{onClick:n,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(5155),s=r(2115),n=r(5695),l=r(283);function o(e){let{children:t,requireAdmin:r=!1,redirectTo:o="/login"}=e,{user:i,isLoading:c,isAuthenticated:d}=(0,l.A)(),u=(0,n.useRouter)(),[m,h]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(!c){if(!d)return void u.push(o);if(r&&i&&!i.is_admin)return void u.push("/");h(!0)}},[d,c,i,r,u,o]),c)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):m?(0,a.jsx)(a.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(500)),_N_E=e.O()}]);