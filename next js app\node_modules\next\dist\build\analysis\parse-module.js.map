{"version": 3, "sources": ["../../../src/build/analysis/parse-module.ts"], "sourcesContent": ["import { LRUCache } from '../../server/lib/lru-cache'\nimport { withPromiseCache } from '../../lib/with-promise-cache'\nimport { createHash } from 'crypto'\nimport { parse } from '../swc'\n\n/**\n * Parses a module with SWC using an LRU cache where the parsed module will\n * be indexed by a sha of its content holding up to 500 entries.\n */\nexport const parseModule = withPromiseCache(\n  new LRUCache<any>(500),\n  async (filename: string, content: string) =>\n    parse(content, { isModule: 'unknown', filename }).catch(() => null),\n  (_, content) => createHash('sha1').update(content).digest('hex')\n)\n"], "names": ["parseModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "L<PERSON><PERSON><PERSON>", "filename", "content", "parse", "isModule", "catch", "_", "createHash", "update", "digest"], "mappings": ";;;;+BASaA;;;eAAAA;;;0BATY;kCACQ;wBACN;qBACL;AAMf,MAAMA,cAAcC,IAAAA,kCAAgB,EACzC,IAAIC,kBAAQ,CAAM,MAClB,OAAOC,UAAkBC,UACvBC,IAAAA,UAAK,EAACD,SAAS;QAAEE,UAAU;QAAWH;IAAS,GAAGI,KAAK,CAAC,IAAM,OAChE,CAACC,GAAGJ,UAAYK,IAAAA,kBAAU,EAAC,QAAQC,MAAM,CAACN,SAASO,MAAM,CAAC"}