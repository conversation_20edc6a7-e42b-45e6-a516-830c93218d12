(()=>{var e={};e.id=9,e.ids=[9],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(687);function o({isVisible:e,message:t="Loading products, please wait...",error:r,onRetry:o}){return e?(0,s.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,s.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:r?"Connection Error":t}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:r}),o&&(0,s.jsx)("button",{onClick:o,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},2661:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\manage-rooms\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-rooms\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6461:(e,t,r)=>{Promise.resolve().then(r.bind(r,8111))},8111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(687),o=r(3210),a=r(6189),n=r(769),i=r(854),d=r(9265);function l(){let e=(0,a.useRouter)(),[t,r]=(0,o.useState)(""),[l,m]=(0,o.useState)(!1),[x,p]=(0,o.useState)(!1),[u,h]=(0,o.useState)(null),{data:b=[],loading:g,error:f,refetch:j}=(0,d.U9)("/rooms"),v=(0,d.L2)("/rooms","POST"),y=(0,d.L2)("/rooms","PUT"),w=(0,d.L2)("/rooms","DELETE"),N=b.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.description&&e.description.toLowerCase().includes(t.toLowerCase())),C=async e=>{try{await v.mutate(e),m(!1),j()}catch(e){console.error("Error adding room:",e)}},k=async e=>{if(u)try{await y.mutate(e,{endpoint:`/rooms/${u.id}`}),p(!1),h(null),j()}catch(e){console.error("Error updating room:",e)}},A=async e=>{if(confirm("Are you sure you want to delete this room? This action cannot be undone."))try{await w.mutate(void 0,{endpoint:`/rooms/${e}`}),j()}catch(e){console.error("Error deleting room:",e)}},P=e=>{h(e),p(!0)};return g?(0,s.jsx)(n.A,{requireAdmin:!0,children:(0,s.jsx)(i.A,{isVisible:!0,message:"Loading rooms..."})}):(0,s.jsx)(n.A,{requireAdmin:!0,children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Rooms"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[(0,s.jsxs)("button",{onClick:()=>m(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,s.jsx)("span",{children:"+"})," Add Room"]}),(0,s.jsx)("div",{className:"w-full sm:w-auto",children:(0,s.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search rooms...",className:"w-full sm:w-64 border border-gray-300 rounded-md px-3 py-2 text-sm"})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>P(e),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Edit"}),(0,s.jsx)("button",{onClick:()=>A(e.id),className:"text-red-600 hover:text-red-800 text-sm",children:"Delete"})]})]}),e.description&&(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:e.description}),(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["Room ID: ",e.id]})})]},e.id))}),0===N.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-500",children:t?"No rooms found matching your search.":"No rooms available. Add your first room to get started."})}),l&&(0,s.jsx)(c,{isOpen:l,onClose:()=>m(!1),onSubmit:C,title:"Add Room"}),x&&u&&(0,s.jsx)(c,{isOpen:x,onClose:()=>{p(!1),h(null)},onSubmit:k,title:"Edit Room",initialData:u})]})})})}function c({isOpen:e,onClose:t,onSubmit:r,title:a,initialData:n}){let[i,d]=(0,o.useState)({name:n?.name||"",description:n?.description||""}),l=(e,t)=>{d(r=>({...r,[e]:t}))};return e?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsx)("div",{className:"bg-white rounded-lg max-w-md w-full",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:a}),(0,s.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 text-2xl",children:"\xd7"})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(i)},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room Name *"}),(0,s.jsx)("input",{type:"text",value:i.name,onChange:e=>l("name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,placeholder:"Enter room name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{value:i.description,onChange:e=>l("description",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",rows:3,placeholder:"Enter room description (optional)"})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,s.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[n?"Update":"Add"," Room"]})]})]})]})})}):null}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9197:(e,t,r)=>{Promise.resolve().then(r.bind(r,2661))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=r(5239),o=r(8088),a=r(8170),n=r.n(a),i=r(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["manage-rooms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2661)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-rooms\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-rooms\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/manage-rooms/page",pathname:"/admin/manage-rooms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[825,227],()=>r(9572));module.exports=s})();