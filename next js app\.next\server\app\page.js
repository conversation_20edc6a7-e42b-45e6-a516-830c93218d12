(()=>{var e={};e.id=974,e.ids=[974],e.modules={27:(e,r,t)=>{"use strict";t.d(r,{A:()=>n,d:()=>l});var s=t(687),a=t(3210);function o({message:e,onRemove:r}){let t={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[e.type];return(0,s.jsx)("div",{className:`${t} text-white px-4 py-2 rounded opacity-95 text-sm toast`,children:e.message})}function n({messages:e,onRemove:r}){return(0,s.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:e.map(e=>(0,s.jsx)(o,{message:e,onRemove:r},e.id))})}function l(){let[e,r]=(0,a.useState)([]);return{messages:e,showToast:(e,t="info")=>{let s=Math.random().toString(36).substr(2,9);r(r=>[...r,{id:s,message:e,type:t}])},removeToast:e=>{r(r=>r.filter(r=>r.id!==e))}}}},769:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(687),a=t(3210),o=t(6189),n=t(3213);function l({children:e,requireAdmin:r=!1,redirectTo:t="/login"}){let{user:l,isLoading:i,isAuthenticated:d}=(0,n.A)();(0,o.useRouter)();let[c,u]=(0,a.useState)(!1);return i?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):c?(0,s.jsx)(s.Fragment,{children:e}):null}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(687);function a({isVisible:e,message:r="Loading products, please wait...",error:t,onRetry:a}){return e?(0,s.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,s.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:t?"Connection Error":r}),t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:t}),a&&(0,s.jsx)("button",{onClick:a,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},1135:()=>{},1204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx","default")},1439:(e,r,t)=>{"use strict";t.d(r,{HW:()=>n,IG:()=>a,JR:()=>s,ri:()=>l,z:()=>o});let s="https://shans-backend.onrender.com/api",a={get:function(e){return null},set:function(e,r){},remove:function(e){}};async function o(e={}){let{redirectOnFail:r=!0,showLoading:t=!1,retryCount:n=2,timeout:l=8e3}=e,i=a.get("authToken"),d=a.get("userInfo");if(!i)return!1;if(d)try{JSON.parse(d);let e=a.get("lastAuthCheck"),r=Date.now();if(e&&r-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),l),t=`?_t=${Date.now()}`,o=await fetch(`${s}/auth/me${t}`,{headers:{Authorization:`Bearer ${i}`},signal:e.signal});if(clearTimeout(r),o.ok){let e=await o.json();return a.set("userInfo",JSON.stringify(e.user)),a.set("lastAuthCheck",Date.now().toString()),!0}if(401===o.status||403===o.status){a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${o.status}`)}catch(r){if(console.warn(`Auth check attempt ${e+1} failed:`,r),e===n){if(d)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),!1}await new Promise(r=>setTimeout(r,1e3*Math.pow(2,e)))}return!1}function n(){let e=a.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function l(){let e=a.get("authToken");e&&fetch(`${s}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck")}},2928:(e,r,t)=>{Promise.resolve().then(t.bind(t,1204))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,r,t)=>{"use strict";t.d(r,{A:()=>l,AuthProvider:()=>i});var s=t(687),a=t(3210),o=t(1439);let n=(0,a.createContext)(void 0);function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i({children:e}){let[r,t]=(0,a.useState)(null),[l,i]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),u=async()=>{try{if(i(!0),!await (0,o.z)({redirectOnFail:!1}))return t(null),c(!1),!1;{let e=(0,o.HW)();return t(e),c(!0),!0}}catch(e){return console.error("Auth check failed:",e),t(null),c(!1),!1}finally{i(!1)}},m=async(e,r)=>{try{i(!0);let s=await fetch(`${o.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),a=await s.json();if(s.ok)return o.IG.set("authToken",a.token),o.IG.set("userInfo",JSON.stringify(a.user)),o.IG.set("lastAuthCheck",Date.now().toString()),t(a.user),c(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{i(!1)}};return(0,s.jsx)(n.Provider,{value:{user:r,isLoading:l,isAuthenticated:d,login:m,logout:()=>{t(null),c(!1),(0,o.ri)()},checkAuth:u},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,r,t)=>{Promise.resolve().then(t.bind(t,3213))},4236:(e,r,t)=>{Promise.resolve().then(t.bind(t,9131))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>o});var s=t(7413);t(1135);var a=t(9131);let o={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function n({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,s.jsx)(a.AuthProvider,{children:e})})})}},5127:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},5226:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(5239),a=t(8088),o=t(8170),n=t.n(o),l=t(893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5694:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(687),a=t(3210),o=t(6189),n=t(3213),l=t(769),i=t(854),d=t(27),c=t(1439);function u(){let e,r,t,{user:u,logout:m}=(0,n.A)();(0,o.useRouter)();let{messages:h,showToast:p,removeToast:b}=(0,d.d)(),[x,g]=(0,a.useState)([]),[f,v]=(0,a.useState)([]),[y,j]=(0,a.useState)(!0),[N,w]=(0,a.useState)(""),[P,k]=(0,a.useState)(1),[C,S]=(0,a.useState)(!0),[A,_]=(0,a.useState)(1),[T,R]=(0,a.useState)(""),[E,I]=(0,a.useState)(""),[F,q]=(0,a.useState)(""),[G,U]=(0,a.useState)(""),[$,M]=(0,a.useState)(""),[O,z]=(0,a.useState)(""),[L,D]=(0,a.useState)(!0),[J,V]=(0,a.useState)(""),[Q,B]=(0,a.useState)(""),[H,W]=(0,a.useState)(""),[Y,K]=(0,a.useState)(""),[X,Z]=(0,a.useState)(""),[ee,er]=(0,a.useState)(1),[et,es]=(0,a.useState)(0),[ea,eo]=(0,a.useState)(""),[en,el]=(0,a.useState)(!0),[ei,ed]=(0,a.useState)("Cash"),[ec,eu]=(0,a.useState)(""),[em,eh]=(0,a.useState)([]),[ep,eb]=(0,a.useState)(!1),[ex,eg]=(0,a.useState)(null),[ef,ev]=(0,a.useState)(!1),ey=(0,a.useRef)();(0,a.useCallback)(()=>{ey.current&&clearTimeout(ey.current),ey.current=setTimeout(()=>{eN()},300)},[X,x]);let ej=async()=>{try{j(!0);let e=new AbortController,r=setTimeout(()=>e.abort(),5e3),t=`&_t=${Date.now()}`,s=await fetch(`${API_BASE_URL}/products?page=${P}&limit=20${t}`,{signal:e.signal});if(clearTimeout(r),!s.ok)throw Error(`HTTP error! status: ${s.status}`);let a=await s.json();a.length<20&&S(!1);let o=new Set(x.map(e=>e.item_code)),n=a.filter(e=>!o.has(e.item_code));g(e=>[...e,...n]),k(e=>e+1)}catch(e){throw console.error("Error fetching products:",e),e instanceof Error&&"AbortError"===e.name?w("Request timed out. Please check your connection."):w("Failed to load products. Please try again."),e}finally{j(!1)}},eN=()=>{let e=X.toLowerCase().trim();if(0===e.length){eh([]),eb(!1);return}eh(x.filter(r=>r.item_name&&r.item_name.toLowerCase().includes(e))),eb(!0)},ew=async()=>{try{j(!0),w(""),await ej();let e=c.IG.get("selectedCompany");if(e)try{let r=JSON.parse(e);R(r.key)}catch(e){console.warn("Error parsing saved company:",e)}eP()}catch(e){console.error("Initialization error:",e),w("Failed to initialize the application. Please try again.")}finally{j(!1)}},eP=()=>{},ek=(e=0,r=0,t=0,f.forEach(s=>{en?(e+=.85*s.price*s.quantity,r+=.15*s.price*s.quantity):e+=s.price*s.quantity,t+=s.price*s.quantity}),en||(r=0),{subtotal:Math.round(e),tax:Math.round(r),total:Math.round(t)});return(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,s.jsx)(i.A,{isVisible:y,error:N,onRetry:()=>{w(""),g([]),k(1),S(!0),ew()}}),(0,s.jsx)(d.A,{messages:h,onRemove:b}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-5 shadow-lg",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-5 flex-wrap gap-4",children:[(0,s.jsx)("h1",{className:"text-gray-800 text-2xl font-normal m-0",children:"Receipt and Quotation Generator"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,s.jsx)("a",{href:"/convert-to-receipt",className:"bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors",children:"Convert to Receipt"}),u?.is_admin&&(0,s.jsx)("a",{href:"/admin",className:"bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors",children:"Admin Dashboard"}),(0,s.jsx)("button",{onClick:m,className:"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors",children:"Logout"})]})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Select Company"}),(0,s.jsx)("label",{htmlFor:"companySelect",className:"block mb-1",children:"Choose a company:"}),(0,s.jsxs)("select",{id:"companySelect",value:T,onChange:e=>R(e.target.value),required:!0,className:"w-full p-2 border border-gray-300 rounded box-border",children:[(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a company"}),(0,s.jsx)("option",{value:"company1",children:"Shans Accessories PTY LTD"}),(0,s.jsx)("option",{value:"company2",children:"Shans Autosport PTY LTD"}),(0,s.jsx)("option",{value:"company3",children:"Shans Motorstyle PTY LTD"})]})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Customer Information"}),(0,s.jsx)("label",{htmlFor:"customerName",className:"block mb-1",children:"Name:"}),(0,s.jsx)("input",{type:"text",id:"customerName",value:E,onChange:e=>I(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"customerEmail",className:"block mb-1",children:"Email:"}),(0,s.jsx)("input",{type:"email",id:"customerEmail",value:F,onChange:e=>q(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"customerAddress",className:"block mb-1",children:"Address:"}),(0,s.jsx)("input",{type:"text",id:"customerAddress",value:G,onChange:e=>U(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"customerPhone",className:"block mb-1",children:"Phone:"}),(0,s.jsx)("input",{type:"tel",id:"customerPhone",value:$,onChange:e=>M(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"salespersonName",className:"block mb-1",children:"Salesperson Name:"}),(0,s.jsx)("input",{type:"text",id:"salespersonName",value:O,onChange:e=>z(e.target.value),placeholder:"Enter your name...",className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Shipping Information"}),(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:L,onChange:e=>D(e.target.checked),className:"mr-2"}),"Same as billing address"]})}),!L&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("label",{htmlFor:"shippingName",className:"block mb-1",children:"Name:"}),(0,s.jsx)("input",{type:"text",id:"shippingName",value:J,onChange:e=>V(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"shippingEmail",className:"block mb-1",children:"Email:"}),(0,s.jsx)("input",{type:"email",id:"shippingEmail",value:Q,onChange:e=>B(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"shippingAddress",className:"block mb-1",children:"Address:"}),(0,s.jsx)("input",{type:"text",id:"shippingAddress",value:H,onChange:e=>W(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"}),(0,s.jsx)("label",{htmlFor:"shippingPhone",className:"block mb-1",children:"Phone:"}),(0,s.jsx)("input",{type:"tel",id:"shippingPhone",value:Y,onChange:e=>K(e.target.value),className:"w-full p-2 mb-2 border border-gray-300 rounded box-border"})]})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Add Products"}),(0,s.jsxs)("div",{className:"relative mb-3",children:[(0,s.jsx)("label",{htmlFor:"productSearch",className:"block mb-1",children:"Search Products:"}),(0,s.jsx)("input",{type:"text",id:"productSearch",value:X,onChange:e=>Z(e.target.value),placeholder:"Type to search products...",className:"w-full p-2 border border-gray-300 rounded box-border"}),ep&&em.length>0&&(0,s.jsx)("div",{className:"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg",children:em.map(e=>(0,s.jsxs)("div",{className:"p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>{eg(e),Z(e.item_name),es(e.unit_retail_price),eo(e.room_name),eb(!1)},children:[(0,s.jsx)("div",{className:"font-medium text-gray-800",children:e.item_name}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Code: ",e.item_code," | Price: ","R",e.unit_retail_price," | Stock: ",e.available_stock," | Room: ",e.room_name]})]},e.item_code))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"quantity",className:"block mb-1",children:"Quantity:"}),(0,s.jsx)("input",{type:"number",id:"quantity",value:ee,onChange:e=>er(Math.max(1,parseInt(e.target.value)||1)),min:"1",className:"w-full p-2 border border-gray-300 rounded box-border"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"price",className:"block mb-1",children:["Price (","R","):"]}),(0,s.jsx)("input",{type:"number",id:"price",value:et,onChange:e=>es(parseFloat(e.target.value)||0),min:"0",step:"0.01",className:"w-full p-2 border border-gray-300 rounded box-border"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"room",className:"block mb-1",children:"Room:"}),(0,s.jsx)("input",{type:"text",id:"room",value:ea,onChange:e=>eo(e.target.value),className:"w-full p-2 border border-gray-300 rounded box-border"})]})]}),(0,s.jsx)("button",{onClick:()=>{if(!X.trim())return void p("Please enter a product name","error");if(et<=0)return void p("Please enter a valid price","error");if(ee<=0)return void p("Please enter a valid quantity","error");let e={item_code:ex?.item_code||`NEW_${A}`,name:X.trim(),room_name:ea.trim()||"N/A",quantity:ee,price:et,tax_per_product:en?.15*et:0,is_new:!ex};v(r=>[...r,e]),Z(""),er(1),es(0),eo(""),eg(null),eb(!1),ex||_(e=>e+1),p("Product added to cart","success")},className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Add Product"})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Selected Products"}),0===f.length?(0,s.jsx)("p",{className:"text-gray-600",children:"No products selected yet."}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full border-collapse",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-gray-100",children:[(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Product"}),(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Room"}),(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Qty"}),(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Price"}),(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Total"}),(0,s.jsx)("th",{className:"border border-gray-300 p-2 text-left",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:f.map((e,r)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-gray-300 p-2",children:e.name}),(0,s.jsx)("td",{className:"border border-gray-300 p-2",children:e.room_name}),(0,s.jsx)("td",{className:"border border-gray-300 p-2",children:e.quantity}),(0,s.jsxs)("td",{className:"border border-gray-300 p-2",children:["R",e.price]}),(0,s.jsxs)("td",{className:"border border-gray-300 p-2",children:["R",e.price*e.quantity]}),(0,s.jsx)("td",{className:"border border-gray-300 p-2",children:(0,s.jsx)("button",{onClick:()=>{v(e=>e.filter((e,t)=>t!==r)),p("Product removed","info")},className:"bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors",children:"Remove"})})]},r))})]})})]}),(0,s.jsxs)("div",{className:"mb-5 border border-gray-300 p-4 rounded",children:[(0,s.jsx)("h2",{className:"text-gray-800 text-xl mb-3",children:"Order Summary"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"paymentMethod",className:"block mb-1",children:"Payment Method:"}),(0,s.jsxs)("select",{id:"paymentMethod",value:ei,onChange:e=>ed(e.target.value),className:"w-full p-2 border border-gray-300 rounded box-border",children:[(0,s.jsx)("option",{value:"Cash",children:"Cash"}),(0,s.jsx)("option",{value:"Card",children:"Card"}),(0,s.jsx)("option",{value:"Bank Transfer",children:"Bank Transfer"}),(0,s.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,s.jsx)("div",{children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:en,onChange:e=>el(e.target.checked),className:"mr-2"}),"Include Tax (15%)"]})})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{htmlFor:"comments",className:"block mb-1",children:"Comments:"}),(0,s.jsx)("textarea",{id:"comments",value:ec,onChange:e=>eu(e.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded box-border resize-vertical",placeholder:"Additional comments or notes..."})]}),(0,s.jsxs)("div",{className:"mt-4 bg-gray-100 p-3 rounded",children:[(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)("span",{children:"Subtotal:"}),(0,s.jsxs)("span",{children:["R",ek.subtotal]})]}),en&&(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)("span",{children:"Tax (15%):"}),(0,s.jsxs)("span",{children:["R",ek.tax]})]}),(0,s.jsxs)("div",{className:"flex justify-between font-bold text-lg border-t border-gray-300 pt-2",children:[(0,s.jsx)("span",{children:"Total:"}),(0,s.jsxs)("span",{children:["R",ek.total]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3 justify-center",children:[(0,s.jsx)("button",{onClick:()=>p("Generate Receipt functionality coming soon","info"),className:"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors",children:"Generate Receipt"}),(0,s.jsx)("button",{onClick:()=>p("Generate Quotation functionality coming soon","info"),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"Generate Quotation"}),(0,s.jsx)("button",{onClick:()=>p("Generate Invoice functionality coming soon","info"),className:"bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors",children:"Generate Invoice"})]})]})]})})}},6189:(e,r,t)=>{"use strict";var s=t(5773);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>a});var s=t(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9776:(e,r,t)=>{Promise.resolve().then(t.bind(t,5694))},9855:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[825],()=>t(5226));module.exports=s})();