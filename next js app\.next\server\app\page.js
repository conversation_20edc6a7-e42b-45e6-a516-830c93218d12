/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q1VzZXIlNUNWaWRlb3MlNUN3b3Jrc3BhY2UlNUNzaGFucy1mcm9udGVuZCU1Q25leHQlMjBqcyUyMGFwcCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDVXNlciU1Q1ZpZGVvcyU1Q3dvcmtzcGFjZSU1Q3NoYW5zLWZyb250ZW5kJTVDbmV4dCUyMGpzJTIwYXBwJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQTBIO0FBQ2hKLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixnSkFBd0g7QUFHMUk7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBR3JCO0FBQ0YsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxWaWRlb3NcXFxcd29ya3NwYWNlXFxcXHNoYW5zLWZyb250ZW5kXFxcXG5leHQganMgYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXFZpZGVvc1xcXFx3b3Jrc3BhY2VcXFxcc2hhbnMtZnJvbnRlbmRcXFxcbmV4dCBqcyBhcHBcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxWaWRlb3NcXFxcd29ya3NwYWNlXFxcXHNoYW5zLWZyb250ZW5kXFxcXG5leHQganMgYXBwXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXFZpZGVvc1xcXFx3b3Jrc3BhY2VcXFxcc2hhbnMtZnJvbnRlbmRcXFxcbmV4dCBqcyBhcHBcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff82097a3125\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZmODIwOTdhMzEyNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\nconst metadata = {\n    title: 'Shans System',\n    description: 'Receipt and Quotation Generator',\n    viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',\n    other: {\n        'mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-capable': 'yes',\n        'theme-color': '#ffffff',\n        'format-detection': 'telephone=no'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans leading-relaxed m-0 p-0 bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQjtBQUMrQjtBQUU5QyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsT0FBTztRQUNMLDBCQUEwQjtRQUMxQixnQ0FBZ0M7UUFDaEMsZUFBZTtRQUNmLG9CQUFvQjtJQUN0QjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDZCw0RUFBQ1gsK0RBQVlBOzBCQUNWTzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTaGFucyBTeXN0ZW0nLFxuICBkZXNjcmlwdGlvbjogJ1JlY2VpcHQgYW5kIFF1b3RhdGlvbiBHZW5lcmF0b3InLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAsIG1heGltdW0tc2NhbGU9MS4wLCB1c2VyLXNjYWxhYmxlPW5vJyxcbiAgb3RoZXI6IHtcbiAgICAnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZSc6ICd5ZXMnLFxuICAgICdhcHBsZS1tb2JpbGUtd2ViLWFwcC1jYXBhYmxlJzogJ3llcycsXG4gICAgJ3RoZW1lLWNvbG9yJzogJyNmZmZmZmYnLFxuICAgICdmb3JtYXQtZGV0ZWN0aW9uJzogJ3RlbGVwaG9uZT1ubycsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGxlYWRpbmctcmVsYXhlZCBtLTAgcC0wIGJnLWdyYXktMTAwXCI+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2aWV3cG9ydCIsIm90aGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(ssr)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst CURRENCY_SYMBOL = 'R';\nconst TAX_RATE = 0.15;\nconst DEBOUNCE_DELAY = 300;\nconst PRODUCTS_PER_PAGE = 20;\nconst companies = {\n    company1: {\n        key: 'company1',\n        name: \"Shans Accessories PTY LTD\",\n        bankingInformation: `\n      First National Bank<br>\n      Account :  ***********<br>\n      Branch code 257705<br>\n      Swift code FIRNZAJJ\n    `\n    },\n    company2: {\n        key: 'company2',\n        name: \"Shans Autosport PTY LTD\",\n        bankingInformation: `\n      Business Account<br>\n      Capitec Current Account<br>\n      Account: **********\n    `\n    },\n    company3: {\n        key: 'company3',\n        name: \"Shans Motorstyle PTY LTD\",\n        bankingInformation: `\n      SHANS MOTORSTYLE (PTY) LTD<br>\n      Gold Business Account<br>\n      Account Number: ***********<br>\n      Branch Code: 250655<br>\n      Swift Code: FIRNZAJJ\n    `\n    }\n};\nfunction HomePage() {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { messages, showToast, removeToast } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [allProducts, setAllProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingError, setLoadingError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreProducts, setHasMoreProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [newProductCounter, setNewProductCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Form state\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerEmail, setCustomerEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerAddress, setCustomerAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerPhone, setCustomerPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [salespersonName, setSalespersonName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sameAsBilling, setSameAsBilling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [shippingName, setShippingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingEmail, setShippingEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingAddress, setShippingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingPhone, setShippingPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [productSearch, setProductSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [price, setPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [includeTax, setIncludeTax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Cash');\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Product search state\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showProductList, setShowProductList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductData, setSelectedProductData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSelectingProduct, setIsSelectingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const searchTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Debounced search function\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[debouncedSearch]\": ()=>{\n            if (searchTimeoutRef.current) {\n                clearTimeout(searchTimeoutRef.current);\n            }\n            searchTimeoutRef.current = setTimeout({\n                \"HomePage.useCallback[debouncedSearch]\": ()=>{\n                    displayFilteredProducts();\n                }\n            }[\"HomePage.useCallback[debouncedSearch]\"], DEBOUNCE_DELAY);\n        }\n    }[\"HomePage.useCallback[debouncedSearch]\"], [\n        productSearch,\n        allProducts\n    ]);\n    // Fetch products from API\n    const fetchAllProducts = async ()=>{\n        try {\n            setIsLoading(true);\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const cacheBuster = `&_t=${Date.now()}`;\n            const response = await fetch(`${API_BASE_URL}/products?page=${currentPage}&limit=${PRODUCTS_PER_PAGE}${cacheBuster}`, {\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const newProducts = await response.json();\n            if (newProducts.length < PRODUCTS_PER_PAGE) {\n                setHasMoreProducts(false);\n            }\n            // Filter out duplicates\n            const existingItemCodes = new Set(allProducts.map((p)=>p.item_code));\n            const uniqueNewProducts = newProducts.filter((product)=>!existingItemCodes.has(product.item_code));\n            setAllProducts((prev)=>[\n                    ...prev,\n                    ...uniqueNewProducts\n                ]);\n            setCurrentPage((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            if (error instanceof Error && error.name === 'AbortError') {\n                setLoadingError('Request timed out. Please check your connection.');\n            } else {\n                setLoadingError('Failed to load products. Please try again.');\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Display filtered products\n    const displayFilteredProducts = ()=>{\n        const searchTerm = productSearch.toLowerCase().trim();\n        if (searchTerm.length === 0) {\n            setFilteredProducts([]);\n            setShowProductList(false);\n            return;\n        }\n        const filtered = allProducts.filter((product)=>product.item_name && product.item_name.toLowerCase().includes(searchTerm));\n        setFilteredProducts(filtered);\n        setShowProductList(true);\n    };\n    // Initialize app\n    const initializeApp = async ()=>{\n        try {\n            setIsLoading(true);\n            setLoadingError('');\n            await fetchAllProducts();\n            // Load saved company selection\n            const savedCompany = _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__.storage.get('selectedCompany');\n            if (savedCompany) {\n                try {\n                    const company = JSON.parse(savedCompany);\n                    setSelectedCompany(company.key);\n                } catch (e) {\n                    console.warn('Error parsing saved company:', e);\n                }\n            }\n            // Load existing order data\n            loadExistingOrderData();\n        } catch (error) {\n            console.error('Initialization error:', error);\n            setLoadingError('Failed to initialize the application. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Load existing order data from storage\n    const loadExistingOrderData = ()=>{\n    // Implementation for loading saved order data\n    // This would restore form fields from localStorage if needed\n    };\n    // Retry initialization\n    const retryInitialization = ()=>{\n        setLoadingError('');\n        setAllProducts([]);\n        setCurrentPage(1);\n        setHasMoreProducts(true);\n        initializeApp();\n    };\n    // Add product to cart\n    const addProduct = ()=>{\n        if (!productSearch.trim()) {\n            showToast('Please enter a product name', 'error');\n            return;\n        }\n        if (price <= 0) {\n            showToast('Please enter a valid price', 'error');\n            return;\n        }\n        if (quantity <= 0) {\n            showToast('Please enter a valid quantity', 'error');\n            return;\n        }\n        const newProduct = {\n            item_code: selectedProductData?.item_code || `NEW_${newProductCounter}`,\n            name: productSearch.trim(),\n            room_name: room.trim() || 'N/A',\n            quantity: quantity,\n            price: price,\n            tax_per_product: includeTax ? price * TAX_RATE : 0,\n            is_new: !selectedProductData\n        };\n        setSelectedProducts((prev)=>[\n                ...prev,\n                newProduct\n            ]);\n        // Reset form\n        setProductSearch('');\n        setQuantity(1);\n        setPrice(0);\n        setRoom('');\n        setSelectedProductData(null);\n        setShowProductList(false);\n        if (!selectedProductData) {\n            setNewProductCounter((prev)=>prev + 1);\n        }\n        showToast('Product added to cart', 'success');\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            initializeApp();\n        }\n    }[\"HomePage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            debouncedSearch();\n        }\n    }[\"HomePage.useEffect\"], [\n        productSearch,\n        debouncedSearch\n    ]);\n    // Calculate totals\n    const calculateTotals = ()=>{\n        let subtotal = 0;\n        let tax = 0;\n        let total = 0;\n        selectedProducts.forEach((product)=>{\n            if (includeTax) {\n                subtotal += product.price * (1 - TAX_RATE) * product.quantity;\n                tax += product.price * TAX_RATE * product.quantity;\n                total += product.price * product.quantity;\n            } else {\n                subtotal += product.price * product.quantity;\n                total += product.price * product.quantity;\n            }\n        });\n        if (!includeTax) {\n            tax = 0;\n        }\n        return {\n            subtotal: Math.round(subtotal),\n            tax: Math.round(tax),\n            total: Math.round(total)\n        };\n    };\n    const totals = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isVisible: isLoading,\n                    error: loadingError,\n                    onRetry: retryInitialization\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    messages: messages,\n                    onRemove: removeToast\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto bg-white p-5 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-5 flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-gray-800 text-2xl font-normal m-0\",\n                                    children: \"Receipt and Quotation Generator\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 flex-wrap\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/convert-to-receipt\",\n                                            className: \"bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors\",\n                                            children: \"Convert to Receipt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        user?.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            className: \"bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors\",\n                                            children: \"Admin Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Select Company\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"companySelect\",\n                                    className: \"block mb-1\",\n                                    children: \"Choose a company:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"companySelect\",\n                                    value: selectedCompany,\n                                    onChange: (e)=>setSelectedCompany(e.target.value),\n                                    required: true,\n                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Select a company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company1\",\n                                            children: \"Shans Accessories PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company2\",\n                                            children: \"Shans Autosport PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company3\",\n                                            children: \"Shans Motorstyle PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Customer Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerName\",\n                                    className: \"block mb-1\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerName\",\n                                    value: customerName,\n                                    onChange: (e)=>setCustomerName(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerEmail\",\n                                    className: \"block mb-1\",\n                                    children: \"Email:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"customerEmail\",\n                                    value: customerEmail,\n                                    onChange: (e)=>setCustomerEmail(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerAddress\",\n                                    className: \"block mb-1\",\n                                    children: \"Address:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerAddress\",\n                                    value: customerAddress,\n                                    onChange: (e)=>setCustomerAddress(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerPhone\",\n                                    className: \"block mb-1\",\n                                    children: \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"customerPhone\",\n                                    value: customerPhone,\n                                    onChange: (e)=>setCustomerPhone(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"salespersonName\",\n                                    className: \"block mb-1\",\n                                    children: \"Salesperson Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"salespersonName\",\n                                    value: salespersonName,\n                                    onChange: (e)=>setSalespersonName(e.target.value),\n                                    placeholder: \"Enter your name...\",\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Shipping Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: sameAsBilling,\n                                                onChange: (e)=>setSameAsBilling(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Same as billing address\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                !sameAsBilling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingName\",\n                                            className: \"block mb-1\",\n                                            children: \"Name:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingName\",\n                                            value: shippingName,\n                                            onChange: (e)=>setShippingName(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingEmail\",\n                                            className: \"block mb-1\",\n                                            children: \"Email:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"shippingEmail\",\n                                            value: shippingEmail,\n                                            onChange: (e)=>setShippingEmail(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingAddress\",\n                                            className: \"block mb-1\",\n                                            children: \"Address:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingAddress\",\n                                            value: shippingAddress,\n                                            onChange: (e)=>setShippingAddress(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingPhone\",\n                                            className: \"block mb-1\",\n                                            children: \"Phone:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            id: \"shippingPhone\",\n                                            value: shippingPhone,\n                                            onChange: (e)=>setShippingPhone(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Add Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"productSearch\",\n                                            className: \"block mb-1\",\n                                            children: \"Search Products:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"productSearch\",\n                                            value: productSearch,\n                                            onChange: (e)=>setProductSearch(e.target.value),\n                                            placeholder: \"Type to search products...\",\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        showProductList && filteredProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg\",\n                                            children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors\",\n                                                    onClick: ()=>{\n                                                        setSelectedProductData(product);\n                                                        setProductSearch(product.item_name);\n                                                        setPrice(product.unit_retail_price);\n                                                        setRoom(product.room_name);\n                                                        setShowProductList(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: product.item_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Code: \",\n                                                                product.item_code,\n                                                                \" | Price: \",\n                                                                CURRENCY_SYMBOL,\n                                                                product.unit_retail_price,\n                                                                \" | Stock: \",\n                                                                product.available_stock,\n                                                                \" | Room: \",\n                                                                product.room_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, product.item_code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"quantity\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Quantity:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"quantity\",\n                                                    value: quantity,\n                                                    onChange: (e)=>setQuantity(Math.max(1, parseInt(e.target.value) || 1)),\n                                                    min: \"1\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"price\",\n                                                    className: \"block mb-1\",\n                                                    children: [\n                                                        \"Price (\",\n                                                        CURRENCY_SYMBOL,\n                                                        \"):\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"price\",\n                                                    value: price,\n                                                    onChange: (e)=>setPrice(parseFloat(e.target.value) || 0),\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"room\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Room:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"room\",\n                                                    value: room,\n                                                    onChange: (e)=>setRoom(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: addProduct,\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                    children: \"Add Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Selected Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                selectedProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"No products selected yet.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Room\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Qty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: selectedProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.room_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price * product.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Remove product logic\n                                                                        setSelectedProducts((prev)=>prev.filter((_, i)=>i !== index));\n                                                                        showToast('Product removed', 'info');\n                                                                    },\n                                                                    className: \"bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Order Summary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"paymentMethod\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Payment Method:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"paymentMethod\",\n                                                    value: paymentMethod,\n                                                    onChange: (e)=>setPaymentMethod(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Cash\",\n                                                            children: \"Cash\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Card\",\n                                                            children: \"Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Bank Transfer\",\n                                                            children: \"Bank Transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Other\",\n                                                            children: \"Other\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: includeTax,\n                                                        onChange: (e)=>setIncludeTax(e.target.checked),\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Include Tax (15%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"comments\",\n                                            className: \"block mb-1\",\n                                            children: \"Comments:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"comments\",\n                                            value: comments,\n                                            onChange: (e)=>setComments(e.target.value),\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border resize-vertical\",\n                                            placeholder: \"Additional comments or notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 bg-gray-100 p-3 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtotal:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.subtotal\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        includeTax && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tax (15%):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.tax\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between font-bold text-lg border-t border-gray-300 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Receipt functionality coming soon', 'info'),\n                                    className: \"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors\",\n                                    children: \"Generate Receipt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Quotation functionality coming soon', 'info'),\n                                    className: \"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"Generate Quotation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Invoice functionality coming soon', 'info'),\n                                    className: \"bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"Generate Invoice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction LoadingScreen({ isVisible, message = \"Loading products, please wait...\", error, onRetry }) {\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-800 text-lg font-semibold text-center text-shadow-sm\",\n                children: error ? 'Connection Error' : message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onRetry,\n                        className: \"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2FkaW5nU2NyZWVuLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBU2UsU0FBU0EsY0FBYyxFQUNwQ0MsU0FBUyxFQUNUQyxVQUFVLGtDQUFrQyxFQUM1Q0MsS0FBSyxFQUNMQyxPQUFPLEVBQ1k7SUFDbkIsSUFBSSxDQUFDSCxXQUFXLE9BQU87SUFFdkIscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUVmLDhEQUFDQztnQkFBRUQsV0FBVTswQkFDVkgsUUFBUSxxQkFBcUJEOzs7Ozs7WUFHL0JDLHVCQUNDOztrQ0FDRSw4REFBQ0U7d0JBQUlDLFdBQVU7a0NBQ1pIOzs7Ozs7b0JBRUZDLHlCQUNDLDhEQUFDSTt3QkFDQ0MsU0FBU0w7d0JBQ1RFLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7O0FBUWIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcVmlkZW9zXFx3b3Jrc3BhY2VcXHNoYW5zLWZyb250ZW5kXFxuZXh0IGpzIGFwcFxcc3JjXFxjb21wb25lbnRzXFxMb2FkaW5nU2NyZWVuLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmludGVyZmFjZSBMb2FkaW5nU2NyZWVuUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW47XG4gIG1lc3NhZ2U/OiBzdHJpbmc7XG4gIGVycm9yPzogc3RyaW5nO1xuICBvblJldHJ5PzogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZ1NjcmVlbih7IFxuICBpc1Zpc2libGUsIFxuICBtZXNzYWdlID0gXCJMb2FkaW5nIHByb2R1Y3RzLCBwbGVhc2Ugd2FpdC4uLlwiLCBcbiAgZXJyb3IsXG4gIG9uUmV0cnkgXG59OiBMb2FkaW5nU2NyZWVuUHJvcHMpIHtcbiAgaWYgKCFpc1Zpc2libGUpIHJldHVybiBudWxsO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtZnVsbCBiZy13aGl0ZSBiZy1vcGFjaXR5LTk4IHotNTAgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LXN0YXJ0IGl0ZW1zLWNlbnRlciBwdC0yMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBib3JkZXItMTAgYm9yZGVyLWdyYXktMzAwIGJvcmRlci10LWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbWItNiBzaGFkb3ctbGdcIj48L2Rpdj5cbiAgICAgIFxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1zaGFkb3ctc21cIj5cbiAgICAgICAge2Vycm9yID8gJ0Nvbm5lY3Rpb24gRXJyb3InIDogbWVzc2FnZX1cbiAgICAgIDwvcD5cbiAgICAgIFxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtMzAwIHRleHQtcmVkLTgwMCBwLTMgcm91bmRlZCBtdC00IG1heC13LXhzIHRleHQtY2VudGVyIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7b25SZXRyeSAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmV0cnl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTUgcHgtNiBweS0zIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgYm9yZGVyLW5vbmUgcm91bmRlZCBjdXJzb3ItcG9pbnRlciB0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOmJnLWJsdWUtNzAwIGhvdmVyOi10cmFuc2xhdGUteS0wLjUgaG92ZXI6c2hhZG93LWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUmV0cnlcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nU2NyZWVuIiwiaXNWaXNpYmxlIiwibWVzc2FnZSIsImVycm9yIiwib25SZXRyeSIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children, requireAdmin = false, redirectTo = '/login' }) {\n    const { user, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [shouldRender, setShouldRender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (isLoading) {\n                return; // Still checking authentication\n            }\n            if (!isAuthenticated) {\n                router.push(redirectTo);\n                return;\n            }\n            if (requireAdmin && user && !user.is_admin) {\n                router.push('/'); // Redirect non-admin users to home\n                return;\n            }\n            setShouldRender(true);\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        user,\n        requireAdmin,\n        router,\n        redirectTo\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this);\n    }\n    if (!shouldRender) {\n        return null; // Don't render anything while redirecting\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToastContainer),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, onRemove }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    onRemove(message.id);\n                }\n            }[\"Toast.useEffect.timer\"], 3000);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        message.id,\n        onRemove\n    ]);\n    const bgColor = {\n        success: 'bg-green-600',\n        error: 'bg-red-600',\n        info: 'bg-blue-600'\n    }[message.type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${bgColor} text-white px-4 py-2 rounded opacity-95 text-sm toast`,\n        children: message.message\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastContainer({ messages, onRemove }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-5 right-5 z-50 flex flex-col gap-2\",\n        children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                message: message,\n                onRemove: onRemove\n            }, message.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n// Hook for managing toast messages\nfunction useToast() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n    };\n    const removeToast = (id)=>{\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== id));\n    };\n    return {\n        messages,\n        showToast,\n        removeToast\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkAuth = async ()=>{\n        try {\n            setIsLoading(true);\n            const authenticated = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.checkAuthStatus)({\n                redirectOnFail: false\n            });\n            if (authenticated) {\n                const currentUser = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                setUser(currentUser);\n                setIsAuthenticated(true);\n                return true;\n            } else {\n                setUser(null);\n                setIsAuthenticated(false);\n                return false;\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n            setUser(null);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(`${_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.API_BASE_URL}/auth/login`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Store authentication token and user info\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('authToken', data.token);\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('userInfo', JSON.stringify(data.user));\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('lastAuthCheck', Date.now().toString());\n                setUser(data.user);\n                setIsAuthenticated(true);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    message: data.message || 'Login failed'\n                };\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                message: 'Network error. Please try again.'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setIsAuthenticated(false);\n        (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.logout)();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlGO0FBQzZCO0FBV3RILE1BQU1XLDRCQUFjVixvREFBYUEsQ0FBOEJXO0FBRXhELFNBQVNDO0lBQ2QsTUFBTUMsVUFBVVosaURBQVVBLENBQUNTO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQU1PLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2YsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDZ0IsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDa0IsaUJBQWlCQyxtQkFBbUIsR0FBR25CLCtDQUFRQSxDQUFDO0lBRXZELE1BQU1vQixZQUFZO1FBQ2hCLElBQUk7WUFDRkgsYUFBYTtZQUNiLE1BQU1JLGdCQUFnQixNQUFNbkIsZ0VBQWVBLENBQUM7Z0JBQUVvQixnQkFBZ0I7WUFBTTtZQUVwRSxJQUFJRCxlQUFlO2dCQUNqQixNQUFNRSxjQUFjdEIsK0RBQWNBO2dCQUNsQ2MsUUFBUVE7Z0JBQ1JKLG1CQUFtQjtnQkFDbkIsT0FBTztZQUNULE9BQU87Z0JBQ0xKLFFBQVE7Z0JBQ1JJLG1CQUFtQjtnQkFDbkIsT0FBTztZQUNUO1FBQ0YsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1lBQ3BDVCxRQUFRO1lBQ1JJLG1CQUFtQjtZQUNuQixPQUFPO1FBQ1QsU0FBVTtZQUNSRixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1TLFFBQVEsT0FBT0MsT0FBZUM7UUFDbEMsSUFBSTtZQUNGWCxhQUFhO1lBRWIsTUFBTVksV0FBVyxNQUFNQyxNQUFNLEdBQUd6Qix5REFBWUEsQ0FBQyxXQUFXLENBQUMsRUFBRTtnQkFDekQwQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRVI7b0JBQU9DO2dCQUFTO1lBQ3pDO1lBRUEsTUFBTVEsT0FBTyxNQUFNUCxTQUFTUSxJQUFJO1lBRWhDLElBQUlSLFNBQVNTLEVBQUUsRUFBRTtnQkFDZiwyQ0FBMkM7Z0JBQzNDaEMsb0RBQU9BLENBQUNpQyxHQUFHLENBQUMsYUFBYUgsS0FBS0ksS0FBSztnQkFDbkNsQyxvREFBT0EsQ0FBQ2lDLEdBQUcsQ0FBQyxZQUFZTCxLQUFLQyxTQUFTLENBQUNDLEtBQUt0QixJQUFJO2dCQUNoRFIsb0RBQU9BLENBQUNpQyxHQUFHLENBQUMsaUJBQWlCRSxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Z0JBRWhENUIsUUFBUXFCLEtBQUt0QixJQUFJO2dCQUNqQkssbUJBQW1CO2dCQUVuQixPQUFPO29CQUFFeUIsU0FBUztnQkFBSztZQUN6QixPQUFPO2dCQUNMLE9BQU87b0JBQUVBLFNBQVM7b0JBQU9DLFNBQVNULEtBQUtTLE9BQU8sSUFBSTtnQkFBZTtZQUNuRTtRQUNGLEVBQUUsT0FBT3JCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsT0FBTztnQkFBRW9CLFNBQVM7Z0JBQU9DLFNBQVM7WUFBbUM7UUFDdkUsU0FBVTtZQUNSNUIsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNZCxTQUFTO1FBQ2JZLFFBQVE7UUFDUkksbUJBQW1CO1FBQ25CZix1REFBVUE7SUFDWjtJQUVBTCxnREFBU0E7a0NBQUM7WUFDUnFCO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLE1BQU0wQixRQUF5QjtRQUM3QmhDO1FBQ0FFO1FBQ0FFO1FBQ0FRO1FBQ0F2QjtRQUNBaUI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDYixZQUFZd0MsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUJqQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcVmlkZW9zXFx3b3Jrc3BhY2VcXHNoYW5zLWZyb250ZW5kXFxuZXh0IGpzIGFwcFxcc3JjXFxjb250ZXh0c1xcQXV0aENvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFVzZXIsIGdldEN1cnJlbnRVc2VyLCBjaGVja0F1dGhTdGF0dXMsIGxvZ291dCBhcyBhdXRoTG9nb3V0LCBBUElfQkFTRV9VUkwsIHN0b3JhZ2UgfSBmcm9tICdAL2xpYi9hdXRoLXV0aWxzJztcblxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XG4gIHVzZXI6IFVzZXIgfCBudWxsO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgbG9naW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZT86IHN0cmluZyB9PjtcbiAgbG9nb3V0OiAoKSA9PiB2b2lkO1xuICBjaGVja0F1dGg6ICgpID0+IFByb21pc2U8Ym9vbGVhbj47XG59XG5cbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUF1dGggbXVzdCBiZSB1c2VkIHdpdGhpbiBhbiBBdXRoUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbaXNBdXRoZW50aWNhdGVkLCBzZXRJc0F1dGhlbnRpY2F0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGNoZWNrQXV0aCA9IGFzeW5jICgpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgYXV0aGVudGljYXRlZCA9IGF3YWl0IGNoZWNrQXV0aFN0YXR1cyh7IHJlZGlyZWN0T25GYWlsOiBmYWxzZSB9KTtcbiAgICAgIFxuICAgICAgaWYgKGF1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBnZXRDdXJyZW50VXNlcigpO1xuICAgICAgICBzZXRVc2VyKGN1cnJlbnRVc2VyKTtcbiAgICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aCBjaGVjayBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZT86IHN0cmluZyB9PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2F1dGgvbG9naW5gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbWFpbCwgcGFzc3dvcmQgfSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIFN0b3JlIGF1dGhlbnRpY2F0aW9uIHRva2VuIGFuZCB1c2VyIGluZm9cbiAgICAgICAgc3RvcmFnZS5zZXQoJ2F1dGhUb2tlbicsIGRhdGEudG9rZW4pO1xuICAgICAgICBzdG9yYWdlLnNldCgndXNlckluZm8nLCBKU09OLnN0cmluZ2lmeShkYXRhLnVzZXIpKTtcbiAgICAgICAgc3RvcmFnZS5zZXQoJ2xhc3RBdXRoQ2hlY2snLCBEYXRlLm5vdygpLnRvU3RyaW5nKCkpO1xuICAgICAgICBcbiAgICAgICAgc2V0VXNlcihkYXRhLnVzZXIpO1xuICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSk7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogZGF0YS5tZXNzYWdlIHx8ICdMb2dpbiBmYWlsZWQnIH07XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnTmV0d29yayBlcnJvci4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH07XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcbiAgICBzZXRVc2VyKG51bGwpO1xuICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgYXV0aExvZ291dCgpO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2hlY2tBdXRoKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB2YWx1ZTogQXV0aENvbnRleHRUeXBlID0ge1xuICAgIHVzZXIsXG4gICAgaXNMb2FkaW5nLFxuICAgIGlzQXV0aGVudGljYXRlZCxcbiAgICBsb2dpbixcbiAgICBsb2dvdXQsXG4gICAgY2hlY2tBdXRoLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJnZXRDdXJyZW50VXNlciIsImNoZWNrQXV0aFN0YXR1cyIsImxvZ291dCIsImF1dGhMb2dvdXQiLCJBUElfQkFTRV9VUkwiLCJzdG9yYWdlIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImlzQXV0aGVudGljYXRlZCIsInNldElzQXV0aGVudGljYXRlZCIsImNoZWNrQXV0aCIsImF1dGhlbnRpY2F0ZWQiLCJyZWRpcmVjdE9uRmFpbCIsImN1cnJlbnRVc2VyIiwiZXJyb3IiLCJjb25zb2xlIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImRhdGEiLCJqc29uIiwib2siLCJzZXQiLCJ0b2tlbiIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwidmFsdWUiLCJQcm92aWRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   initAuth: () => (/* binding */ initAuth),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n// Shared authentication utilities for Next.js app\nconst API_BASE_URL = 'https://shans-backend.onrender.com/api';\n// Storage utility with fallback\nconst storage = {\n    get: function(key) {\n        if (true) return null;\n        try {\n            return localStorage.getItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            return sessionStorage.getItem(key);\n        }\n    },\n    set: function(key, value) {\n        if (true) return;\n        try {\n            localStorage.setItem(key, value);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.setItem(key, value);\n        }\n    },\n    remove: function(key) {\n        if (true) return;\n        try {\n            localStorage.removeItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.removeItem(key);\n        }\n    }\n};\n// Enhanced authentication check with better error handling\nasync function checkAuthStatus(options = {}) {\n    const { redirectOnFail = true, showLoading = false, retryCount = 2, timeout = 8000 } = options;\n    const token = storage.get('authToken');\n    const userInfo = storage.get('userInfo');\n    // If no token, redirect immediately\n    if (!token) {\n        if (redirectOnFail && \"undefined\" !== 'undefined') {}\n        return false;\n    }\n    // If we have userInfo cached and it's recent, trust it temporarily\n    if (userInfo) {\n        try {\n            const user = JSON.parse(userInfo);\n            const lastCheck = storage.get('lastAuthCheck');\n            const now = Date.now();\n            // If last check was less than 5 minutes ago, skip server validation\n            if (lastCheck && now - parseInt(lastCheck) < 5 * 60 * 1000) {\n                return true;\n            }\n        } catch (e) {\n            console.warn('Error parsing cached user info:', e);\n        }\n    }\n    // Validate token with server\n    for(let attempt = 0; attempt <= retryCount; attempt++){\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            // Add cache busting parameter to prevent cached responses\n            const cacheBuster = `?_t=${Date.now()}`;\n            const response = await fetch(`${API_BASE_URL}/auth/me${cacheBuster}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                // Update cached user info and timestamp\n                storage.set('userInfo', JSON.stringify(data.user));\n                storage.set('lastAuthCheck', Date.now().toString());\n                return true;\n            } else if (response.status === 401 || response.status === 403) {\n                // Token is invalid, clear storage and redirect\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            } else {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n        } catch (error) {\n            console.warn(`Auth check attempt ${attempt + 1} failed:`, error);\n            // If this is the last attempt, handle the failure\n            if (attempt === retryCount) {\n                // If we have cached user info, use it as fallback\n                if (userInfo) {\n                    console.log('Using cached authentication due to network issues');\n                    return true;\n                }\n                // No cached info and all attempts failed\n                console.error('Authentication failed after all retries:', error);\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            }\n            // Wait before retry (exponential backoff)\n            await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n    }\n    return false;\n}\n// Check if user is admin\nfunction isAdmin() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return false;\n    try {\n        const user = JSON.parse(userInfo);\n        return Boolean(user.is_admin);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return false;\n    }\n}\n// Get current user info\nfunction getCurrentUser() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return null;\n    try {\n        return JSON.parse(userInfo);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return null;\n    }\n}\n// Logout function\nfunction logout() {\n    const token = storage.get('authToken');\n    // Call logout endpoint (don't wait for response)\n    if (token) {\n        fetch(`${API_BASE_URL}/auth/logout`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`\n            }\n        }).catch((error)=>{\n            console.warn('Logout API call failed:', error);\n        });\n    }\n    // Clear storage and redirect\n    storage.remove('authToken');\n    storage.remove('userInfo');\n    storage.remove('lastAuthCheck');\n    if (false) {}\n}\n// Initialize authentication for a page\nasync function initAuth(options = {}) {\n    const { requireAuth = true, showLoading = false } = options;\n    if (!requireAuth) return true;\n    try {\n        const isAuthenticated = await checkAuthStatus({\n            showLoading,\n            redirectOnFail: true\n        });\n        return isAuthenticated;\n    } catch (error) {\n        console.error('Auth initialization failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();