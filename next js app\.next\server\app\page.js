/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff82097a3125\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZmODIwOTdhMzEyNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\nconst metadata = {\n    title: 'Shans System',\n    description: 'Receipt and Quotation Generator',\n    viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',\n    other: {\n        'mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-capable': 'yes',\n        'theme-color': '#ffffff',\n        'format-detection': 'telephone=no'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans leading-relaxed m-0 p-0 bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQjtBQUMrQjtBQUU5QyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsT0FBTztRQUNMLDBCQUEwQjtRQUMxQixnQ0FBZ0M7UUFDaEMsZUFBZTtRQUNmLG9CQUFvQjtJQUN0QjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDZCw0RUFBQ1gsK0RBQVlBOzBCQUNWTzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTaGFucyBTeXN0ZW0nLFxuICBkZXNjcmlwdGlvbjogJ1JlY2VpcHQgYW5kIFF1b3RhdGlvbiBHZW5lcmF0b3InLFxuICB2aWV3cG9ydDogJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAsIG1heGltdW0tc2NhbGU9MS4wLCB1c2VyLXNjYWxhYmxlPW5vJyxcbiAgb3RoZXI6IHtcbiAgICAnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZSc6ICd5ZXMnLFxuICAgICdhcHBsZS1tb2JpbGUtd2ViLWFwcC1jYXBhYmxlJzogJ3llcycsXG4gICAgJ3RoZW1lLWNvbG9yJzogJyNmZmZmZmYnLFxuICAgICdmb3JtYXQtZGV0ZWN0aW9uJzogJ3RlbGVwaG9uZT1ubycsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGxlYWRpbmctcmVsYXhlZCBtLTAgcC0wIGJnLWdyYXktMTAwXCI+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2aWV3cG9ydCIsIm90aGVyIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBc0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNWaWRlb3MlNUMlNUN3b3Jrc3BhY2UlNUMlNUNzaGFucy1mcm9udGVuZCU1QyU1Q25leHQlMjBqcyUyMGFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcVmlkZW9zXFxcXHdvcmtzcGFjZVxcXFxzaGFucy1mcm9udGVuZFxcXFxuZXh0IGpzIGFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CVideos%5C%5Cworkspace%5C%5Cshans-frontend%5C%5Cnext%20js%20app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(ssr)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst CURRENCY_SYMBOL = 'R';\nconst TAX_RATE = 0.15;\nconst DEBOUNCE_DELAY = 300;\nconst PRODUCTS_PER_PAGE = 20;\nconst companies = {\n    company1: {\n        key: 'company1',\n        name: \"Shans Accessories PTY LTD\",\n        bankingInformation: `\n      First National Bank<br>\n      Account :  ***********<br>\n      Branch code 257705<br>\n      Swift code FIRNZAJJ\n    `\n    },\n    company2: {\n        key: 'company2',\n        name: \"Shans Autosport PTY LTD\",\n        bankingInformation: `\n      Business Account<br>\n      Capitec Current Account<br>\n      Account: **********\n    `\n    },\n    company3: {\n        key: 'company3',\n        name: \"Shans Motorstyle PTY LTD\",\n        bankingInformation: `\n      SHANS MOTORSTYLE (PTY) LTD<br>\n      Gold Business Account<br>\n      Account Number: ***********<br>\n      Branch Code: 250655<br>\n      Swift Code: FIRNZAJJ\n    `\n    }\n};\nfunction HomePage() {\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { messages, showToast, removeToast } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // State management\n    const [allProducts, setAllProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingError, setLoadingError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreProducts, setHasMoreProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [newProductCounter, setNewProductCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Form state\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerEmail, setCustomerEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerAddress, setCustomerAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerPhone, setCustomerPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [salespersonName, setSalespersonName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sameAsBilling, setSameAsBilling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [shippingName, setShippingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingEmail, setShippingEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingAddress, setShippingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [shippingPhone, setShippingPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [productSearch, setProductSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [price, setPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [includeTax, setIncludeTax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Cash');\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Product search state\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showProductList, setShowProductList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProductData, setSelectedProductData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSelectingProduct, setIsSelectingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Refs\n    const searchTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Debounced search function\n    const debouncedSearch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"HomePage.useCallback[debouncedSearch]\": ()=>{\n            if (searchTimeoutRef.current) {\n                clearTimeout(searchTimeoutRef.current);\n            }\n            searchTimeoutRef.current = setTimeout({\n                \"HomePage.useCallback[debouncedSearch]\": ()=>{\n                    displayFilteredProducts();\n                }\n            }[\"HomePage.useCallback[debouncedSearch]\"], DEBOUNCE_DELAY);\n        }\n    }[\"HomePage.useCallback[debouncedSearch]\"], [\n        productSearch,\n        allProducts\n    ]);\n    // Fetch products from API\n    const fetchAllProducts = async ()=>{\n        try {\n            setIsLoading(true);\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000);\n            const cacheBuster = `&_t=${Date.now()}`;\n            const response = await fetch(`${_lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__.API_BASE_URL}/products?page=${currentPage}&limit=${PRODUCTS_PER_PAGE}${cacheBuster}`, {\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const newProducts = await response.json();\n            if (newProducts.length < PRODUCTS_PER_PAGE) {\n                setHasMoreProducts(false);\n            }\n            // Filter out duplicates\n            const existingItemCodes = new Set(allProducts.map((p)=>p.item_code));\n            const uniqueNewProducts = newProducts.filter((product)=>!existingItemCodes.has(product.item_code));\n            setAllProducts((prev)=>[\n                    ...prev,\n                    ...uniqueNewProducts\n                ]);\n            setCurrentPage((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            if (error instanceof Error && error.name === 'AbortError') {\n                setLoadingError('Request timed out. Please check your connection.');\n            } else {\n                setLoadingError('Failed to load products. Please try again.');\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Display filtered products\n    const displayFilteredProducts = ()=>{\n        const searchTerm = productSearch.toLowerCase().trim();\n        if (searchTerm.length === 0) {\n            setFilteredProducts([]);\n            setShowProductList(false);\n            return;\n        }\n        const filtered = allProducts.filter((product)=>product.item_name && product.item_name.toLowerCase().includes(searchTerm));\n        setFilteredProducts(filtered);\n        setShowProductList(true);\n    };\n    // Initialize app\n    const initializeApp = async ()=>{\n        try {\n            setIsLoading(true);\n            setLoadingError('');\n            await fetchAllProducts();\n            // Load saved company selection\n            const savedCompany = _lib_auth_utils__WEBPACK_IMPORTED_MODULE_7__.storage.get('selectedCompany');\n            if (savedCompany) {\n                try {\n                    const company = JSON.parse(savedCompany);\n                    setSelectedCompany(company.key);\n                } catch (e) {\n                    console.warn('Error parsing saved company:', e);\n                }\n            }\n            // Load existing order data\n            loadExistingOrderData();\n        } catch (error) {\n            console.error('Initialization error:', error);\n            setLoadingError('Failed to initialize the application. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Load existing order data from storage\n    const loadExistingOrderData = ()=>{\n    // Implementation for loading saved order data\n    // This would restore form fields from localStorage if needed\n    };\n    // Retry initialization\n    const retryInitialization = ()=>{\n        setLoadingError('');\n        setAllProducts([]);\n        setCurrentPage(1);\n        setHasMoreProducts(true);\n        initializeApp();\n    };\n    // Add product to cart\n    const addProduct = ()=>{\n        if (!productSearch.trim()) {\n            showToast('Please enter a product name', 'error');\n            return;\n        }\n        if (price <= 0) {\n            showToast('Please enter a valid price', 'error');\n            return;\n        }\n        if (quantity <= 0) {\n            showToast('Please enter a valid quantity', 'error');\n            return;\n        }\n        const newProduct = {\n            item_code: selectedProductData?.item_code || `NEW_${newProductCounter}`,\n            name: productSearch.trim(),\n            room_name: room.trim() || 'N/A',\n            quantity: quantity,\n            price: price,\n            tax_per_product: includeTax ? price * TAX_RATE : 0,\n            is_new: !selectedProductData\n        };\n        setSelectedProducts((prev)=>[\n                ...prev,\n                newProduct\n            ]);\n        // Reset form\n        setProductSearch('');\n        setQuantity(1);\n        setPrice(0);\n        setRoom('');\n        setSelectedProductData(null);\n        setShowProductList(false);\n        if (!selectedProductData) {\n            setNewProductCounter((prev)=>prev + 1);\n        }\n        showToast('Product added to cart', 'success');\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            initializeApp();\n        }\n    }[\"HomePage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            debouncedSearch();\n        }\n    }[\"HomePage.useEffect\"], [\n        productSearch,\n        debouncedSearch\n    ]);\n    // Calculate totals\n    const calculateTotals = ()=>{\n        let subtotal = 0;\n        let tax = 0;\n        let total = 0;\n        selectedProducts.forEach((product)=>{\n            if (includeTax) {\n                subtotal += product.price * (1 - TAX_RATE) * product.quantity;\n                tax += product.price * TAX_RATE * product.quantity;\n                total += product.price * product.quantity;\n            } else {\n                subtotal += product.price * product.quantity;\n                total += product.price * product.quantity;\n            }\n        });\n        if (!includeTax) {\n            tax = 0;\n        }\n        return {\n            subtotal: Math.round(subtotal),\n            tax: Math.round(tax),\n            total: Math.round(total)\n        };\n    };\n    const totals = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isVisible: isLoading,\n                    error: loadingError,\n                    onRetry: retryInitialization\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    messages: messages,\n                    onRemove: removeToast\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto bg-white p-5 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-5 flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-gray-800 text-2xl font-normal m-0\",\n                                    children: \"Receipt and Quotation Generator\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 flex-wrap\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/convert-to-receipt\",\n                                            className: \"bg-orange-500 text-white px-4 py-2 rounded text-sm no-underline hover:bg-orange-600 transition-colors\",\n                                            children: \"Convert to Receipt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        user?.is_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/admin\",\n                                            className: \"bg-purple-600 text-white px-4 py-2 rounded text-sm no-underline hover:bg-purple-700 transition-colors\",\n                                            children: \"Admin Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Select Company\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"companySelect\",\n                                    className: \"block mb-1\",\n                                    children: \"Choose a company:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"companySelect\",\n                                    value: selectedCompany,\n                                    onChange: (e)=>setSelectedCompany(e.target.value),\n                                    required: true,\n                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"Select a company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company1\",\n                                            children: \"Shans Accessories PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company2\",\n                                            children: \"Shans Autosport PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"company3\",\n                                            children: \"Shans Motorstyle PTY LTD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Customer Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerName\",\n                                    className: \"block mb-1\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerName\",\n                                    value: customerName,\n                                    onChange: (e)=>setCustomerName(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerEmail\",\n                                    className: \"block mb-1\",\n                                    children: \"Email:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"customerEmail\",\n                                    value: customerEmail,\n                                    onChange: (e)=>setCustomerEmail(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerAddress\",\n                                    className: \"block mb-1\",\n                                    children: \"Address:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"customerAddress\",\n                                    value: customerAddress,\n                                    onChange: (e)=>setCustomerAddress(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"customerPhone\",\n                                    className: \"block mb-1\",\n                                    children: \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    id: \"customerPhone\",\n                                    value: customerPhone,\n                                    onChange: (e)=>setCustomerPhone(e.target.value),\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"salespersonName\",\n                                    className: \"block mb-1\",\n                                    children: \"Salesperson Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"salespersonName\",\n                                    value: salespersonName,\n                                    onChange: (e)=>setSalespersonName(e.target.value),\n                                    placeholder: \"Enter your name...\",\n                                    className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Shipping Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: sameAsBilling,\n                                                onChange: (e)=>setSameAsBilling(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Same as billing address\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                !sameAsBilling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingName\",\n                                            className: \"block mb-1\",\n                                            children: \"Name:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingName\",\n                                            value: shippingName,\n                                            onChange: (e)=>setShippingName(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingEmail\",\n                                            className: \"block mb-1\",\n                                            children: \"Email:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"shippingEmail\",\n                                            value: shippingEmail,\n                                            onChange: (e)=>setShippingEmail(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingAddress\",\n                                            className: \"block mb-1\",\n                                            children: \"Address:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"shippingAddress\",\n                                            value: shippingAddress,\n                                            onChange: (e)=>setShippingAddress(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"shippingPhone\",\n                                            className: \"block mb-1\",\n                                            children: \"Phone:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            id: \"shippingPhone\",\n                                            value: shippingPhone,\n                                            onChange: (e)=>setShippingPhone(e.target.value),\n                                            className: \"w-full p-2 mb-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Add Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"productSearch\",\n                                            className: \"block mb-1\",\n                                            children: \"Search Products:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"productSearch\",\n                                            value: productSearch,\n                                            onChange: (e)=>setProductSearch(e.target.value),\n                                            placeholder: \"Type to search products...\",\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        showProductList && filteredProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded mt-1 max-h-60 overflow-y-auto z-10 shadow-lg\",\n                                            children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors\",\n                                                    onClick: ()=>{\n                                                        setSelectedProductData(product);\n                                                        setProductSearch(product.item_name);\n                                                        setPrice(product.unit_retail_price);\n                                                        setRoom(product.room_name);\n                                                        setShowProductList(false);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: product.item_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Code: \",\n                                                                product.item_code,\n                                                                \" | Price: \",\n                                                                CURRENCY_SYMBOL,\n                                                                product.unit_retail_price,\n                                                                \" | Stock: \",\n                                                                product.available_stock,\n                                                                \" | Room: \",\n                                                                product.room_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, product.item_code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"quantity\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Quantity:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"quantity\",\n                                                    value: quantity,\n                                                    onChange: (e)=>setQuantity(Math.max(1, parseInt(e.target.value) || 1)),\n                                                    min: \"1\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"price\",\n                                                    className: \"block mb-1\",\n                                                    children: [\n                                                        \"Price (\",\n                                                        CURRENCY_SYMBOL,\n                                                        \"):\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    id: \"price\",\n                                                    value: price,\n                                                    onChange: (e)=>setPrice(parseFloat(e.target.value) || 0),\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"room\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Room:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"room\",\n                                                    value: room,\n                                                    onChange: (e)=>setRoom(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: addProduct,\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\",\n                                    children: \"Add Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Selected Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this),\n                                selectedProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"No products selected yet.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full border-collapse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Room\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Qty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"border border-gray-300 p-2 text-left\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: selectedProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.room_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: product.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: [\n                                                                    CURRENCY_SYMBOL,\n                                                                    product.price * product.quantity\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"border border-gray-300 p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        // Remove product logic\n                                                                        setSelectedProducts((prev)=>prev.filter((_, i)=>i !== index));\n                                                                        showToast('Product removed', 'info');\n                                                                    },\n                                                                    className: \"bg-red-600 text-white px-2 py-1 rounded text-sm hover:bg-red-700 transition-colors\",\n                                                                    children: \"Remove\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-5 border border-gray-300 p-4 rounded\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-gray-800 text-xl mb-3\",\n                                    children: \"Order Summary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"paymentMethod\",\n                                                    className: \"block mb-1\",\n                                                    children: \"Payment Method:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"paymentMethod\",\n                                                    value: paymentMethod,\n                                                    onChange: (e)=>setPaymentMethod(e.target.value),\n                                                    className: \"w-full p-2 border border-gray-300 rounded box-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Cash\",\n                                                            children: \"Cash\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Card\",\n                                                            children: \"Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Bank Transfer\",\n                                                            children: \"Bank Transfer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"Other\",\n                                                            children: \"Other\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: includeTax,\n                                                        onChange: (e)=>setIncludeTax(e.target.checked),\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Include Tax (15%)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"comments\",\n                                            className: \"block mb-1\",\n                                            children: \"Comments:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            id: \"comments\",\n                                            value: comments,\n                                            onChange: (e)=>setComments(e.target.value),\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded box-border resize-vertical\",\n                                            placeholder: \"Additional comments or notes...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 bg-gray-100 p-3 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtotal:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.subtotal\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 15\n                                        }, this),\n                                        includeTax && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Tax (15%):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.tax\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between font-bold text-lg border-t border-gray-300 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        CURRENCY_SYMBOL,\n                                                        totals.total\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Receipt functionality coming soon', 'info'),\n                                    className: \"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors\",\n                                    children: \"Generate Receipt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Quotation functionality coming soon', 'info'),\n                                    className: \"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"Generate Quotation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>showToast('Generate Invoice functionality coming soon', 'info'),\n                                    className: \"bg-purple-600 text-white px-6 py-3 rounded text-lg hover:bg-purple-700 transition-colors\",\n                                    children: \"Generate Invoice\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/LoadingScreen.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction LoadingScreen({ isVisible, message = \"Loading products, please wait...\", error, onRetry }) {\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-800 text-lg font-semibold text-center text-shadow-sm\",\n                children: error ? 'Connection Error' : message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onRetry,\n                        className: \"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\LoadingScreen.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Mb2FkaW5nU2NyZWVuLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBU2UsU0FBU0EsY0FBYyxFQUNwQ0MsU0FBUyxFQUNUQyxVQUFVLGtDQUFrQyxFQUM1Q0MsS0FBSyxFQUNMQyxPQUFPLEVBQ1k7SUFDbkIsSUFBSSxDQUFDSCxXQUFXLE9BQU87SUFFdkIscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUVmLDhEQUFDQztnQkFBRUQsV0FBVTswQkFDVkgsUUFBUSxxQkFBcUJEOzs7Ozs7WUFHL0JDLHVCQUNDOztrQ0FDRSw4REFBQ0U7d0JBQUlDLFdBQVU7a0NBQ1pIOzs7Ozs7b0JBRUZDLHlCQUNDLDhEQUFDSTt3QkFDQ0MsU0FBU0w7d0JBQ1RFLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7O0FBUWIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcVmlkZW9zXFx3b3Jrc3BhY2VcXHNoYW5zLWZyb250ZW5kXFxuZXh0IGpzIGFwcFxcc3JjXFxjb21wb25lbnRzXFxMb2FkaW5nU2NyZWVuLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmludGVyZmFjZSBMb2FkaW5nU2NyZWVuUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW47XG4gIG1lc3NhZ2U/OiBzdHJpbmc7XG4gIGVycm9yPzogc3RyaW5nO1xuICBvblJldHJ5PzogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZ1NjcmVlbih7IFxuICBpc1Zpc2libGUsIFxuICBtZXNzYWdlID0gXCJMb2FkaW5nIHByb2R1Y3RzLCBwbGVhc2Ugd2FpdC4uLlwiLCBcbiAgZXJyb3IsXG4gIG9uUmV0cnkgXG59OiBMb2FkaW5nU2NyZWVuUHJvcHMpIHtcbiAgaWYgKCFpc1Zpc2libGUpIHJldHVybiBudWxsO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtZnVsbCBiZy13aGl0ZSBiZy1vcGFjaXR5LTk4IHotNTAgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LXN0YXJ0IGl0ZW1zLWNlbnRlciBwdC0yMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBib3JkZXItMTAgYm9yZGVyLWdyYXktMzAwIGJvcmRlci10LWJsdWUtNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbWItNiBzaGFkb3ctbGdcIj48L2Rpdj5cbiAgICAgIFxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1zaGFkb3ctc21cIj5cbiAgICAgICAge2Vycm9yID8gJ0Nvbm5lY3Rpb24gRXJyb3InIDogbWVzc2FnZX1cbiAgICAgIDwvcD5cbiAgICAgIFxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtMzAwIHRleHQtcmVkLTgwMCBwLTMgcm91bmRlZCBtdC00IG1heC13LXhzIHRleHQtY2VudGVyIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICB7b25SZXRyeSAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmV0cnl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTUgcHgtNiBweS0zIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgYm9yZGVyLW5vbmUgcm91bmRlZCBjdXJzb3ItcG9pbnRlciB0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOmJnLWJsdWUtNzAwIGhvdmVyOi10cmFuc2xhdGUteS0wLjUgaG92ZXI6c2hhZG93LWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUmV0cnlcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nU2NyZWVuIiwiaXNWaXNpYmxlIiwibWVzc2FnZSIsImVycm9yIiwib25SZXRyeSIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children, requireAdmin = false, redirectTo = '/login' }) {\n    const { user, isLoading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [shouldRender, setShouldRender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (isLoading) {\n                return; // Still checking authentication\n            }\n            if (!isAuthenticated) {\n                router.push(redirectTo);\n                return;\n            }\n            if (requireAdmin && user && !user.is_admin) {\n                router.push('/'); // Redirect non-admin users to home\n                return;\n            }\n            setShouldRender(true);\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        user,\n        requireAdmin,\n        router,\n        redirectTo\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this);\n    }\n    if (!shouldRender) {\n        return null; // Don't render anything while redirecting\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToastContainer),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, onRemove }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    onRemove(message.id);\n                }\n            }[\"Toast.useEffect.timer\"], 3000);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        message.id,\n        onRemove\n    ]);\n    const bgColor = {\n        success: 'bg-green-600',\n        error: 'bg-red-600',\n        info: 'bg-blue-600'\n    }[message.type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${bgColor} text-white px-4 py-2 rounded opacity-95 text-sm toast`,\n        children: message.message\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastContainer({ messages, onRemove }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-5 right-5 z-50 flex flex-col gap-2\",\n        children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                message: message,\n                onRemove: onRemove\n            }, message.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n// Hook for managing toast messages\nfunction useToast() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n    };\n    const removeToast = (id)=>{\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== id));\n    };\n    return {\n        messages,\n        showToast,\n        removeToast\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./src/lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkAuth = async ()=>{\n        try {\n            setIsLoading(true);\n            const authenticated = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.checkAuthStatus)({\n                redirectOnFail: false\n            });\n            if (authenticated) {\n                const currentUser = (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                setUser(currentUser);\n                setIsAuthenticated(true);\n                return true;\n            } else {\n                setUser(null);\n                setIsAuthenticated(false);\n                return false;\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n            setUser(null);\n            setIsAuthenticated(false);\n            return false;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(`${_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.API_BASE_URL}/auth/login`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // Store authentication token and user info\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('authToken', data.token);\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('userInfo', JSON.stringify(data.user));\n                _lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.storage.set('lastAuthCheck', Date.now().toString());\n                setUser(data.user);\n                setIsAuthenticated(true);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    message: data.message || 'Login failed'\n                };\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                message: 'Network error. Please try again.'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setIsAuthenticated(false);\n        (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_2__.logout)();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        isLoading,\n        isAuthenticated,\n        login,\n        logout,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_BASE_URL: () => (/* binding */ API_BASE_URL),\n/* harmony export */   checkAuthStatus: () => (/* binding */ checkAuthStatus),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   initAuth: () => (/* binding */ initAuth),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n// Shared authentication utilities for Next.js app\nconst API_BASE_URL = 'https://shans-backend.onrender.com/api';\n// Storage utility with fallback\nconst storage = {\n    get: function(key) {\n        if (true) return null;\n        try {\n            return localStorage.getItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            return sessionStorage.getItem(key);\n        }\n    },\n    set: function(key, value) {\n        if (true) return;\n        try {\n            localStorage.setItem(key, value);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.setItem(key, value);\n        }\n    },\n    remove: function(key) {\n        if (true) return;\n        try {\n            localStorage.removeItem(key);\n        } catch (e) {\n            console.warn('localStorage not available, using sessionStorage');\n            sessionStorage.removeItem(key);\n        }\n    }\n};\n// Enhanced authentication check with better error handling\nasync function checkAuthStatus(options = {}) {\n    const { redirectOnFail = true, showLoading = false, retryCount = 2, timeout = 8000 } = options;\n    const token = storage.get('authToken');\n    const userInfo = storage.get('userInfo');\n    // If no token, redirect immediately\n    if (!token) {\n        if (redirectOnFail && \"undefined\" !== 'undefined') {}\n        return false;\n    }\n    // If we have userInfo cached and it's recent, trust it temporarily\n    if (userInfo) {\n        try {\n            const user = JSON.parse(userInfo);\n            const lastCheck = storage.get('lastAuthCheck');\n            const now = Date.now();\n            // If last check was less than 5 minutes ago, skip server validation\n            if (lastCheck && now - parseInt(lastCheck) < 5 * 60 * 1000) {\n                return true;\n            }\n        } catch (e) {\n            console.warn('Error parsing cached user info:', e);\n        }\n    }\n    // Validate token with server\n    for(let attempt = 0; attempt <= retryCount; attempt++){\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), timeout);\n            // Add cache busting parameter to prevent cached responses\n            const cacheBuster = `?_t=${Date.now()}`;\n            const response = await fetch(`${API_BASE_URL}/auth/me${cacheBuster}`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                // Update cached user info and timestamp\n                storage.set('userInfo', JSON.stringify(data.user));\n                storage.set('lastAuthCheck', Date.now().toString());\n                return true;\n            } else if (response.status === 401 || response.status === 403) {\n                // Token is invalid, clear storage and redirect\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            } else {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n        } catch (error) {\n            console.warn(`Auth check attempt ${attempt + 1} failed:`, error);\n            // If this is the last attempt, handle the failure\n            if (attempt === retryCount) {\n                // If we have cached user info, use it as fallback\n                if (userInfo) {\n                    console.log('Using cached authentication due to network issues');\n                    return true;\n                }\n                // No cached info and all attempts failed\n                console.error('Authentication failed after all retries:', error);\n                storage.remove('authToken');\n                storage.remove('userInfo');\n                storage.remove('lastAuthCheck');\n                if (redirectOnFail && \"undefined\" !== 'undefined') {}\n                return false;\n            }\n            // Wait before retry (exponential backoff)\n            await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n    }\n    return false;\n}\n// Check if user is admin\nfunction isAdmin() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return false;\n    try {\n        const user = JSON.parse(userInfo);\n        return Boolean(user.is_admin);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return false;\n    }\n}\n// Get current user info\nfunction getCurrentUser() {\n    const userInfo = storage.get('userInfo');\n    if (!userInfo) return null;\n    try {\n        return JSON.parse(userInfo);\n    } catch (e) {\n        console.error('Error parsing user info:', e);\n        return null;\n    }\n}\n// Logout function\nfunction logout() {\n    const token = storage.get('authToken');\n    // Call logout endpoint (don't wait for response)\n    if (token) {\n        fetch(`${API_BASE_URL}/auth/logout`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`\n            }\n        }).catch((error)=>{\n            console.warn('Logout API call failed:', error);\n        });\n    }\n    // Clear storage and redirect\n    storage.remove('authToken');\n    storage.remove('userInfo');\n    storage.remove('lastAuthCheck');\n    if (false) {}\n}\n// Initialize authentication for a page\nasync function initAuth(options = {}) {\n    const { requireAuth = true, showLoading = false } = options;\n    if (!requireAuth) return true;\n    try {\n        const isAuthenticated = await checkAuthStatus({\n            showLoading,\n            redirectOnFail: true\n        });\n        return isAuthenticated;\n    } catch (error) {\n        console.error('Auth initialization failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2F1dGgtdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLGtEQUFrRDtBQUMzQyxNQUFNQSxlQUFlLHlDQUF5QztBQWNyRSxnQ0FBZ0M7QUFDekIsTUFBTUMsVUFBVTtJQUNyQkMsS0FBSyxTQUFTQyxHQUFXO1FBQ3ZCLElBQUksSUFBNkIsRUFBRSxPQUFPO1FBQzFDLElBQUk7WUFDRixPQUFPQyxhQUFhQyxPQUFPLENBQUNGO1FBQzlCLEVBQUUsT0FBT0csR0FBRztZQUNWQyxRQUFRQyxJQUFJLENBQUM7WUFDYixPQUFPQyxlQUFlSixPQUFPLENBQUNGO1FBQ2hDO0lBQ0Y7SUFDQU8sS0FBSyxTQUFTUCxHQUFXLEVBQUVRLEtBQWE7UUFDdEMsSUFBSSxJQUE2QixFQUFFO1FBQ25DLElBQUk7WUFDRlAsYUFBYVEsT0FBTyxDQUFDVCxLQUFLUTtRQUM1QixFQUFFLE9BQU9MLEdBQUc7WUFDVkMsUUFBUUMsSUFBSSxDQUFDO1lBQ2JDLGVBQWVHLE9BQU8sQ0FBQ1QsS0FBS1E7UUFDOUI7SUFDRjtJQUNBRSxRQUFRLFNBQVNWLEdBQVc7UUFDMUIsSUFBSSxJQUE2QixFQUFFO1FBQ25DLElBQUk7WUFDRkMsYUFBYVUsVUFBVSxDQUFDWDtRQUMxQixFQUFFLE9BQU9HLEdBQUc7WUFDVkMsUUFBUUMsSUFBSSxDQUFDO1lBQ2JDLGVBQWVLLFVBQVUsQ0FBQ1g7UUFDNUI7SUFDRjtBQUNGLEVBQUU7QUFTRiwyREFBMkQ7QUFDcEQsZUFBZVksZ0JBQWdCQyxVQUE0QixDQUFDLENBQUM7SUFDbEUsTUFBTSxFQUNKQyxpQkFBaUIsSUFBSSxFQUNyQkMsY0FBYyxLQUFLLEVBQ25CQyxhQUFhLENBQUMsRUFDZEMsVUFBVSxJQUFJLEVBQ2YsR0FBR0o7SUFFSixNQUFNSyxRQUFRcEIsUUFBUUMsR0FBRyxDQUFDO0lBQzFCLE1BQU1vQixXQUFXckIsUUFBUUMsR0FBRyxDQUFDO0lBRTdCLG9DQUFvQztJQUNwQyxJQUFJLENBQUNtQixPQUFPO1FBQ1YsSUFBSUosa0JBQWtCLGdCQUFrQixhQUFhLEVBRXBEO1FBQ0QsT0FBTztJQUNUO0lBRUEsbUVBQW1FO0lBQ25FLElBQUlLLFVBQVU7UUFDWixJQUFJO1lBQ0YsTUFBTUksT0FBT0MsS0FBS0MsS0FBSyxDQUFDTjtZQUN4QixNQUFNTyxZQUFZNUIsUUFBUUMsR0FBRyxDQUFDO1lBQzlCLE1BQU00QixNQUFNQyxLQUFLRCxHQUFHO1lBRXBCLG9FQUFvRTtZQUNwRSxJQUFJRCxhQUFhLE1BQU9HLFNBQVNILGFBQWMsSUFBSSxLQUFLLE1BQU07Z0JBQzVELE9BQU87WUFDVDtRQUNGLEVBQUUsT0FBT3ZCLEdBQUc7WUFDVkMsUUFBUUMsSUFBSSxDQUFDLG1DQUFtQ0Y7UUFDbEQ7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixJQUFLLElBQUkyQixVQUFVLEdBQUdBLFdBQVdkLFlBQVljLFVBQVc7UUFDdEQsSUFBSTtZQUNGLE1BQU1DLGFBQWEsSUFBSUM7WUFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUlsQjtZQUV2RCwwREFBMEQ7WUFDMUQsTUFBTW1CLGNBQWMsQ0FBQyxJQUFJLEVBQUVSLEtBQUtELEdBQUcsSUFBSTtZQUN2QyxNQUFNVSxXQUFXLE1BQU1DLE1BQU0sR0FBR3pDLGFBQWEsUUFBUSxFQUFFdUMsYUFBYSxFQUFFO2dCQUNwRUcsU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVyQixPQUFPO2dCQUNwQztnQkFDQXNCLFFBQVFULFdBQVdTLE1BQU07WUFDM0I7WUFFQUMsYUFBYVI7WUFFYixJQUFJSSxTQUFTSyxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNTixTQUFTTyxJQUFJO2dCQUNoQyx3Q0FBd0M7Z0JBQ3hDOUMsUUFBUVMsR0FBRyxDQUFDLFlBQVlpQixLQUFLcUIsU0FBUyxDQUFDRixLQUFLcEIsSUFBSTtnQkFDaER6QixRQUFRUyxHQUFHLENBQUMsaUJBQWlCcUIsS0FBS0QsR0FBRyxHQUFHbUIsUUFBUTtnQkFDaEQsT0FBTztZQUNULE9BQU8sSUFBSVQsU0FBU1UsTUFBTSxLQUFLLE9BQU9WLFNBQVNVLE1BQU0sS0FBSyxLQUFLO2dCQUM3RCwrQ0FBK0M7Z0JBQy9DakQsUUFBUVksTUFBTSxDQUFDO2dCQUNmWixRQUFRWSxNQUFNLENBQUM7Z0JBQ2ZaLFFBQVFZLE1BQU0sQ0FBQztnQkFFZixJQUFJSSxrQkFBa0IsZ0JBQWtCLGFBQWEsRUFFcEQ7Z0JBQ0QsT0FBTztZQUNULE9BQU87Z0JBQ0wsTUFBTSxJQUFJa0MsTUFBTSxDQUFDLG9CQUFvQixFQUFFWCxTQUFTVSxNQUFNLEVBQUU7WUFDMUQ7UUFDRixFQUFFLE9BQU9FLE9BQU87WUFDZDdDLFFBQVFDLElBQUksQ0FBQyxDQUFDLG1CQUFtQixFQUFFeUIsVUFBVSxFQUFFLFFBQVEsQ0FBQyxFQUFFbUI7WUFFMUQsa0RBQWtEO1lBQ2xELElBQUluQixZQUFZZCxZQUFZO2dCQUMxQixrREFBa0Q7Z0JBQ2xELElBQUlHLFVBQVU7b0JBQ1pmLFFBQVE4QyxHQUFHLENBQUM7b0JBQ1osT0FBTztnQkFDVDtnQkFFQSx5Q0FBeUM7Z0JBQ3pDOUMsUUFBUTZDLEtBQUssQ0FBQyw0Q0FBNENBO2dCQUMxRG5ELFFBQVFZLE1BQU0sQ0FBQztnQkFDZlosUUFBUVksTUFBTSxDQUFDO2dCQUNmWixRQUFRWSxNQUFNLENBQUM7Z0JBRWYsSUFBSUksa0JBQWtCLGdCQUFrQixhQUFhLEVBSXBEO2dCQUNELE9BQU87WUFDVDtZQUVBLDBDQUEwQztZQUMxQyxNQUFNLElBQUlxQyxRQUFRQyxDQUFBQSxVQUFXbEIsV0FBV2tCLFNBQVNDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHeEIsV0FBVztRQUMxRTtJQUNGO0lBRUEsT0FBTztBQUNUO0FBRUEseUJBQXlCO0FBQ2xCLFNBQVN5QjtJQUNkLE1BQU1wQyxXQUFXckIsUUFBUUMsR0FBRyxDQUFDO0lBQzdCLElBQUksQ0FBQ29CLFVBQVUsT0FBTztJQUV0QixJQUFJO1FBQ0YsTUFBTUksT0FBT0MsS0FBS0MsS0FBSyxDQUFDTjtRQUN4QixPQUFPcUMsUUFBUWpDLEtBQUtrQyxRQUFRO0lBQzlCLEVBQUUsT0FBT3RELEdBQUc7UUFDVkMsUUFBUTZDLEtBQUssQ0FBQyw0QkFBNEI5QztRQUMxQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLHdCQUF3QjtBQUNqQixTQUFTdUQ7SUFDZCxNQUFNdkMsV0FBV3JCLFFBQVFDLEdBQUcsQ0FBQztJQUM3QixJQUFJLENBQUNvQixVQUFVLE9BQU87SUFFdEIsSUFBSTtRQUNGLE9BQU9LLEtBQUtDLEtBQUssQ0FBQ047SUFDcEIsRUFBRSxPQUFPaEIsR0FBRztRQUNWQyxRQUFRNkMsS0FBSyxDQUFDLDRCQUE0QjlDO1FBQzFDLE9BQU87SUFDVDtBQUNGO0FBRUEsa0JBQWtCO0FBQ1gsU0FBU3dEO0lBQ2QsTUFBTXpDLFFBQVFwQixRQUFRQyxHQUFHLENBQUM7SUFFMUIsaURBQWlEO0lBQ2pELElBQUltQixPQUFPO1FBQ1RvQixNQUFNLEdBQUd6QyxhQUFhLFlBQVksQ0FBQyxFQUFFO1lBQ25DK0QsUUFBUTtZQUNSckIsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVyQixPQUFPO1lBQ3BDO1FBQ0YsR0FBRzJDLEtBQUssQ0FBQ1osQ0FBQUE7WUFDUDdDLFFBQVFDLElBQUksQ0FBQywyQkFBMkI0QztRQUMxQztJQUNGO0lBRUEsNkJBQTZCO0lBQzdCbkQsUUFBUVksTUFBTSxDQUFDO0lBQ2ZaLFFBQVFZLE1BQU0sQ0FBQztJQUNmWixRQUFRWSxNQUFNLENBQUM7SUFDZixJQUFJLEtBQTZCLEVBQUUsRUFFbEM7QUFDSDtBQUVBLHVDQUF1QztBQUNoQyxlQUFlb0QsU0FBU2pELFVBQTRCLENBQUMsQ0FBQztJQUMzRCxNQUFNLEVBQUVrRCxjQUFjLElBQUksRUFBRWhELGNBQWMsS0FBSyxFQUFFLEdBQUdGO0lBRXBELElBQUksQ0FBQ2tELGFBQWEsT0FBTztJQUV6QixJQUFJO1FBQ0YsTUFBTUMsa0JBQWtCLE1BQU1wRCxnQkFBZ0I7WUFDNUNHO1lBQ0FELGdCQUFnQjtRQUNsQjtRQUNBLE9BQU9rRDtJQUNULEVBQUUsT0FBT2YsT0FBTztRQUNkN0MsUUFBUTZDLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXFZpZGVvc1xcd29ya3NwYWNlXFxzaGFucy1mcm9udGVuZFxcbmV4dCBqcyBhcHBcXHNyY1xcbGliXFxhdXRoLXV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBhdXRoZW50aWNhdGlvbiB1dGlsaXRpZXMgZm9yIE5leHQuanMgYXBwXG5leHBvcnQgY29uc3QgQVBJX0JBU0VfVVJMID0gJ2h0dHBzOi8vc2hhbnMtYmFja2VuZC5vbnJlbmRlci5jb20vYXBpJztcblxuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBpc19hZG1pbjogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoUmVzcG9uc2Uge1xuICB0b2tlbjogc3RyaW5nO1xuICB1c2VyOiBVc2VyO1xufVxuXG4vLyBTdG9yYWdlIHV0aWxpdHkgd2l0aCBmYWxsYmFja1xuZXhwb3J0IGNvbnN0IHN0b3JhZ2UgPSB7XG4gIGdldDogZnVuY3Rpb24oa2V5OiBzdHJpbmcpOiBzdHJpbmcgfCBudWxsIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsO1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ2xvY2FsU3RvcmFnZSBub3QgYXZhaWxhYmxlLCB1c2luZyBzZXNzaW9uU3RvcmFnZScpO1xuICAgICAgcmV0dXJuIHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oa2V5KTtcbiAgICB9XG4gIH0sXG4gIHNldDogZnVuY3Rpb24oa2V5OiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcbiAgICB0cnkge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSk7XG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgY29uc29sZS53YXJuKCdsb2NhbFN0b3JhZ2Ugbm90IGF2YWlsYWJsZSwgdXNpbmcgc2Vzc2lvblN0b3JhZ2UnKTtcbiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSk7XG4gICAgfVxuICB9LFxuICByZW1vdmU6IGZ1bmN0aW9uKGtleTogc3RyaW5nKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgY29uc29sZS53YXJuKCdsb2NhbFN0b3JhZ2Ugbm90IGF2YWlsYWJsZSwgdXNpbmcgc2Vzc2lvblN0b3JhZ2UnKTtcbiAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcbiAgICB9XG4gIH1cbn07XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aENoZWNrT3B0aW9ucyB7XG4gIHJlZGlyZWN0T25GYWlsPzogYm9vbGVhbjtcbiAgc2hvd0xvYWRpbmc/OiBib29sZWFuO1xuICByZXRyeUNvdW50PzogbnVtYmVyO1xuICB0aW1lb3V0PzogbnVtYmVyO1xufVxuXG4vLyBFbmhhbmNlZCBhdXRoZW50aWNhdGlvbiBjaGVjayB3aXRoIGJldHRlciBlcnJvciBoYW5kbGluZ1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrQXV0aFN0YXR1cyhvcHRpb25zOiBBdXRoQ2hlY2tPcHRpb25zID0ge30pOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgY29uc3QgeyBcbiAgICByZWRpcmVjdE9uRmFpbCA9IHRydWUsIFxuICAgIHNob3dMb2FkaW5nID0gZmFsc2UsIFxuICAgIHJldHJ5Q291bnQgPSAyLFxuICAgIHRpbWVvdXQgPSA4MDAwIFxuICB9ID0gb3B0aW9ucztcbiAgXG4gIGNvbnN0IHRva2VuID0gc3RvcmFnZS5nZXQoJ2F1dGhUb2tlbicpO1xuICBjb25zdCB1c2VySW5mbyA9IHN0b3JhZ2UuZ2V0KCd1c2VySW5mbycpO1xuICBcbiAgLy8gSWYgbm8gdG9rZW4sIHJlZGlyZWN0IGltbWVkaWF0ZWx5XG4gIGlmICghdG9rZW4pIHtcbiAgICBpZiAocmVkaXJlY3RPbkZhaWwgJiYgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBcbiAgLy8gSWYgd2UgaGF2ZSB1c2VySW5mbyBjYWNoZWQgYW5kIGl0J3MgcmVjZW50LCB0cnVzdCBpdCB0ZW1wb3JhcmlseVxuICBpZiAodXNlckluZm8pIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlciA9IEpTT04ucGFyc2UodXNlckluZm8pO1xuICAgICAgY29uc3QgbGFzdENoZWNrID0gc3RvcmFnZS5nZXQoJ2xhc3RBdXRoQ2hlY2snKTtcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgICBcbiAgICAgIC8vIElmIGxhc3QgY2hlY2sgd2FzIGxlc3MgdGhhbiA1IG1pbnV0ZXMgYWdvLCBza2lwIHNlcnZlciB2YWxpZGF0aW9uXG4gICAgICBpZiAobGFzdENoZWNrICYmIChub3cgLSBwYXJzZUludChsYXN0Q2hlY2spKSA8IDUgKiA2MCAqIDEwMDApIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgY29uc29sZS53YXJuKCdFcnJvciBwYXJzaW5nIGNhY2hlZCB1c2VyIGluZm86JywgZSk7XG4gICAgfVxuICB9XG4gIFxuICAvLyBWYWxpZGF0ZSB0b2tlbiB3aXRoIHNlcnZlclxuICBmb3IgKGxldCBhdHRlbXB0ID0gMDsgYXR0ZW1wdCA8PSByZXRyeUNvdW50OyBhdHRlbXB0KyspIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4gY29udHJvbGxlci5hYm9ydCgpLCB0aW1lb3V0KTtcbiAgICAgIFxuICAgICAgLy8gQWRkIGNhY2hlIGJ1c3RpbmcgcGFyYW1ldGVyIHRvIHByZXZlbnQgY2FjaGVkIHJlc3BvbnNlc1xuICAgICAgY29uc3QgY2FjaGVCdXN0ZXIgPSBgP190PSR7RGF0ZS5ub3coKX1gO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2F1dGgvbWUke2NhY2hlQnVzdGVyfWAsIHtcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWBcbiAgICAgICAgfSxcbiAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgLy8gVXBkYXRlIGNhY2hlZCB1c2VyIGluZm8gYW5kIHRpbWVzdGFtcFxuICAgICAgICBzdG9yYWdlLnNldCgndXNlckluZm8nLCBKU09OLnN0cmluZ2lmeShkYXRhLnVzZXIpKTtcbiAgICAgICAgc3RvcmFnZS5zZXQoJ2xhc3RBdXRoQ2hlY2snLCBEYXRlLm5vdygpLnRvU3RyaW5nKCkpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEgfHwgcmVzcG9uc2Uuc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgLy8gVG9rZW4gaXMgaW52YWxpZCwgY2xlYXIgc3RvcmFnZSBhbmQgcmVkaXJlY3RcbiAgICAgICAgc3RvcmFnZS5yZW1vdmUoJ2F1dGhUb2tlbicpO1xuICAgICAgICBzdG9yYWdlLnJlbW92ZSgndXNlckluZm8nKTtcbiAgICAgICAgc3RvcmFnZS5yZW1vdmUoJ2xhc3RBdXRoQ2hlY2snKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChyZWRpcmVjdE9uRmFpbCAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oYEF1dGggY2hlY2sgYXR0ZW1wdCAke2F0dGVtcHQgKyAxfSBmYWlsZWQ6YCwgZXJyb3IpO1xuICAgICAgXG4gICAgICAvLyBJZiB0aGlzIGlzIHRoZSBsYXN0IGF0dGVtcHQsIGhhbmRsZSB0aGUgZmFpbHVyZVxuICAgICAgaWYgKGF0dGVtcHQgPT09IHJldHJ5Q291bnQpIHtcbiAgICAgICAgLy8gSWYgd2UgaGF2ZSBjYWNoZWQgdXNlciBpbmZvLCB1c2UgaXQgYXMgZmFsbGJhY2tcbiAgICAgICAgaWYgKHVzZXJJbmZvKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1VzaW5nIGNhY2hlZCBhdXRoZW50aWNhdGlvbiBkdWUgdG8gbmV0d29yayBpc3N1ZXMnKTtcbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgLy8gTm8gY2FjaGVkIGluZm8gYW5kIGFsbCBhdHRlbXB0cyBmYWlsZWRcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aGVudGljYXRpb24gZmFpbGVkIGFmdGVyIGFsbCByZXRyaWVzOicsIGVycm9yKTtcbiAgICAgICAgc3RvcmFnZS5yZW1vdmUoJ2F1dGhUb2tlbicpO1xuICAgICAgICBzdG9yYWdlLnJlbW92ZSgndXNlckluZm8nKTtcbiAgICAgICAgc3RvcmFnZS5yZW1vdmUoJ2xhc3RBdXRoQ2hlY2snKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChyZWRpcmVjdE9uRmFpbCAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJztcbiAgICAgICAgICB9LCAyMDAwKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFdhaXQgYmVmb3JlIHJldHJ5IChleHBvbmVudGlhbCBiYWNrb2ZmKVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIE1hdGgucG93KDIsIGF0dGVtcHQpICogMTAwMCkpO1xuICAgIH1cbiAgfVxuICBcbiAgcmV0dXJuIGZhbHNlO1xufVxuXG4vLyBDaGVjayBpZiB1c2VyIGlzIGFkbWluXG5leHBvcnQgZnVuY3Rpb24gaXNBZG1pbigpOiBib29sZWFuIHtcbiAgY29uc3QgdXNlckluZm8gPSBzdG9yYWdlLmdldCgndXNlckluZm8nKTtcbiAgaWYgKCF1c2VySW5mbykgcmV0dXJuIGZhbHNlO1xuICBcbiAgdHJ5IHtcbiAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZSh1c2VySW5mbyk7XG4gICAgcmV0dXJuIEJvb2xlYW4odXNlci5pc19hZG1pbik7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIHVzZXIgaW5mbzonLCBlKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuLy8gR2V0IGN1cnJlbnQgdXNlciBpbmZvXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q3VycmVudFVzZXIoKTogVXNlciB8IG51bGwge1xuICBjb25zdCB1c2VySW5mbyA9IHN0b3JhZ2UuZ2V0KCd1c2VySW5mbycpO1xuICBpZiAoIXVzZXJJbmZvKSByZXR1cm4gbnVsbDtcbiAgXG4gIHRyeSB7XG4gICAgcmV0dXJuIEpTT04ucGFyc2UodXNlckluZm8pO1xuICB9IGNhdGNoIChlKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyB1c2VyIGluZm86JywgZSk7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLy8gTG9nb3V0IGZ1bmN0aW9uXG5leHBvcnQgZnVuY3Rpb24gbG9nb3V0KCk6IHZvaWQge1xuICBjb25zdCB0b2tlbiA9IHN0b3JhZ2UuZ2V0KCdhdXRoVG9rZW4nKTtcbiAgXG4gIC8vIENhbGwgbG9nb3V0IGVuZHBvaW50IChkb24ndCB3YWl0IGZvciByZXNwb25zZSlcbiAgaWYgKHRva2VuKSB7XG4gICAgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9hdXRoL2xvZ291dGAsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICB9XG4gICAgfSkuY2F0Y2goZXJyb3IgPT4ge1xuICAgICAgY29uc29sZS53YXJuKCdMb2dvdXQgQVBJIGNhbGwgZmFpbGVkOicsIGVycm9yKTtcbiAgICB9KTtcbiAgfVxuICBcbiAgLy8gQ2xlYXIgc3RvcmFnZSBhbmQgcmVkaXJlY3RcbiAgc3RvcmFnZS5yZW1vdmUoJ2F1dGhUb2tlbicpO1xuICBzdG9yYWdlLnJlbW92ZSgndXNlckluZm8nKTtcbiAgc3RvcmFnZS5yZW1vdmUoJ2xhc3RBdXRoQ2hlY2snKTtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJztcbiAgfVxufVxuXG4vLyBJbml0aWFsaXplIGF1dGhlbnRpY2F0aW9uIGZvciBhIHBhZ2VcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBpbml0QXV0aChvcHRpb25zOiBBdXRoQ2hlY2tPcHRpb25zID0ge30pOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgY29uc3QgeyByZXF1aXJlQXV0aCA9IHRydWUsIHNob3dMb2FkaW5nID0gZmFsc2UgfSA9IG9wdGlvbnM7XG4gIFxuICBpZiAoIXJlcXVpcmVBdXRoKSByZXR1cm4gdHJ1ZTtcbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gYXdhaXQgY2hlY2tBdXRoU3RhdHVzKHsgXG4gICAgICBzaG93TG9hZGluZywgXG4gICAgICByZWRpcmVjdE9uRmFpbDogdHJ1ZSBcbiAgICB9KTtcbiAgICByZXR1cm4gaXNBdXRoZW50aWNhdGVkO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggaW5pdGlhbGl6YXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJBUElfQkFTRV9VUkwiLCJzdG9yYWdlIiwiZ2V0Iiwia2V5IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImUiLCJjb25zb2xlIiwid2FybiIsInNlc3Npb25TdG9yYWdlIiwic2V0IiwidmFsdWUiLCJzZXRJdGVtIiwicmVtb3ZlIiwicmVtb3ZlSXRlbSIsImNoZWNrQXV0aFN0YXR1cyIsIm9wdGlvbnMiLCJyZWRpcmVjdE9uRmFpbCIsInNob3dMb2FkaW5nIiwicmV0cnlDb3VudCIsInRpbWVvdXQiLCJ0b2tlbiIsInVzZXJJbmZvIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwidXNlciIsIkpTT04iLCJwYXJzZSIsImxhc3RDaGVjayIsIm5vdyIsIkRhdGUiLCJwYXJzZUludCIsImF0dGVtcHQiLCJjb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImFib3J0IiwiY2FjaGVCdXN0ZXIiLCJyZXNwb25zZSIsImZldGNoIiwiaGVhZGVycyIsInNpZ25hbCIsImNsZWFyVGltZW91dCIsIm9rIiwiZGF0YSIsImpzb24iLCJzdHJpbmdpZnkiLCJ0b1N0cmluZyIsInN0YXR1cyIsIkVycm9yIiwiZXJyb3IiLCJsb2ciLCJQcm9taXNlIiwicmVzb2x2ZSIsIk1hdGgiLCJwb3ciLCJpc0FkbWluIiwiQm9vbGVhbiIsImlzX2FkbWluIiwiZ2V0Q3VycmVudFVzZXIiLCJsb2dvdXQiLCJtZXRob2QiLCJjYXRjaCIsImluaXRBdXRoIiwicmVxdWlyZUF1dGgiLCJpc0F1dGhlbnRpY2F0ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CVideos%5Cworkspace%5Cshans-frontend%5Cnext%20js%20app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();