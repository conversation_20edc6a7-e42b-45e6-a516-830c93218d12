(()=>{var e={};e.id=481,e.ids=[481],e.modules={553:(e,t,r)=>{Promise.resolve().then(r.bind(r,933))},769:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(687),n=r(3210),o=r(6189),a=r(3213);function i({children:e,requireAdmin:t=!1,redirectTo:r="/login"}){let{user:i,isLoading:l,isAuthenticated:u}=(0,a.A)();(0,o.useRouter)();let[d,c]=(0,n.useState)(!1);return l?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):d?(0,s.jsx)(s.Fragment,{children:e}):null}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},933:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\manage-users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-users\\page.tsx","default")},1135:()=>{},1439:(e,t,r)=>{"use strict";r.d(t,{HW:()=>a,IG:()=>n,JR:()=>s,ri:()=>i,z:()=>o});let s="https://shans-backend.onrender.com/api",n={get:function(e){return null},set:function(e,t){},remove:function(e){}};async function o(e={}){let{redirectOnFail:t=!0,showLoading:r=!1,retryCount:a=2,timeout:i=8e3}=e,l=n.get("authToken"),u=n.get("userInfo");if(!l)return!1;if(u)try{JSON.parse(u);let e=n.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=a;e++)try{let e=new AbortController,t=setTimeout(()=>e.abort(),i),r=`?_t=${Date.now()}`,o=await fetch(`${s}/auth/me${r}`,{headers:{Authorization:`Bearer ${l}`},signal:e.signal});if(clearTimeout(t),o.ok){let e=await o.json();return n.set("userInfo",JSON.stringify(e.user)),n.set("lastAuthCheck",Date.now().toString()),!0}if(401===o.status||403===o.status){n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck");break}throw Error(`HTTP error! status: ${o.status}`)}catch(t){if(console.warn(`Auth check attempt ${e+1} failed:`,t),e===a){if(u)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",t),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck"),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function a(){let e=n.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=n.get("authToken");e&&fetch(`${s}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${e}`}}).catch(e=>{console.warn("Logout API call failed:",e)}),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck")}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var s=r(687),n=r(3210),o=r(1439);let a=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,n.useState)(null),[i,l]=(0,n.useState)(!0),[u,d]=(0,n.useState)(!1),c=async()=>{try{if(l(!0),!await (0,o.z)({redirectOnFail:!1}))return r(null),d(!1),!1;{let e=(0,o.HW)();return r(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),r(null),d(!1),!1}finally{l(!1)}},h=async(e,t)=>{try{l(!0);let s=await fetch(`${o.JR}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),n=await s.json();if(s.ok)return o.IG.set("authToken",n.token),o.IG.set("userInfo",JSON.stringify(n.user)),o.IG.set("lastAuthCheck",Date.now().toString()),r(n.user),d(!0),{success:!0};return{success:!1,message:n.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,s.jsx)(a.Provider,{value:{user:t,isLoading:i,isAuthenticated:u,login:h,logout:()=>{r(null),d(!1),(0,o.ri)()},checkAuth:c},children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3964:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4236:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var s=r(7413);r(1135);var n=r(9131);let o={title:"Shans System",description:"Receipt and Quotation Generator",viewport:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","theme-color":"#ffffff","format-detection":"telephone=no"}};function a({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"font-sans leading-relaxed m-0 p-0 bg-gray-100",children:(0,s.jsx)(n.AuthProvider,{children:e})})})}},5127:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},5131:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(687),n=r(6189),o=r(769);function a(){let e=(0,n.useRouter)();return(0,s.jsx)(o.A,{requireAdmin:!0,children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Users"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,s.jsx)("div",{className:"text-blue-600 text-6xl mb-4",children:"\uD83D\uDC65"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"User Management"}),(0,s.jsx)("p",{className:"text-gray-500 mb-6",children:"This feature is coming soon. You'll be able to manage user accounts, permissions, and access levels."}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,s.jsx)("div",{children:"• Create and manage user accounts"}),(0,s.jsx)("div",{children:"• Set admin permissions"}),(0,s.jsx)("div",{children:"• Monitor user activity"}),(0,s.jsx)("div",{children:"• Reset passwords"})]})]})]})})})}},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},7001:(e,t,r)=>{Promise.resolve().then(r.bind(r,5131))},7452:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var s=r(5239),n=r(8088),o=r(8170),a=r.n(o),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let u={children:["",{children:["admin",{children:["manage-users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,933)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-users\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/manage-users/page",pathname:"/admin/manage-users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","useAuth");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\contexts\\AuthContext.tsx","AuthProvider")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[825],()=>r(7452));module.exports=s})();