(()=>{var e={};e.id=259,e.ids=[259],e.modules={27:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,d:()=>o});var r=s(687),n=s(3210);function a({message:e,onRemove:t}){let s={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[e.type];return(0,r.jsx)("div",{className:`${s} text-white px-4 py-2 rounded opacity-95 text-sm toast`,children:e.message})}function i({messages:e,onRemove:t}){return(0,r.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:e.map(e=>(0,r.jsx)(a,{message:e,onRemove:t},e.id))})}function o(){let[e,t]=(0,n.useState)([]);return{messages:e,showToast:(e,s="info")=>{let r=Math.random().toString(36).substr(2,9);t(t=>[...t,{id:r,message:e,type:s}])},removeToast:e=>{t(t=>t.filter(t=>t.id!==e))}}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2185:(e,t,s)=>{"use strict";s.d(t,{FH:()=>a});var r=s(1439);async function n(e,t={}){try{let s=`${r.JR}${e}`,n=await fetch(s,{headers:{"Content-Type":"application/json",...t.headers},...t}),a=await n.json();if(!n.ok)return{success:!1,error:a.message||`HTTP error! status: ${n.status}`};return{success:!0,data:a}}catch(e){return{success:!1,error:e instanceof Error?e.message:"An unknown error occurred"}}}let a={products:{search:async(e,t=1,s=20)=>n(`/products/search?q=${encodeURIComponent(e)}&page=${t}&limit=${s}`),getAll:async(e=1,t=50)=>n(`/products?page=${e}&limit=${t}`),getByCode:async e=>n(`/products/${encodeURIComponent(e)}`)},quotations:{create:async e=>n("/quotations",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/quotations"),getById:async e=>n(`/quotations/${e}`),delete:async e=>n(`/quotations/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-quotation-to-receipt/${e}`,{method:"POST"})},invoices:{create:async e=>n("/invoices",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/invoices"),getById:async e=>n(`/invoices/${e}`),delete:async e=>n(`/invoices/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-invoice-to-receipt/${e}`,{method:"POST"})},receipts:{create:async e=>n("/receipts",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/receipts"),getById:async e=>n(`/receipts/${e}`),delete:async e=>n(`/receipts/${e}`,{method:"DELETE"})},auth:{login:async(e,t)=>n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}),checkStatus:async()=>n("/auth/status"),verifyToken:async e=>n("/auth/verify",{headers:{Authorization:`Bearer ${e}`}})},server:{checkStatus:async()=>n("/status")}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3286:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=s(5239),n=s(8088),a=s(8170),i=s.n(a),o=s(893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(t,c);let l={children:["",{children:["quotation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6917)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\quotation\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\quotation\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/quotation/page",pathname:"/quotation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6557:(e,t,s)=>{Promise.resolve().then(s.bind(s,6917))},6917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\quotation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\quotation\\page.tsx","default")},8690:(e,t,s)=>{Promise.resolve().then(s.bind(s,8831))},8831:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(687),n=s(3210),a=s(6189),i=s(769),o=s(27),c=s(1439),l=s(2185),d=s(9265);let m={company1:{key:"company1",name:"Shans Accessories PTY LTD",bankingInformation:`
      First National Bank<br>
      Account :  ***********<br>
      Branch code 257705<br>
      Swift code FIRNZAJJ
    `},company2:{key:"company2",name:"Shans Autosport PTY LTD",bankingInformation:`
      Business Account<br>
      Capitec Current Account<br>
      Account: **********
    `},company3:{key:"company3",name:"Shans Motorstyle PTY LTD",bankingInformation:`
      SHANS MOTORSTYLE (PTY) LTD<br>
      Gold Business Account<br>
      Account Number: ***********<br>
      Branch Code: 250655<br>
      Swift Code: FIRNZAJJ
    `}};function u(){var e;let t=(0,a.useRouter)();(0,a.useSearchParams)();let{messages:s,showToast:u,removeToast:p}=(0,o.d)(),[x,h]=(0,n.useState)(null),[g,b]=(0,n.useState)(!0),[y,f]=(0,n.useState)(""),{mutate:j,loading:v,error:N}=(0,d.L2)(l.FH.quotations.create),T=e=>`R${parseFloat(e.toString()).toLocaleString("en-ZA",{minimumFractionDigits:0,maximumFractionDigits:0})}`,I=()=>{if(!x)return{subtotal:0,tax:0,total:0};let e=0,t=0,s=0;return x.selectedProducts.forEach(r=>{let n=r.price*r.quantity;if(x.includeTax){let a=r.price/1.15,i=r.price-a;e+=a*r.quantity,t+=i*r.quantity,s+=n}else e+=n,s+=n}),{subtotal:Math.round(e),tax:Math.round(t),total:Math.round(s)}},w=async()=>{if(x)try{let e=I(),s=m[x.companyKey],r={reference_number:y,company:{name:s.name,bankingInformation:s.bankingInformation},billing:x.customerInfo.billing,shipping:x.customerInfo.shipping,items:x.selectedProducts.map(e=>({item_code:e.item_code,item_name:e.name,room_name:e.room_name,quantity:e.quantity,unit_price_including_tax:x.includeTax?e.price:1.15*e.price,unit_price_excluding_tax:x.includeTax?e.price/1.15:e.price,total_price:e.price*e.quantity,tax_per_product:x.includeTax?e.price-e.price/1.15:.15*e.price})),subtotal:e.subtotal,tax:e.tax,total:e.total,payment_method:x.paymentMethod,comments:x.comments,salesperson_name:x.salespersonName,include_tax:x.includeTax};await j(r)&&(u("Quotation created successfully!","success"),c.IG.remove("customerInfo"),c.IG.remove("selectedProducts"),c.IG.remove("paymentMethod"),c.IG.remove("comments"),c.IG.remove("salespersonName"),c.IG.remove("includeTax"),setTimeout(()=>{t.push("/")},2e3))}catch(e){console.error("Error creating quotation:",e),u(`Error: ${e instanceof Error?e.message:"Failed to create quotation"}`,"error")}};if(g)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading quotation..."})]})})});if(!x)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"No order data found"}),(0,r.jsx)("button",{onClick:()=>t.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Go to Home"})]})})});let S=I(),P=m[x.companyKey];return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100 p-5",children:[(0,r.jsx)(o.A,{messages:s,onRemove:p}),(0,r.jsxs)("div",{className:"flex gap-5 mb-5",children:[(0,r.jsx)("button",{onClick:()=>t.push("/"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Home"}),(0,r.jsx)("button",{onClick:()=>{sessionStorage.setItem("editingOrder","true"),t.push("/")},className:"text-blue-600 hover:text-blue-800 transition-colors",children:"✏️ Edit Order"})]}),(0,r.jsxs)("div",{id:"pdf-1",className:"max-w-4xl mx-auto bg-white p-8 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"QUOTATION"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Reference: ",y]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Date: ",(e=new Date,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e))]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:P.name}),(0,r.jsx)("div",{className:"text-sm text-gray-600",dangerouslySetInnerHTML:{__html:P.bankingInformation}})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"BILL TO"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[(0,r.jsx)("p",{children:x.customerInfo.billing.name}),(0,r.jsx)("p",{children:x.customerInfo.billing.email}),(0,r.jsx)("p",{children:x.customerInfo.billing.address}),(0,r.jsx)("p",{children:x.customerInfo.billing.phone})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"SHIP TO"}),(0,r.jsx)("div",{className:"text-gray-600",children:x.customerInfo.shipping?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:x.customerInfo.shipping.name}),(0,r.jsx)("p",{children:x.customerInfo.shipping.email}),(0,r.jsx)("p",{children:x.customerInfo.shipping.address}),(0,r.jsx)("p",{children:x.customerInfo.shipping.phone})]}):(0,r.jsx)("p",{children:"Same as billing address"})})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"border border-gray-300",children:[(0,r.jsxs)("div",{className:"bg-gray-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300",children:[(0,r.jsx)("div",{className:"col-span-4",children:"DESCRIPTION"}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:"QTY"}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:"UNIT PRICE"}),x.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:"TAX"}),(0,r.jsx)("div",{className:`${x.includeTax?"col-span-3":"col-span-5"} text-right`,children:"TOTAL"})]}),x.selectedProducts.map((e,t)=>{let s=e.price*e.quantity,n=x.includeTax?e.price/1.15:e.price,a=x.includeTax?e.price-n:0;return(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0",children:[(0,r.jsxs)("div",{className:"col-span-4",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Room: ",e.room_name]})]}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:e.quantity}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:T(x.includeTax?n:e.price)}),x.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:T(a)}),(0,r.jsx)("div",{className:`${x.includeTax?"col-span-3":"col-span-5"} text-right font-medium`,children:T(s)})]},t)})]})}),(0,r.jsx)("div",{className:"flex justify-end mb-8",children:(0,r.jsxs)("div",{className:"w-64",children:[(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Subtotal:"}),(0,r.jsx)("span",{children:T(S.subtotal)})]}),x.includeTax&&(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Tax (15%):"}),(0,r.jsx)("span",{children:T(S.tax)})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2 font-bold text-lg",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{children:T(S.total)})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Method"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.paymentMethod})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Salesperson"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.salespersonName||"N/A"})]})]}),x.comments&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Comments"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.comments})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:w,disabled:v,className:"bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2",children:[v&&(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),v?"Creating...":"Confirm Quotation"]}),(0,r.jsx)("button",{onClick:()=>window.print(),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"\uD83D\uDCF8 Print/Save"})]})]})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[825,227],()=>s(3286));module.exports=r})();