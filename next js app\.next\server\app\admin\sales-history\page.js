(()=>{var e={};e.id=800,e.ids=[800],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(687);function a({isVisible:e,message:t="Loading products, please wait...",error:s,onRetry:a}){return e?(0,r.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,r.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:s?"Connection Error":t}),s&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:s}),a&&(0,r.jsx)("button",{onClick:a,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},1042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\sales-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\sales-history\\page.tsx","default")},1580:(e,t,s)=>{Promise.resolve().then(s.bind(s,6340))},1852:(e,t,s)=>{Promise.resolve().then(s.bind(s,1042))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6340:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(687),a=s(3210),n=s(6189),d=s(769),l=s(854),i=s(9265);function o(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)(""),[o,c]=(0,a.useState)(""),[x,m]=(0,a.useState)(""),{data:p=[],loading:h,error:u}=(0,i.U9)("/sales"),g=p.filter(e=>{let s=e.customer_name.toLowerCase().includes(t.toLowerCase())||e.order_id.toLowerCase().includes(t.toLowerCase())||e.salesperson_name.toLowerCase().includes(t.toLowerCase()),r=!o||e.created_at.startsWith(o),a=!x||e.payment_method===x;return s&&r&&a}),y=g.reduce((e,t)=>e+t.total_amount,0),b=g.length>0?y/g.length:0,f=[...new Set(p.map(e=>e.payment_method))];return h?(0,r.jsx)(d.A,{requireAdmin:!0,children:(0,r.jsx)(l.A,{isVisible:!0,message:"Loading sales history..."})}):(0,r.jsx)(d.A,{requireAdmin:!0,children:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Sales History"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Total Sales"}),(0,r.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["R ",y.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Number of Orders"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:g.length})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Average Order"}),(0,r.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["R ",b.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Period"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:o||"All Time"})]})]}),(0,r.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>s(e.target.value),placeholder:"Search by customer, order ID, or salesperson...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,r.jsx)("input",{type:"date",value:o,onChange:e=>c(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,r.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Methods"}),f.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{onClick:()=>{s(""),c(""),m("")},className:"w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors",children:"Clear Filters"})})]})}),g.length>0?(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salesperson"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Payment Method"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.order_id}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.customer_name}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.salesperson_name}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.payment_method})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["R ",e.total_amount.toLocaleString()]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-900 mr-3",children:"View Details"}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-900",children:"Export"})]})]},e.id))})]})})}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCA"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Sales Records Found"}),(0,r.jsx)("p",{className:"text-gray-500",children:t||o||x?"No sales records match your current filters.":"No sales records are available yet."})]})]})})})}},9018:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(5239),a=s(8088),n=s(8170),d=s.n(n),l=s(893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let o={children:["",{children:["admin",{children:["sales-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1042)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\sales-history\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\sales-history\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/sales-history/page",pathname:"/admin/sales-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[825,227],()=>s(9018));module.exports=r})();