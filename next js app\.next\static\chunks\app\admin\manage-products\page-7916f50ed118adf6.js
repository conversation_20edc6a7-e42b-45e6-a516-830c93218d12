(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,AuthProvider:()=>d});var a=r(5155),l=r(2115),s=r(2799);let o=(0,l.createContext)(void 0);function n(){let e=(0,l.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function d(e){let{children:t}=e,[r,n]=(0,l.useState)(null),[d,i]=(0,l.useState)(!0),[c,u]=(0,l.useState)(!1),m=async()=>{try{if(i(!0),!await (0,s.z)({redirectOnFail:!1}))return n(null),u(!1),!1;{let e=(0,s.HW)();return n(e),u(!0),!0}}catch(e){return console.error("Auth check failed:",e),n(null),u(!1),!1}finally{i(!1)}},x=async(e,t)=>{try{i(!0);let r=await fetch("".concat(s.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return s.IG.set("authToken",a.token),s.IG.set("userInfo",JSON.stringify(a.user)),s.IG.set("lastAuthCheck",Date.now().toString()),n(a.user),u(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{i(!1)}};return(0,l.useEffect)(()=>{m()},[]),(0,a.jsx)(o.Provider,{value:{user:r,isLoading:d,isAuthenticated:c,login:x,logout:()=>{n(null),u(!1),(0,s.ri)()},checkAuth:m},children:t})}},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>o,IG:()=>l,JR:()=>a,ri:()=>n,z:()=>s});let a="https://shans-backend.onrender.com/api",l={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:s=2,timeout:o=8e3}=e,n=l.get("authToken"),d=l.get("userInfo");if(!n)return t&&(window.location.href="/login"),!1;if(d)try{JSON.parse(d);let e=l.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=s;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),o),s="?_t=".concat(Date.now()),d=await fetch("".concat(a,"/auth/me").concat(s),{headers:{Authorization:"Bearer ".concat(n)},signal:e.signal});if(clearTimeout(r),d.ok){let e=await d.json();return l.set("userInfo",JSON.stringify(e.user)),l.set("lastAuthCheck",Date.now().toString()),!0}if(401===d.status||403===d.status){l.remove("authToken"),l.remove("userInfo"),l.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(d.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===s){if(d)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),l.remove("authToken"),l.remove("userInfo"),l.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function o(){let e=l.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function n(){let e=l.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),l.remove("authToken"),l.remove("userInfo"),l.remove("lastAuthCheck"),window.location.href="/login"}},2987:(e,t,r)=>{Promise.resolve().then(r.bind(r,6393))},3213:(e,t,r)=>{"use strict";r.d(t,{L2:()=>o,U9:()=>s,gf:()=>l});var a=r(2115);function l(e){let[t,r]=(0,a.useState)({data:null,loading:!1,error:null}),l=(0,a.useCallback)(async function(){for(var t=arguments.length,a=Array(t),l=0;l<t;l++)a[l]=arguments[l];r(e=>({...e,loading:!0,error:null}));try{let t=await e(...a);if(t.success&&t.data)return r({data:t.data,loading:!1,error:null}),t.data;return r({data:null,loading:!1,error:t.error||"An unknown error occurred"}),null}catch(e){return r({data:null,loading:!1,error:e instanceof Error?e.message:"An unknown error occurred"}),null}},[e]),s=(0,a.useCallback)(()=>{r({data:null,loading:!1,error:null})},[]);return{...t,execute:l,reset:s}}function s(e){let t=l(e),r=(0,a.useCallback)(e=>{t.data&&d(t=>({...t,data:t.data?[...t.data,...e]:e}))},[t.data]),s=(0,a.useCallback)((e,t)=>{d(r=>{if(!r.data)return r;let a=[...r.data];return a[e]=t,{...r,data:a}})},[]),o=(0,a.useCallback)(e=>{d(t=>{if(!t.data)return t;let r=t.data.filter((t,r)=>r!==e);return{...t,data:r}})},[]),[n,d]=(0,a.useState)({data:null,loading:!1,error:null});return{...t,append:r,updateItem:s,removeItem:o}}function o(e){let[t,r]=(0,a.useState)(!1),[l,s]=(0,a.useState)(null);return{mutate:(0,a.useCallback)(async t=>{r(!0),s(null);try{let a=await e(t);if(a.success&&a.data)return r(!1),a.data;return s(a.error||"An unknown error occurred"),r(!1),null}catch(e){return s(e instanceof Error?e.message:"An unknown error occurred"),r(!1),null}},[e]),loading:t,error:l,reset:(0,a.useCallback)(()=>{r(!1),s(null)},[])}}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(5155),l=r(2115),s=r(5695),o=r(9053),n=r(7960),d=r(3213);function i(){let e=(0,s.useRouter)(),[t,r]=(0,l.useState)(""),[i,u]=(0,l.useState)(!1),[m,x]=(0,l.useState)(!1),[h,p]=(0,l.useState)(null),[g,b]=(0,l.useState)({carBrand:"",productCategory:"",carModel:"",room:"",colourTape:""}),{data:y=[],loading:v,error:f,refetch:j}=(0,d.U9)("/products"),{data:w=[],loading:N}=(0,d.U9)("/rooms"),{data:_=[],loading:k}=(0,d.U9)("/categories"),{data:C=[],loading:S}=(0,d.U9)("/car-brands"),{data:A=[],loading:I}=(0,d.U9)("/car-models"),P=(0,d.L2)("/products","POST"),T=(0,d.L2)("/products","PUT"),E=(0,d.L2)("/products","DELETE"),L=y.filter(e=>{var r,a,l;let s=e.item_name.toLowerCase().includes(t.toLowerCase())||e.item_code.toLowerCase().includes(t.toLowerCase()),o=!g.carBrand||(null==(r=e.car_brand_id)?void 0:r.toString())===g.carBrand,n=!g.productCategory||e.product_category===g.productCategory,d=!g.carModel||(null==(a=e.car_model_id)?void 0:a.toString())===g.carModel,i=!g.room||(null==(l=e.room_id)?void 0:l.toString())===g.room,c=!g.colourTape||e.colour_tape===g.colourTape;return s&&o&&n&&d&&i&&c}),R=async e=>{try{await P.mutate(e),u(!1),j()}catch(e){console.error("Error adding product:",e)}},M=async e=>{if(h)try{await T.mutate(e,{endpoint:"/products/".concat(h.id)}),x(!1),p(null),j()}catch(e){console.error("Error updating product:",e)}},B=async e=>{if(confirm("Are you sure you want to delete this product?"))try{await E.mutate(void 0,{endpoint:"/products/".concat(e)}),j()}catch(e){console.error("Error deleting product:",e)}},D=e=>{p(e),x(!0)};return v||N||k||S||I?(0,a.jsx)(o.A,{requireAdmin:!0,children:(0,a.jsx)(n.A,{isVisible:!0,message:"Loading products..."})}):(0,a.jsx)(o.A,{requireAdmin:!0,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Products"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,a.jsxs)("button",{onClick:()=>u(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"+"})," Add Product"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"⚙️"})," Manage Categories"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDE97"})," Manage Car Brands"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDE98"})," Manage Car Models"]})]}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Brand"}),(0,a.jsxs)("select",{value:g.carBrand,onChange:e=>b(t=>({...t,carBrand:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Brands"}),C.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),(0,a.jsxs)("select",{value:g.productCategory,onChange:e=>b(t=>({...t,productCategory:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Categories"}),_.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Model"}),(0,a.jsxs)("select",{value:g.carModel,onChange:e=>b(t=>({...t,carModel:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Models"}),A.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room"}),(0,a.jsxs)("select",{value:g.room,onChange:e=>b(t=>({...t,room:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Rooms"}),w.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search products...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Code"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Room"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Model"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Retail Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:L.map(e=>{var t,r,l,s,o;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.item_code}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(t=w.find(t=>t.id===e.room_id))?void 0:t.name)||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.item_name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(r=C.find(t=>t.id===e.car_brand_id))?void 0:r.name)||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(null==(l=A.find(t=>t.id===e.car_model_id))?void 0:l.name)||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:null==(s=e.product_category)?void 0:s.replace(/_/g," ")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.available_stock}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["R ",null==(o=e.unit_retail_price)?void 0:o.toLocaleString()]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>D(e),className:"text-blue-600 hover:text-blue-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>B(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e.id)})})]})})}),i&&(0,a.jsx)(c,{isOpen:i,onClose:()=>u(!1),onSubmit:R,rooms:w,categories:_,carBrands:C,carModels:A,title:"Add Product"}),m&&h&&(0,a.jsx)(c,{isOpen:m,onClose:()=>{x(!1),p(null)},onSubmit:M,rooms:w,categories:_,carBrands:C,carModels:A,title:"Edit Product",initialData:h})]})})})}function c(e){let{isOpen:t,onClose:r,onSubmit:s,rooms:o,categories:n,carBrands:d,carModels:i,title:c,initialData:u}=e,[m,x]=(0,l.useState)({item_code:(null==u?void 0:u.item_code)||"",room_id:(null==u?void 0:u.room_id)||0,item_name:(null==u?void 0:u.item_name)||"",car_brand_id:(null==u?void 0:u.car_brand_id)||0,car_model_id:(null==u?void 0:u.car_model_id)||0,product_category:(null==u?void 0:u.product_category)||"",available_stock:(null==u?void 0:u.available_stock)||0,low_stock_threshold:(null==u?void 0:u.low_stock_threshold)||5,colour_tape:(null==u?void 0:u.colour_tape)||"#000000",location:(null==u?void 0:u.location)||"",unit_retail_price:(null==u?void 0:u.unit_retail_price)||0,wholesale_price:(null==u?void 0:u.wholesale_price)||0,unit_cost:(null==u?void 0:u.unit_cost)||0,supplier_code:(null==u?void 0:u.supplier_code)||"",additional_comments:(null==u?void 0:u.additional_comments)||""}),h=(e,t)=>{x(r=>({...r,[e]:t}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:c}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 text-2xl",children:"\xd7"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(m)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Item Code *"}),(0,a.jsx)("input",{type:"text",value:m.item_code,onChange:e=>h("item_code",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room *"}),(0,a.jsxs)("select",{value:m.room_id,onChange:e=>h("room_id",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a room"}),o.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Item Name *"}),(0,a.jsx)("input",{type:"text",value:m.item_name,onChange:e=>h("item_name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Brand"}),(0,a.jsxs)("select",{value:m.car_brand_id,onChange:e=>h("car_brand_id",parseInt(e.target.value)||void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Select a brand"}),d.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Model"}),(0,a.jsxs)("select",{value:m.car_model_id,onChange:e=>h("car_model_id",parseInt(e.target.value)||void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Select a model"}),i.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Category *"}),(0,a.jsxs)("select",{value:m.product_category,onChange:e=>h("product_category",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),n.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Available Stock"}),(0,a.jsx)("input",{type:"number",value:m.available_stock,onChange:e=>h("available_stock",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Low Stock Threshold"}),(0,a.jsx)("input",{type:"number",value:m.low_stock_threshold,onChange:e=>h("low_stock_threshold",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Colour Tape"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"color",value:m.colour_tape,onChange:e=>h("colour_tape",e.target.value),className:"w-16 h-10 border border-gray-300 rounded-md"}),(0,a.jsx)("input",{type:"text",value:m.colour_tape,onChange:e=>h("colour_tape",e.target.value),className:"flex-1 border border-gray-300 rounded-md px-3 py-2",placeholder:"#000000"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),(0,a.jsx)("input",{type:"text",value:m.location,onChange:e=>h("location",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Unit Retail Price (R)"}),(0,a.jsx)("input",{type:"number",value:m.unit_retail_price,onChange:e=>h("unit_retail_price",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wholesale Price (R)"}),(0,a.jsx)("input",{type:"number",value:m.wholesale_price,onChange:e=>h("wholesale_price",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Unit Cost (R)"}),(0,a.jsx)("input",{type:"number",value:m.unit_cost,onChange:e=>h("unit_cost",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Supplier Code"}),(0,a.jsx)("input",{type:"text",value:m.supplier_code,onChange:e=>h("supplier_code",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Comments"}),(0,a.jsx)("textarea",{value:m.additional_comments,onChange:e=>h("additional_comments",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",rows:3})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[u?"Update":"Add"," Product"]})]})]})]})})}):null}},7960:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155);function l(e){let{isVisible:t,message:r="Loading products, please wait...",error:l,onRetry:s}=e;return t?(0,a.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,a.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:l?"Connection Error":r}),l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:l}),s&&(0,a.jsx)("button",{onClick:s,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(5155),l=r(2115),s=r(5695),o=r(283);function n(e){let{children:t,requireAdmin:r=!1,redirectTo:n="/login"}=e,{user:d,isLoading:i,isAuthenticated:c}=(0,o.A)(),u=(0,s.useRouter)(),[m,x]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{if(!i){if(!c)return void u.push(n);if(r&&d&&!d.is_admin)return void u.push("/");x(!0)}},[c,i,d,r,u,n]),i)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):m?(0,a.jsx)(a.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(2987)),_N_E=e.O()}]);