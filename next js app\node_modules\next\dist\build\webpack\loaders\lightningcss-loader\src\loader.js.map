{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/loader.ts"], "sourcesContent": ["import type { LoaderContext } from 'webpack'\nimport { getTargets } from './utils'\nimport {\n  getImportCode,\n  type ApiParam,\n  type ApiReplacement,\n  type CssExport,\n  type CssImport,\n  getModuleCode,\n  getExportCode,\n} from './codegen'\nimport {\n  getFilter,\n  getPreRequester,\n  isDataUrl,\n  isUrlRequestable,\n  requestify,\n  resolveRequests,\n} from '../../css-loader/src/utils'\nimport { stringifyRequest } from '../../../stringify-request'\nimport { ECacheKey } from './interface'\n\nconst encoder = new TextEncoder()\n\nfunction createUrlAndImportVisitor(\n  visitorOptions: any,\n  apis: ApiParam[],\n  imports: CssImport[],\n  replacements: ApiReplacement[],\n  replacedUrls: Map<number, string>,\n  replacedImportUrls: Map<number, string>\n) {\n  const importUrlToNameMap = new Map<string, string>()\n\n  let hasUrlImportHelper = false\n  const urlToNameMap = new Map()\n  const urlToReplacementMap = new Map()\n  let urlIndex = -1\n  let importUrlIndex = -1\n\n  function handleUrl(u: { url: string; loc: unknown }): unknown {\n    let url = u.url\n    const needKeep = visitorOptions.urlFilter(url)\n\n    if (!needKeep) {\n      return u\n    }\n\n    if (isDataUrl(url)) {\n      return u\n    }\n\n    urlIndex++\n\n    replacedUrls.set(urlIndex, url)\n    url = `__NEXT_LIGHTNINGCSS_LOADER_URL_REPLACE_${urlIndex}__`\n\n    const [, query, hashOrQuery] = url.split(/(\\?)?#/, 3)\n\n    const queryParts = url.split('!')\n    let prefix: string | undefined\n\n    if (queryParts.length > 1) {\n      url = queryParts.pop()!\n      prefix = queryParts.join('!')\n    }\n\n    let hash = query ? '?' : ''\n    hash += hashOrQuery ? `#${hashOrQuery}` : ''\n\n    if (!hasUrlImportHelper) {\n      imports.push({\n        type: 'get_url_import',\n        importName: '___CSS_LOADER_GET_URL_IMPORT___',\n        url: JSON.stringify(\n          require.resolve('../../css-loader/src/runtime/getUrl.js')\n        ),\n        index: -1,\n      })\n\n      hasUrlImportHelper = true\n    }\n\n    const newUrl = prefix ? `${prefix}!${url}` : url\n    let importName = urlToNameMap.get(newUrl)\n\n    if (!importName) {\n      importName = `___CSS_LOADER_URL_IMPORT_${urlToNameMap.size}___`\n      urlToNameMap.set(newUrl, importName)\n\n      imports.push({\n        type: 'url',\n        importName,\n        url: JSON.stringify(newUrl),\n        index: urlIndex,\n      })\n    }\n    // This should be true for string-urls in image-set\n    const needQuotes = false\n\n    const replacementKey = JSON.stringify({ newUrl, hash, needQuotes })\n    let replacementName = urlToReplacementMap.get(replacementKey)\n\n    if (!replacementName) {\n      replacementName = `___CSS_LOADER_URL_REPLACEMENT_${urlToReplacementMap.size}___`\n      urlToReplacementMap.set(replacementKey, replacementName)\n\n      replacements.push({\n        replacementName,\n        importName,\n        hash,\n        needQuotes,\n      })\n    }\n\n    return {\n      loc: u.loc,\n      url: replacementName,\n    }\n  }\n\n  return {\n    Rule: {\n      import(node: any) {\n        if (visitorOptions.importFilter) {\n          const needKeep = visitorOptions.importFilter(\n            node.value.url,\n            node.value.media\n          )\n\n          if (!needKeep) {\n            return node\n          }\n        }\n        let url = node.value.url\n\n        importUrlIndex++\n\n        replacedImportUrls.set(importUrlIndex, url)\n        url = `__NEXT_LIGHTNINGCSS_LOADER_IMPORT_URL_REPLACE_${importUrlIndex}__`\n\n        // TODO: Use identical logic as valueParser.stringify()\n        const media = node.value.media.mediaQueries.length\n          ? JSON.stringify(node.value.media.mediaQueries)\n          : undefined\n        const isRequestable = isUrlRequestable(url)\n        let prefix: string | undefined\n        if (isRequestable) {\n          const queryParts = url.split('!')\n          if (queryParts.length > 1) {\n            url = queryParts.pop()!\n            prefix = queryParts.join('!')\n          }\n        }\n        if (!isRequestable) {\n          apis.push({ url, media })\n          // Bug of lightningcss\n          return { type: 'ignored', value: '' }\n        }\n        const newUrl = prefix ? `${prefix}!${url}` : url\n        let importName = importUrlToNameMap.get(newUrl)\n        if (!importName) {\n          importName = `___CSS_LOADER_AT_RULE_IMPORT_${importUrlToNameMap.size}___`\n          importUrlToNameMap.set(newUrl, importName)\n\n          const importUrl = visitorOptions.urlHandler(newUrl)\n          imports.push({\n            type: 'rule_import',\n            importName,\n            url: importUrl,\n          })\n        }\n        apis.push({ importName, media })\n        // Bug of lightningcss\n        return { type: 'ignored', value: '' }\n      },\n    },\n    Url(node: any) {\n      return handleUrl(node)\n    },\n  }\n}\n\nfunction createIcssVisitor({\n  apis,\n  imports,\n  replacements,\n  replacedUrls,\n  urlHandler,\n}: {\n  apis: ApiParam[]\n  imports: CssImport[]\n  replacements: ApiReplacement[]\n  replacedUrls: Map<number, string>\n  urlHandler: (url: any) => string\n}) {\n  let index = -1\n  let replacementIndex = -1\n\n  return {\n    Declaration: {\n      composes(node: any) {\n        if (node.property === 'unparsed') {\n          return\n        }\n\n        const specifier = node.value.from\n\n        if (specifier?.type !== 'file') {\n          return\n        }\n\n        let url = specifier.value\n        if (!url) {\n          return\n        }\n\n        index++\n\n        replacedUrls.set(index, url)\n        url = `__NEXT_LIGHTNINGCSS_LOADER_ICSS_URL_REPLACE_${index}__`\n\n        const importName = `___CSS_LOADER_ICSS_IMPORT_${imports.length}___`\n        imports.push({\n          type: 'icss_import',\n          importName,\n          icss: true,\n          url: urlHandler(url),\n          index,\n        })\n\n        apis.push({ importName, dedupe: true, index })\n\n        const newNames: string[] = []\n\n        for (const localName of node.value.names) {\n          replacementIndex++\n          const replacementName = `___CSS_LOADER_ICSS_IMPORT_${index}_REPLACEMENT_${replacementIndex}___`\n\n          replacements.push({\n            replacementName,\n            importName,\n            localName,\n          })\n          newNames.push(replacementName)\n        }\n\n        return {\n          property: 'composes',\n          value: {\n            loc: node.value.loc,\n            names: newNames,\n            from: specifier,\n          },\n        }\n      },\n    },\n  }\n}\n\nconst LOADER_NAME = `lightningcss-loader`\nexport async function LightningCssLoader(\n  this: LoaderContext<any>,\n  source: string,\n  prevMap?: Record<string, any>\n): Promise<void> {\n  const done = this.async()\n  const options = this.getOptions()\n  const { implementation, targets: userTargets, ...opts } = options\n\n  options.modules ??= {}\n\n  if (implementation && typeof implementation.transformCss !== 'function') {\n    done(\n      new TypeError(\n        `[${LOADER_NAME}]: options.implementation.transformCss must be an 'lightningcss' transform function. Received ${typeof implementation.transformCss}`\n      )\n    )\n    return\n  }\n\n  if (options.postcss) {\n    const { postcssWithPlugins } = await options.postcss()\n\n    if (postcssWithPlugins?.plugins?.length > 0) {\n      throw new Error(\n        `[${LOADER_NAME}]: experimental.useLightningcss does not work with postcss plugins. Please remove 'useLightningcss: true' from your configuration.`\n      )\n    }\n  }\n\n  const exports: CssExport[] = []\n  const imports: CssImport[] = []\n  const icssImports: CssImport[] = []\n  const apis: ApiParam[] = []\n  const replacements: ApiReplacement[] = []\n\n  if (options.modules?.exportOnlyLocals !== true) {\n    imports.unshift({\n      type: 'api_import',\n      importName: '___CSS_LOADER_API_IMPORT___',\n      url: stringifyRequest(\n        this,\n        require.resolve('../../css-loader/src/runtime/api')\n      ),\n    })\n  }\n  const { loadBindings } = require('next/dist/build/swc')\n\n  const transform =\n    implementation?.transformCss ??\n    (await loadBindings()).css.lightning.transform\n\n  const replacedUrls = new Map<number, string>()\n  const icssReplacedUrls = new Map<number, string>()\n  const replacedImportUrls = new Map<number, string>()\n\n  const urlImportVisitor = createUrlAndImportVisitor(\n    {\n      urlHandler: (url: any) =>\n        stringifyRequest(\n          this,\n          getPreRequester(this)(options.importLoaders ?? 0) + url\n        ),\n      urlFilter: getFilter(options.url, this.resourcePath),\n      importFilter: getFilter(options.import, this.resourcePath),\n\n      context: this.context,\n    },\n    apis,\n    imports,\n    replacements,\n    replacedUrls,\n    replacedImportUrls\n  )\n\n  const icssVisitor = createIcssVisitor({\n    apis,\n    imports: icssImports,\n    replacements,\n    replacedUrls: icssReplacedUrls,\n    urlHandler: (url: string) =>\n      stringifyRequest(\n        this,\n        getPreRequester(this)(options.importLoaders) + url\n      ),\n  })\n\n  // This works by returned visitors are not conflicting.\n  // naive workaround for composeVisitors, as we do not directly depends on lightningcss's npm pkg\n  // but next-swc provides bindings\n  const visitor = {\n    ...urlImportVisitor,\n    ...icssVisitor,\n  }\n\n  try {\n    const {\n      code,\n      map,\n      exports: moduleExports,\n    } = transform({\n      ...opts,\n      visitor,\n      cssModules: options.modules\n        ? {\n            pattern: process.env.__NEXT_TEST_MODE\n              ? '[name]__[local]'\n              : '[name]__[hash]__[local]',\n          }\n        : undefined,\n      filename: this.resourcePath,\n      code: encoder.encode(source),\n      sourceMap: this.sourceMap,\n      targets: getTargets({ targets: userTargets, key: ECacheKey.loader }),\n      inputSourceMap:\n        this.sourceMap && prevMap ? JSON.stringify(prevMap) : undefined,\n      include: 1, // Features.Nesting\n    })\n    let cssCodeAsString = code.toString()\n\n    if (moduleExports) {\n      for (const name in moduleExports) {\n        if (Object.prototype.hasOwnProperty.call(moduleExports, name)) {\n          const v = moduleExports[name]\n          let value = v.name\n          for (const compose of v.composes) {\n            value += ` ${compose.name}`\n          }\n\n          exports.push({\n            name,\n            value,\n          })\n        }\n      }\n    }\n\n    if (replacedUrls.size !== 0) {\n      const urlResolver = this.getResolve({\n        conditionNames: ['asset'],\n        mainFields: ['asset'],\n        mainFiles: [],\n        extensions: [],\n      })\n\n      for (const [index, url] of replacedUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(urlResolver, this.context, [\n          ...new Set([request, url]),\n        ])\n\n        for (const importItem of imports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n\n    if (replacedImportUrls.size !== 0) {\n      const importResolver = this.getResolve({\n        conditionNames: ['style'],\n        extensions: ['.css'],\n        mainFields: ['css', 'style', 'main', '...'],\n        mainFiles: ['index', '...'],\n        restrictions: [/\\.css$/i],\n      })\n\n      for (const [index, url] of replacedImportUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(\n          importResolver,\n          this.context,\n          [...new Set([request, url])]\n        )\n\n        for (const importItem of imports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_IMPORT_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n    if (icssReplacedUrls.size !== 0) {\n      const icssResolver = this.getResolve({\n        conditionNames: ['style'],\n        extensions: [],\n        mainFields: ['css', 'style', 'main', '...'],\n        mainFiles: ['index', '...'],\n      })\n\n      for (const [index, url] of icssReplacedUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(icssResolver, this.context, [\n          ...new Set([url, request]),\n        ])\n\n        for (const importItem of icssImports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_ICSS_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n\n    imports.push(...icssImports)\n\n    const importCode = getImportCode(imports, options)\n    const moduleCode = getModuleCode(\n      { css: cssCodeAsString, map },\n      apis,\n      replacements,\n      options,\n      this\n    )\n    const exportCode = getExportCode(exports, replacements, options)\n\n    const esCode = `${importCode}${moduleCode}${exportCode}`\n\n    done(null, esCode, map && JSON.parse(map.toString()))\n  } catch (error: unknown) {\n    console.error('lightningcss-loader error', error)\n    done(error as Error)\n  }\n}\n\nexport const raw = true\n"], "names": ["Lightning<PERSON>s<PERSON><PERSON>der", "raw", "encoder", "TextEncoder", "createUrlAndImportVisitor", "visitorOptions", "apis", "imports", "replacements", "replacedUrls", "replacedImportUrls", "importUrlToNameMap", "Map", "hasUrlImportHelper", "urlToNameMap", "urlToReplacementMap", "urlIndex", "importUrlIndex", "handleUrl", "u", "url", "<PERSON><PERSON><PERSON>", "url<PERSON><PERSON><PERSON>", "isDataUrl", "set", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "queryParts", "prefix", "length", "pop", "join", "hash", "push", "type", "importName", "JSON", "stringify", "require", "resolve", "index", "newUrl", "get", "size", "needQuotes", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "loc", "Rule", "import", "node", "importFilter", "value", "media", "mediaQueries", "undefined", "isRequestable", "isUrlRequestable", "importUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Url", "createIcssVisitor", "replacementIndex", "Declaration", "composes", "property", "specifier", "from", "icss", "dedupe", "newNames", "localName", "names", "LOADER_NAME", "source", "prevMap", "options", "done", "async", "getOptions", "implementation", "targets", "userTargets", "opts", "modules", "transformCss", "TypeError", "postcss", "postcssWithPlugins", "plugins", "Error", "exports", "icssImports", "exportOnlyLocals", "unshift", "stringifyRequest", "loadBindings", "transform", "css", "lightning", "icssReplacedUrls", "urlImportVisitor", "getPreRequester", "importLoaders", "getFilter", "resourcePath", "context", "icssVisitor", "visitor", "code", "map", "moduleExports", "cssModules", "pattern", "process", "env", "__NEXT_TEST_MODE", "filename", "encode", "sourceMap", "getTargets", "key", "<PERSON><PERSON><PERSON><PERSON>", "loader", "inputSourceMap", "include", "cssCodeAsString", "toString", "name", "Object", "prototype", "hasOwnProperty", "call", "v", "compose", "urlResolver", "getResolve", "conditionNames", "mainFields", "mainFiles", "extensions", "entries", "pathname", "request", "requestify", "rootContext", "resolvedUrl", "resolveRequests", "Set", "importItem", "replace", "importResolver", "restrictions", "icssResolver", "importCode", "getImportCode", "moduleCode", "getModuleCode", "exportCode", "getExportCode", "esCode", "parse", "error", "console"], "mappings": ";;;;;;;;;;;;;;;IAqQsBA,kBAAkB;eAAlBA;;IA2OTC,GAAG;eAAHA;;;uBA/ec;yBASpB;wBAQA;kCAC0B;2BACP;AAE1B,MAAMC,UAAU,IAAIC;AAEpB,SAASC,0BACPC,cAAmB,EACnBC,IAAgB,EAChBC,OAAoB,EACpBC,YAA8B,EAC9BC,YAAiC,EACjCC,kBAAuC;IAEvC,MAAMC,qBAAqB,IAAIC;IAE/B,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,IAAIF;IACzB,MAAMG,sBAAsB,IAAIH;IAChC,IAAII,WAAW,CAAC;IAChB,IAAIC,iBAAiB,CAAC;IAEtB,SAASC,UAAUC,CAAgC;QACjD,IAAIC,MAAMD,EAAEC,GAAG;QACf,MAAMC,WAAWhB,eAAeiB,SAAS,CAACF;QAE1C,IAAI,CAACC,UAAU;YACb,OAAOF;QACT;QAEA,IAAII,IAAAA,iBAAS,EAACH,MAAM;YAClB,OAAOD;QACT;QAEAH;QAEAP,aAAae,GAAG,CAACR,UAAUI;QAC3BA,MAAM,CAAC,uCAAuC,EAAEJ,SAAS,EAAE,CAAC;QAE5D,MAAM,GAAGS,OAAOC,YAAY,GAAGN,IAAIO,KAAK,CAAC,UAAU;QAEnD,MAAMC,aAAaR,IAAIO,KAAK,CAAC;QAC7B,IAAIE;QAEJ,IAAID,WAAWE,MAAM,GAAG,GAAG;YACzBV,MAAMQ,WAAWG,GAAG;YACpBF,SAASD,WAAWI,IAAI,CAAC;QAC3B;QAEA,IAAIC,OAAOR,QAAQ,MAAM;QACzBQ,QAAQP,cAAc,CAAC,CAAC,EAAEA,aAAa,GAAG;QAE1C,IAAI,CAACb,oBAAoB;YACvBN,QAAQ2B,IAAI,CAAC;gBACXC,MAAM;gBACNC,YAAY;gBACZhB,KAAKiB,KAAKC,SAAS,CACjBC,QAAQC,OAAO,CAAC;gBAElBC,OAAO,CAAC;YACV;YAEA5B,qBAAqB;QACvB;QAEA,MAAM6B,SAASb,SAAS,GAAGA,OAAO,CAAC,EAAET,KAAK,GAAGA;QAC7C,IAAIgB,aAAatB,aAAa6B,GAAG,CAACD;QAElC,IAAI,CAACN,YAAY;YACfA,aAAa,CAAC,yBAAyB,EAAEtB,aAAa8B,IAAI,CAAC,GAAG,CAAC;YAC/D9B,aAAaU,GAAG,CAACkB,QAAQN;YAEzB7B,QAAQ2B,IAAI,CAAC;gBACXC,MAAM;gBACNC;gBACAhB,KAAKiB,KAAKC,SAAS,CAACI;gBACpBD,OAAOzB;YACT;QACF;QACA,mDAAmD;QACnD,MAAM6B,aAAa;QAEnB,MAAMC,iBAAiBT,KAAKC,SAAS,CAAC;YAAEI;YAAQT;YAAMY;QAAW;QACjE,IAAIE,kBAAkBhC,oBAAoB4B,GAAG,CAACG;QAE9C,IAAI,CAACC,iBAAiB;YACpBA,kBAAkB,CAAC,8BAA8B,EAAEhC,oBAAoB6B,IAAI,CAAC,GAAG,CAAC;YAChF7B,oBAAoBS,GAAG,CAACsB,gBAAgBC;YAExCvC,aAAa0B,IAAI,CAAC;gBAChBa;gBACAX;gBACAH;gBACAY;YACF;QACF;QAEA,OAAO;YACLG,KAAK7B,EAAE6B,GAAG;YACV5B,KAAK2B;QACP;IACF;IAEA,OAAO;QACLE,MAAM;YACJC,QAAOC,IAAS;gBACd,IAAI9C,eAAe+C,YAAY,EAAE;oBAC/B,MAAM/B,WAAWhB,eAAe+C,YAAY,CAC1CD,KAAKE,KAAK,CAACjC,GAAG,EACd+B,KAAKE,KAAK,CAACC,KAAK;oBAGlB,IAAI,CAACjC,UAAU;wBACb,OAAO8B;oBACT;gBACF;gBACA,IAAI/B,MAAM+B,KAAKE,KAAK,CAACjC,GAAG;gBAExBH;gBAEAP,mBAAmBc,GAAG,CAACP,gBAAgBG;gBACvCA,MAAM,CAAC,8CAA8C,EAAEH,eAAe,EAAE,CAAC;gBAEzE,uDAAuD;gBACvD,MAAMqC,QAAQH,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,CAACzB,MAAM,GAC9CO,KAAKC,SAAS,CAACa,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,IAC5CC;gBACJ,MAAMC,gBAAgBC,IAAAA,wBAAgB,EAACtC;gBACvC,IAAIS;gBACJ,IAAI4B,eAAe;oBACjB,MAAM7B,aAAaR,IAAIO,KAAK,CAAC;oBAC7B,IAAIC,WAAWE,MAAM,GAAG,GAAG;wBACzBV,MAAMQ,WAAWG,GAAG;wBACpBF,SAASD,WAAWI,IAAI,CAAC;oBAC3B;gBACF;gBACA,IAAI,CAACyB,eAAe;oBAClBnD,KAAK4B,IAAI,CAAC;wBAAEd;wBAAKkC;oBAAM;oBACvB,sBAAsB;oBACtB,OAAO;wBAAEnB,MAAM;wBAAWkB,OAAO;oBAAG;gBACtC;gBACA,MAAMX,SAASb,SAAS,GAAGA,OAAO,CAAC,EAAET,KAAK,GAAGA;gBAC7C,IAAIgB,aAAazB,mBAAmBgC,GAAG,CAACD;gBACxC,IAAI,CAACN,YAAY;oBACfA,aAAa,CAAC,6BAA6B,EAAEzB,mBAAmBiC,IAAI,CAAC,GAAG,CAAC;oBACzEjC,mBAAmBa,GAAG,CAACkB,QAAQN;oBAE/B,MAAMuB,YAAYtD,eAAeuD,UAAU,CAAClB;oBAC5CnC,QAAQ2B,IAAI,CAAC;wBACXC,MAAM;wBACNC;wBACAhB,KAAKuC;oBACP;gBACF;gBACArD,KAAK4B,IAAI,CAAC;oBAAEE;oBAAYkB;gBAAM;gBAC9B,sBAAsB;gBACtB,OAAO;oBAAEnB,MAAM;oBAAWkB,OAAO;gBAAG;YACtC;QACF;QACAQ,KAAIV,IAAS;YACX,OAAOjC,UAAUiC;QACnB;IACF;AACF;AAEA,SAASW,kBAAkB,EACzBxD,IAAI,EACJC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZmD,UAAU,EAOX;IACC,IAAInB,QAAQ,CAAC;IACb,IAAIsB,mBAAmB,CAAC;IAExB,OAAO;QACLC,aAAa;YACXC,UAASd,IAAS;gBAChB,IAAIA,KAAKe,QAAQ,KAAK,YAAY;oBAChC;gBACF;gBAEA,MAAMC,YAAYhB,KAAKE,KAAK,CAACe,IAAI;gBAEjC,IAAID,CAAAA,6BAAAA,UAAWhC,IAAI,MAAK,QAAQ;oBAC9B;gBACF;gBAEA,IAAIf,MAAM+C,UAAUd,KAAK;gBACzB,IAAI,CAACjC,KAAK;oBACR;gBACF;gBAEAqB;gBAEAhC,aAAae,GAAG,CAACiB,OAAOrB;gBACxBA,MAAM,CAAC,4CAA4C,EAAEqB,MAAM,EAAE,CAAC;gBAE9D,MAAML,aAAa,CAAC,0BAA0B,EAAE7B,QAAQuB,MAAM,CAAC,GAAG,CAAC;gBACnEvB,QAAQ2B,IAAI,CAAC;oBACXC,MAAM;oBACNC;oBACAiC,MAAM;oBACNjD,KAAKwC,WAAWxC;oBAChBqB;gBACF;gBAEAnC,KAAK4B,IAAI,CAAC;oBAAEE;oBAAYkC,QAAQ;oBAAM7B;gBAAM;gBAE5C,MAAM8B,WAAqB,EAAE;gBAE7B,KAAK,MAAMC,aAAarB,KAAKE,KAAK,CAACoB,KAAK,CAAE;oBACxCV;oBACA,MAAMhB,kBAAkB,CAAC,0BAA0B,EAAEN,MAAM,aAAa,EAAEsB,iBAAiB,GAAG,CAAC;oBAE/FvD,aAAa0B,IAAI,CAAC;wBAChBa;wBACAX;wBACAoC;oBACF;oBACAD,SAASrC,IAAI,CAACa;gBAChB;gBAEA,OAAO;oBACLmB,UAAU;oBACVb,OAAO;wBACLL,KAAKG,KAAKE,KAAK,CAACL,GAAG;wBACnByB,OAAOF;wBACPH,MAAMD;oBACR;gBACF;YACF;QACF;IACF;AACF;AAEA,MAAMO,cAAc,CAAC,mBAAmB,CAAC;AAClC,eAAe1E,mBAEpB2E,MAAc,EACdC,OAA6B;QAiCzBC;IA/BJ,MAAMC,OAAO,IAAI,CAACC,KAAK;IACvB,MAAMF,UAAU,IAAI,CAACG,UAAU;IAC/B,MAAM,EAAEC,cAAc,EAAEC,SAASC,WAAW,EAAE,GAAGC,MAAM,GAAGP;IAE1DA,QAAQQ,OAAO,KAAK,CAAC;IAErB,IAAIJ,kBAAkB,OAAOA,eAAeK,YAAY,KAAK,YAAY;QACvER,KACE,qBAEC,CAFD,IAAIS,UACF,CAAC,CAAC,EAAEb,YAAY,8FAA8F,EAAE,OAAOO,eAAeK,YAAY,EAAE,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEA;QAEF;IACF;IAEA,IAAIT,QAAQW,OAAO,EAAE;YAGfC;QAFJ,MAAM,EAAEA,kBAAkB,EAAE,GAAG,MAAMZ,QAAQW,OAAO;QAEpD,IAAIC,CAAAA,uCAAAA,8BAAAA,mBAAoBC,OAAO,qBAA3BD,4BAA6B3D,MAAM,IAAG,GAAG;YAC3C,MAAM,qBAEL,CAFK,IAAI6D,MACR,CAAC,CAAC,EAAEjB,YAAY,kIAAkI,CAAC,GAD/I,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,MAAMkB,WAAuB,EAAE;IAC/B,MAAMrF,UAAuB,EAAE;IAC/B,MAAMsF,cAA2B,EAAE;IACnC,MAAMvF,OAAmB,EAAE;IAC3B,MAAME,eAAiC,EAAE;IAEzC,IAAIqE,EAAAA,mBAAAA,QAAQQ,OAAO,qBAAfR,iBAAiBiB,gBAAgB,MAAK,MAAM;QAC9CvF,QAAQwF,OAAO,CAAC;YACd5D,MAAM;YACNC,YAAY;YACZhB,KAAK4E,IAAAA,kCAAgB,EACnB,IAAI,EACJzD,QAAQC,OAAO,CAAC;QAEpB;IACF;IACA,MAAM,EAAEyD,YAAY,EAAE,GAAG1D,QAAQ;IAEjC,MAAM2D,YACJjB,CAAAA,kCAAAA,eAAgBK,YAAY,KAC5B,AAAC,CAAA,MAAMW,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACF,SAAS;IAEhD,MAAMzF,eAAe,IAAIG;IACzB,MAAMyF,mBAAmB,IAAIzF;IAC7B,MAAMF,qBAAqB,IAAIE;IAE/B,MAAM0F,mBAAmBlG,0BACvB;QACEwD,YAAY,CAACxC,MACX4E,IAAAA,kCAAgB,EACd,IAAI,EACJO,IAAAA,uBAAe,EAAC,IAAI,EAAE1B,QAAQ2B,aAAa,IAAI,KAAKpF;QAExDE,WAAWmF,IAAAA,iBAAS,EAAC5B,QAAQzD,GAAG,EAAE,IAAI,CAACsF,YAAY;QACnDtD,cAAcqD,IAAAA,iBAAS,EAAC5B,QAAQ3B,MAAM,EAAE,IAAI,CAACwD,YAAY;QAEzDC,SAAS,IAAI,CAACA,OAAO;IACvB,GACArG,MACAC,SACAC,cACAC,cACAC;IAGF,MAAMkG,cAAc9C,kBAAkB;QACpCxD;QACAC,SAASsF;QACTrF;QACAC,cAAc4F;QACdzC,YAAY,CAACxC,MACX4E,IAAAA,kCAAgB,EACd,IAAI,EACJO,IAAAA,uBAAe,EAAC,IAAI,EAAE1B,QAAQ2B,aAAa,IAAIpF;IAErD;IAEA,uDAAuD;IACvD,gGAAgG;IAChG,iCAAiC;IACjC,MAAMyF,UAAU;QACd,GAAGP,gBAAgB;QACnB,GAAGM,WAAW;IAChB;IAEA,IAAI;QACF,MAAM,EACJE,IAAI,EACJC,GAAG,EACHnB,SAASoB,aAAa,EACvB,GAAGd,UAAU;YACZ,GAAGd,IAAI;YACPyB;YACAI,YAAYpC,QAAQQ,OAAO,GACvB;gBACE6B,SAASC,QAAQC,GAAG,CAACC,gBAAgB,GACjC,oBACA;YACN,IACA7D;YACJ8D,UAAU,IAAI,CAACZ,YAAY;YAC3BI,MAAM5G,QAAQqH,MAAM,CAAC5C;YACrB6C,WAAW,IAAI,CAACA,SAAS;YACzBtC,SAASuC,IAAAA,iBAAU,EAAC;gBAAEvC,SAASC;gBAAauC,KAAKC,oBAAS,CAACC,MAAM;YAAC;YAClEC,gBACE,IAAI,CAACL,SAAS,IAAI5C,UAAUvC,KAAKC,SAAS,CAACsC,WAAWpB;YACxDsE,SAAS;QACX;QACA,IAAIC,kBAAkBjB,KAAKkB,QAAQ;QAEnC,IAAIhB,eAAe;YACjB,IAAK,MAAMiB,QAAQjB,cAAe;gBAChC,IAAIkB,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACrB,eAAeiB,OAAO;oBAC7D,MAAMK,IAAItB,aAAa,CAACiB,KAAK;oBAC7B,IAAI5E,QAAQiF,EAAEL,IAAI;oBAClB,KAAK,MAAMM,WAAWD,EAAErE,QAAQ,CAAE;wBAChCZ,SAAS,CAAC,CAAC,EAAEkF,QAAQN,IAAI,EAAE;oBAC7B;oBAEArC,SAAQ1D,IAAI,CAAC;wBACX+F;wBACA5E;oBACF;gBACF;YACF;QACF;QAEA,IAAI5C,aAAamC,IAAI,KAAK,GAAG;YAC3B,MAAM4F,cAAc,IAAI,CAACC,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbC,YAAY,EAAE;YAChB;YAEA,KAAK,MAAM,CAACpG,OAAOrB,IAAI,IAAIX,aAAaqI,OAAO,GAAI;gBACjD,MAAM,CAACC,SAAY,GAAG3H,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMqH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EAACZ,aAAa,IAAI,CAAC7B,OAAO,EAAE;uBAChE,IAAI0C,IAAI;wBAACL;wBAAS5H;qBAAI;iBAC1B;gBAED,KAAK,MAAMkI,cAAc/I,QAAS;oBAChC+I,WAAWlI,GAAG,GAAGkI,WAAWlI,GAAG,CAACmI,OAAO,CACrC,CAAC,uCAAuC,EAAE9G,MAAM,EAAE,CAAC,EACnD0G,eAAe/H;gBAEnB;YACF;QACF;QAEA,IAAIV,mBAAmBkC,IAAI,KAAK,GAAG;YACjC,MAAM4G,iBAAiB,IAAI,CAACf,UAAU,CAAC;gBACrCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY;oBAAC;iBAAO;gBACpBF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3Ba,cAAc;oBAAC;iBAAU;YAC3B;YAEA,KAAK,MAAM,CAAChH,OAAOrB,IAAI,IAAIV,mBAAmBoI,OAAO,GAAI;gBACvD,MAAM,CAACC,SAAY,GAAG3H,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMqH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EACvCI,gBACA,IAAI,CAAC7C,OAAO,EACZ;uBAAI,IAAI0C,IAAI;wBAACL;wBAAS5H;qBAAI;iBAAE;gBAG9B,KAAK,MAAMkI,cAAc/I,QAAS;oBAChC+I,WAAWlI,GAAG,GAAGkI,WAAWlI,GAAG,CAACmI,OAAO,CACrC,CAAC,8CAA8C,EAAE9G,MAAM,EAAE,CAAC,EAC1D0G,eAAe/H;gBAEnB;YACF;QACF;QACA,IAAIiF,iBAAiBzD,IAAI,KAAK,GAAG;YAC/B,MAAM8G,eAAe,IAAI,CAACjB,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY,EAAE;gBACdF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEA,KAAK,MAAM,CAACnG,OAAOrB,IAAI,IAAIiF,iBAAiByC,OAAO,GAAI;gBACrD,MAAM,CAACC,SAAY,GAAG3H,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMqH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EAACM,cAAc,IAAI,CAAC/C,OAAO,EAAE;uBACjE,IAAI0C,IAAI;wBAACjI;wBAAK4H;qBAAQ;iBAC1B;gBAED,KAAK,MAAMM,cAAczD,YAAa;oBACpCyD,WAAWlI,GAAG,GAAGkI,WAAWlI,GAAG,CAACmI,OAAO,CACrC,CAAC,4CAA4C,EAAE9G,MAAM,EAAE,CAAC,EACxD0G,eAAe/H;gBAEnB;YACF;QACF;QAEAb,QAAQ2B,IAAI,IAAI2D;QAEhB,MAAM8D,aAAaC,IAAAA,sBAAa,EAACrJ,SAASsE;QAC1C,MAAMgF,aAAaC,IAAAA,sBAAa,EAC9B;YAAE3D,KAAK4B;YAAiBhB;QAAI,GAC5BzG,MACAE,cACAqE,SACA,IAAI;QAEN,MAAMkF,aAAaC,IAAAA,sBAAa,EAACpE,UAASpF,cAAcqE;QAExD,MAAMoF,SAAS,GAAGN,aAAaE,aAAaE,YAAY;QAExDjF,KAAK,MAAMmF,QAAQlD,OAAO1E,KAAK6H,KAAK,CAACnD,IAAIiB,QAAQ;IACnD,EAAE,OAAOmC,OAAgB;QACvBC,QAAQD,KAAK,CAAC,6BAA6BA;QAC3CrF,KAAKqF;IACP;AACF;AAEO,MAAMlK,MAAM"}