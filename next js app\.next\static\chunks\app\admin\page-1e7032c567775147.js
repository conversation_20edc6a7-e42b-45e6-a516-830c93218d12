(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>l});var o=r(5155),s=r(2115),n=r(2799);let a=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(e){let{children:t}=e,[r,i]=(0,s.useState)(null),[l,c]=(0,s.useState)(!0),[u,d]=(0,s.useState)(!1),h=async()=>{try{if(c(!0),!await (0,n.z)({redirectOnFail:!1}))return i(null),d(!1),!1;{let e=(0,n.HW)();return i(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),i(null),d(!1),!1}finally{c(!1)}},m=async(e,t)=>{try{c(!0);let r=await fetch("".concat(n.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),o=await r.json();if(r.ok)return n.IG.set("authToken",o.token),n.IG.set("userInfo",JSON.stringify(o.user)),n.IG.set("lastAuthCheck",Date.now().toString()),i(o.user),d(!0),{success:!0};return{success:!1,message:o.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{c(!1)}};return(0,s.useEffect)(()=>{h()},[]),(0,o.jsx)(a.Provider,{value:{user:r,isLoading:l,isAuthenticated:u,login:m,logout:()=>{i(null),d(!1),(0,n.ri)()},checkAuth:h},children:t})}},2699:(e,t,r)=>{Promise.resolve().then(r.bind(r,7220))},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>a,IG:()=>s,JR:()=>o,ri:()=>i,z:()=>n});let o="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:n=2,timeout:a=8e3}=e,i=s.get("authToken"),l=s.get("userInfo");if(!i)return t&&(window.location.href="/login"),!1;if(l)try{JSON.parse(l);let e=s.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),a),n="?_t=".concat(Date.now()),l=await fetch("".concat(o,"/auth/me").concat(n),{headers:{Authorization:"Bearer ".concat(i)},signal:e.signal});if(clearTimeout(r),l.ok){let e=await l.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===l.status||403===l.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(l.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===n){if(l)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function a(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=s.get("authToken");e&&fetch("".concat(o,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},5695:(e,t,r)=>{"use strict";var o=r(8999);r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}}),r.o(o,"useSearchParams")&&r.d(t,{useSearchParams:function(){return o.useSearchParams}})},7220:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var o=r(5155),s=r(283),n=r(5695),a=r(2115),i=r(9053),l=r(7960);function c(){let{user:e,logout:t}=(0,s.A)(),r=(0,n.useRouter)(),[c,u]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{if(e&&!e.is_admin)return void r.push("/");let t=setTimeout(()=>{u(!1)},1e3);return()=>clearTimeout(t)},[e,r]),c)?(0,o.jsx)(i.A,{requireAdmin:!0,children:(0,o.jsx)(l.A,{isVisible:!0,message:"Loading dashboard, please wait..."})}):(0,o.jsx)(i.A,{requireAdmin:!0,children:(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50",children:(0,o.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4",children:[(0,o.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 m-0",children:"Welcome, Admin"}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,o.jsx)("button",{onClick:()=>r.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors",children:"Go to User Page"}),(0,o.jsx)("button",{onClick:t,className:"bg-red-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-700 transition-colors",children:"Logout"})]})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:[{id:"manageRooms",title:"Manage Rooms",icon:"\uD83D\uDEAA",href:"/admin/manage-rooms",color:"from-blue-500 to-blue-600",hoverColor:"hover:from-blue-600 hover:to-blue-700"},{id:"manageProducts",title:"Manage Products",icon:"\uD83D\uDCE6",href:"/admin/manage-products",color:"from-green-500 to-green-600",hoverColor:"hover:from-green-600 hover:to-green-700"},{id:"lowStock",title:"Low Stock",icon:"⚠️",href:"/admin/low-stock",color:"from-orange-500 to-orange-600",hoverColor:"hover:from-orange-600 hover:to-orange-700"},{id:"salesHistory",title:"Sales History",icon:"\uD83D\uDCC8",href:"/admin/sales-history",color:"from-red-500 to-red-600",hoverColor:"hover:from-red-600 hover:to-red-700"},{id:"manageUsers",title:"Manage Users",icon:"\uD83D\uDC65",href:"/admin/manage-users",color:"from-purple-500 to-purple-600",hoverColor:"hover:from-purple-600 hover:to-purple-700"},{id:"backupFiles",title:"Backup Files",icon:"☁️",href:"/admin/backup-files",color:"from-indigo-500 to-indigo-600",hoverColor:"hover:from-indigo-600 hover:to-indigo-700"},{id:"expenses",title:"Expenses",icon:"\uD83E\uDDFE",href:"/admin/expenses",color:"from-pink-500 to-pink-600",hoverColor:"hover:from-pink-600 hover:to-pink-700"}].map(e=>(0,o.jsxs)("div",{onClick:()=>r.push(e.href),className:"\n                  bg-gradient-to-br ".concat(e.color," ").concat(e.hoverColor,"\n                  rounded-2xl p-6 text-center cursor-pointer\n                  transform transition-all duration-300 ease-in-out\n                  hover:-translate-y-2 hover:shadow-xl\n                  shadow-lg text-white\n                  min-h-[160px] flex flex-col justify-center items-center\n                "),children:[(0,o.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,o.jsx)("h2",{className:"text-lg font-semibold",children:e.title})]},e.id))}),(0,o.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Quick Actions"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Access frequently used admin functions"})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"System Status"}),(0,o.jsx)("p",{className:"text-green-600 text-sm",children:"All systems operational"})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Recent Activity"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Monitor latest admin actions"})]})]})]})})})}},7960:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var o=r(5155);function s(e){let{isVisible:t,message:r="Loading products, please wait...",error:s,onRetry:n}=e;return t?(0,o.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,o.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,o.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:s?"Connection Error":r}),s&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:s}),n&&(0,o.jsx)("button",{onClick:n,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(5155),s=r(2115),n=r(5695),a=r(283);function i(e){let{children:t,requireAdmin:r=!1,redirectTo:i="/login"}=e,{user:l,isLoading:c,isAuthenticated:u}=(0,a.A)(),d=(0,n.useRouter)(),[h,m]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(!c){if(!u)return void d.push(i);if(r&&l&&!l.is_admin)return void d.push("/");m(!0)}},[u,c,l,r,d,i]),c)?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):h?(0,o.jsx)(o.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(2699)),_N_E=e.O()}]);