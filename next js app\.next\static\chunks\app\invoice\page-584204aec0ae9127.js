(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[598],{7208:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var n=r(5155),t=r(2115),a=r(5695),i=r(9053),c=r(7389),l=r(2799),o=r(5731),d=r(3213);let m={company1:{key:"company1",name:"Shans Accessories PTY LTD",bankingInformation:"\n      First National Bank<br>\n      Account :  ***********<br>\n      Branch code 257705<br>\n      Swift code FIRNZAJJ\n    "},company2:{key:"company2",name:"Shans Autosport PTY LTD",bankingInformation:"\n      Business Account<br>\n      Capitec Current Account<br>\n      Account: **********\n    "},company3:{key:"company3",name:"Shans Motorstyle PTY LTD",bankingInformation:"\n      SHANS MOTORSTYLE (PTY) LTD<br>\n      Gold Business Account<br>\n      Account Number: ***********<br>\n      Branch Code: 250655<br>\n      Swift Code: FIRNZAJJ\n    "}};function x(){var e;let s=(0,a.useRouter)(),{messages:r,showToast:x,removeToast:h}=(0,c.d)(),[u,p]=(0,t.useState)(null),[b,g]=(0,t.useState)(!0),[j,y]=(0,t.useState)(""),{mutate:N,loading:f,error:v}=(0,d.L2)(o.FH.invoices.create),I=e=>"".concat("R").concat(parseFloat(e.toString()).toLocaleString("en-ZA",{minimumFractionDigits:0,maximumFractionDigits:0})),T=()=>Math.floor(1e6+9e6*Math.random()).toString(),w=()=>{try{let e=l.IG.get("customerInfo"),r=l.IG.get("selectedProducts"),n=l.IG.get("paymentMethod"),t=l.IG.get("comments"),a=l.IG.get("salespersonName"),i=l.IG.get("selectedCompany"),c=l.IG.get("includeTax");if(!e||!r){x("No order data found. Please create an order first.","error"),s.push("/");return}let o=JSON.parse(e),d=JSON.parse(r),m=JSON.parse(i||"{}").key||"company1";p({customerInfo:o,selectedProducts:d,paymentMethod:n||"Cash",comments:t||"",salespersonName:a||"",companyKey:m,includeTax:"true"===c}),y(T())}catch(e){console.error("Error loading order data:",e),x("Error loading order data","error"),s.push("/")}finally{g(!1)}},_=()=>{if(!u)return{subtotal:0,tax:0,total:0};let e=0,s=0,r=0;return u.selectedProducts.forEach(n=>{let t=n.price*n.quantity;if(u.includeTax){let a=n.price/1.15,i=n.price-a;e+=a*n.quantity,s+=i*n.quantity,r+=t}else e+=t,r+=t}),{subtotal:Math.round(e),tax:Math.round(s),total:Math.round(r)}},S=async()=>{if(u)try{let e=_(),r=m[u.companyKey],n={reference_number:j,company:{name:r.name,bankingInformation:r.bankingInformation},billing:u.customerInfo.billing,shipping:u.customerInfo.shipping,items:u.selectedProducts.map(e=>({item_code:e.item_code,item_name:e.name,room_name:e.room_name,quantity:e.quantity,unit_price_including_tax:u.includeTax?e.price:1.15*e.price,unit_price_excluding_tax:u.includeTax?e.price/1.15:e.price,total_price:e.price*e.quantity,tax_per_product:u.includeTax?e.price-e.price/1.15:.15*e.price})),subtotal:e.subtotal,tax:e.tax,total:e.total,payment_method:u.paymentMethod,comments:u.comments,salesperson_name:u.salespersonName,include_tax:u.includeTax};await N(n)&&(x("Invoice created successfully!","success"),l.IG.remove("customerInfo"),l.IG.remove("selectedProducts"),l.IG.remove("paymentMethod"),l.IG.remove("comments"),l.IG.remove("salespersonName"),l.IG.remove("includeTax"),setTimeout(()=>{s.push("/")},2e3))}catch(e){console.error("Error creating invoice:",e),x("Error: ".concat(e instanceof Error?e.message:"Failed to create invoice"),"error")}};if((0,t.useEffect)(()=>{w()},[]),(0,t.useEffect)(()=>{v&&x("Error: ".concat(v),"error")},[v,x]),b)return(0,n.jsx)(i.A,{children:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading invoice..."})]})})});if(!u)return(0,n.jsx)(i.A,{children:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"No order data found"}),(0,n.jsx)("button",{onClick:()=>s.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Go to Home"})]})})});let k=_(),A=m[u.companyKey];return(0,n.jsx)(i.A,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-100 p-5",children:[(0,n.jsx)(c.A,{messages:r,onRemove:h}),(0,n.jsxs)("div",{className:"flex gap-5 mb-5",children:[(0,n.jsx)("button",{onClick:()=>s.push("/"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Home"}),(0,n.jsx)("button",{onClick:()=>{sessionStorage.setItem("editingOrder","true"),s.push("/")},className:"text-blue-600 hover:text-blue-800 transition-colors",children:"✏️ Edit Order"})]}),(0,n.jsxs)("div",{id:"pdf-1",className:"max-w-4xl mx-auto bg-white p-8 shadow-lg",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-red-600 mb-2",children:"INVOICE"}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Reference: ",j]}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Date: ",(e=new Date,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e))]}),(0,n.jsx)("p",{className:"text-red-600 font-semibold",children:"PAYMENT DUE"})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:A.name}),(0,n.jsx)("div",{className:"text-sm text-gray-600",dangerouslySetInnerHTML:{__html:A.bankingInformation}})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"BILL TO"}),(0,n.jsxs)("div",{className:"text-gray-600",children:[(0,n.jsx)("p",{children:u.customerInfo.billing.name}),(0,n.jsx)("p",{children:u.customerInfo.billing.email}),(0,n.jsx)("p",{children:u.customerInfo.billing.address}),(0,n.jsx)("p",{children:u.customerInfo.billing.phone})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"SHIP TO"}),(0,n.jsx)("div",{className:"text-gray-600",children:u.customerInfo.shipping?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:u.customerInfo.shipping.name}),(0,n.jsx)("p",{children:u.customerInfo.shipping.email}),(0,n.jsx)("p",{children:u.customerInfo.shipping.address}),(0,n.jsx)("p",{children:u.customerInfo.shipping.phone})]}):(0,n.jsx)("p",{children:"Same as billing address"})})]})]}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"border border-gray-300",children:[(0,n.jsxs)("div",{className:"bg-red-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300",children:[(0,n.jsx)("div",{className:"col-span-4",children:"DESCRIPTION"}),(0,n.jsx)("div",{className:"col-span-1 text-center",children:"QTY"}),(0,n.jsx)("div",{className:"col-span-2 text-right",children:"UNIT PRICE"}),u.includeTax&&(0,n.jsx)("div",{className:"col-span-2 text-right",children:"TAX"}),(0,n.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right"),children:"TOTAL"})]}),u.selectedProducts.map((e,s)=>{let r=e.price*e.quantity,t=u.includeTax?e.price/1.15:e.price,a=u.includeTax?e.price-t:0;return(0,n.jsxs)("div",{className:"grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0",children:[(0,n.jsxs)("div",{className:"col-span-4",children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:["Room: ",e.room_name]})]}),(0,n.jsx)("div",{className:"col-span-1 text-center",children:e.quantity}),(0,n.jsx)("div",{className:"col-span-2 text-right",children:I(u.includeTax?t:e.price)}),u.includeTax&&(0,n.jsx)("div",{className:"col-span-2 text-right",children:I(a)}),(0,n.jsx)("div",{className:"".concat(u.includeTax?"col-span-3":"col-span-5"," text-right font-medium"),children:I(r)})]},s)})]})}),(0,n.jsx)("div",{className:"flex justify-end mb-8",children:(0,n.jsxs)("div",{className:"w-64",children:[(0,n.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,n.jsx)("span",{children:"Subtotal:"}),(0,n.jsx)("span",{children:I(k.subtotal)})]}),u.includeTax&&(0,n.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,n.jsx)("span",{children:"Tax (15%):"}),(0,n.jsx)("span",{children:I(k.tax)})]}),(0,n.jsxs)("div",{className:"flex justify-between py-2 font-bold text-lg bg-red-100 px-2 rounded",children:[(0,n.jsx)("span",{children:"Amount Due:"}),(0,n.jsx)("span",{className:"text-red-600",children:I(k.total)})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Method"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.paymentMethod})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Salesperson"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.salespersonName||"N/A"})]})]}),u.comments&&(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Comments"}),(0,n.jsx)("p",{className:"text-gray-600",children:u.comments})]}),(0,n.jsxs)("div",{className:"mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded",children:[(0,n.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Terms"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Payment is due within 30 days of invoice date. Please include the reference number with your payment."})]}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,n.jsxs)("button",{onClick:S,disabled:f,className:"bg-red-600 text-white px-6 py-3 rounded text-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2",children:[f&&(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),f?"Creating...":"Confirm Invoice"]}),(0,n.jsx)("button",{onClick:()=>window.print(),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"\uD83D\uDCF8 Print/Save"})]})]})]})})}},7467:(e,s,r)=>{Promise.resolve().then(r.bind(r,7208))}},e=>{var s=s=>e(e.s=s);e.O(0,[535,441,684,358],()=>s(7467)),_N_E=e.O()}]);