{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport {\n  rawEntrypointsToEntrypoints,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: dir,\n      rootPath: config.turbopack?.root || config.outputFileTracingRoot || dir,\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental?.turbopackMemoryLimit,\n      dependencyTracking: persistentCaching,\n    }\n  )\n  try {\n    // Write an empty file in a known location to signal this was built with Turbopack\n    await fs.writeFile(path.join(distDir, 'turbopack'), '')\n\n    await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n    await fs.mkdir(path.join(distDir, 'static', buildId), {\n      recursive: true,\n    })\n    await fs.writeFile(\n      path.join(distDir, 'package.json'),\n      JSON.stringify(\n        {\n          type: 'commonjs',\n        },\n        null,\n        2\n      )\n    )\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const entrypoints = await project.writeAllEntrypointsToDisk(appDirOnly)\n\n    const manifestLoader = new TurbopackManifestLoader({\n      buildId,\n      distDir,\n      encryptionKey,\n    })\n\n    const topLevelErrors = []\n    const topLevelWarnings = []\n    for (const issue of entrypoints.issues) {\n      if (issue.severity === 'error' || issue.severity === 'fatal') {\n        topLevelErrors.push(formatIssue(issue))\n      } else if (isRelevantWarning(issue)) {\n        topLevelWarnings.push(formatIssue(issue))\n      }\n    }\n\n    if (topLevelWarnings.length > 0) {\n      console.warn(\n        `Turbopack build encountered ${\n          topLevelWarnings.length\n        } warnings:\\n${topLevelWarnings.join('\\n')}`\n      )\n    }\n\n    if (topLevelErrors.length > 0) {\n      throw new Error(\n        `Turbopack build failed with ${\n          topLevelErrors.length\n        } errors:\\n${topLevelErrors.join('\\n')}`\n      )\n    }\n\n    const currentEntrypoints = await rawEntrypointsToEntrypoints(entrypoints)\n\n    const promises: Promise<any>[] = []\n\n    if (!appDirOnly) {\n      for (const [page, route] of currentEntrypoints.page) {\n        promises.push(\n          handleRouteType({\n            page,\n            route,\n            manifestLoader,\n          })\n        )\n      }\n    }\n\n    for (const [page, route] of currentEntrypoints.app) {\n      promises.push(\n        handleRouteType({\n          page,\n          route,\n          manifestLoader,\n        })\n      )\n    }\n\n    await Promise.all(promises)\n\n    await Promise.all([\n      manifestLoader.loadBuildManifest('_app'),\n      manifestLoader.loadPagesManifest('_app'),\n      manifestLoader.loadFontManifest('_app'),\n      manifestLoader.loadPagesManifest('_document'),\n      manifestLoader.loadBuildManifest('_error'),\n      manifestLoader.loadPagesManifest('_error'),\n      manifestLoader.loadFontManifest('_error'),\n      entrypoints.instrumentation &&\n        manifestLoader.loadMiddlewareManifest(\n          'instrumentation',\n          'instrumentation'\n        ),\n      entrypoints.middleware &&\n        (await manifestLoader.loadMiddlewareManifest(\n          'middleware',\n          'middleware'\n        )),\n    ])\n\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites: rewrites,\n      entrypoints: currentEntrypoints,\n    })\n\n    const shutdownPromise = project.shutdown()\n\n    const time = process.hrtime(startTime)\n    return {\n      duration: time[0] + time[1] / 1e9,\n      buildTraceContext: undefined,\n      shutdownPromise,\n    }\n  } catch (err) {\n    await project.shutdown()\n    throw err\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["turbopackBuild", "waitForShutdown", "worker<PERSON>ain", "config", "validateTurboNextConfig", "dir", "NextBuildContext", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "startTime", "process", "hrtime", "bindings", "loadBindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "persistentCaching", "isPersistentCachingEnabled", "project", "turbo", "createProject", "projectPath", "rootPath", "turbopack", "root", "outputFileTracingRoot", "nextConfig", "jsConfig", "getTurbopackJsConfig", "watch", "enable", "env", "defineEnv", "createDefineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "turbopackMemoryLimit", "dependencyTracking", "fs", "writeFile", "path", "mkdir", "recursive", "JSON", "stringify", "type", "entrypoints", "writeAllEntrypointsToDisk", "manifest<PERSON><PERSON>der", "TurbopackManifestLoader", "topLevelErrors", "topLevelWarnings", "issue", "issues", "severity", "push", "formatIssue", "isRelevantWarning", "length", "console", "warn", "Error", "currentEntrypoints", "rawEntrypointsToEntrypoints", "promises", "page", "route", "handleRouteType", "app", "Promise", "all", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "instrumentation", "loadMiddlewareManifest", "middleware", "writeManifests", "devRewrites", "productionRewrites", "shutdownPromise", "shutdown", "time", "duration", "buildTraceContext", "err", "workerData", "Object", "assign", "buildContext", "loadConfig", "PHASE_PRODUCTION_BUILD", "hasCustomExportOutput", "telemetry", "Telemetry", "setGlobal", "result"], "mappings": ";;;;;;;;;;;;;;;;IAsBsBA,cAAc;eAAdA;;IA2NAC,eAAe;eAAfA;;IA9BAC,UAAU;eAAVA;;;6DAnNL;kCACuB;uBAMjC;8BAC0B;qBACa;mCAIvC;gCACiC;oBACT;2BACQ;+DAChB;wBACe;yBACZ;uBACA;;;;;;AAEnB,eAAeF;QAsBgBG,sBAYtBA,mBA4BGA;IAzDjB,MAAMC,IAAAA,yCAAuB,EAAC;QAC5BC,KAAKC,8BAAgB,CAACD,GAAG;QACzBE,OAAO;IACT;IAEA,MAAMJ,SAASG,8BAAgB,CAACH,MAAM;IACtC,MAAME,MAAMC,8BAAgB,CAACD,GAAG;IAChC,MAAMG,UAAUF,8BAAgB,CAACE,OAAO;IACxC,MAAMC,UAAUH,8BAAgB,CAACG,OAAO;IACxC,MAAMC,gBAAgBJ,8BAAgB,CAACI,aAAa;IACpD,MAAMC,eAAeL,8BAAgB,CAACK,YAAY;IAClD,MAAMC,cAAcN,8BAAgB,CAACM,WAAW;IAChD,MAAMC,WAAWP,8BAAgB,CAACO,QAAQ;IAC1C,MAAMC,aAAaR,8BAAgB,CAACQ,UAAU;IAC9C,MAAMC,aAAaT,8BAAgB,CAACS,UAAU;IAE9C,MAAMC,YAAYC,QAAQC,MAAM;IAChC,MAAMC,WAAW,MAAMC,IAAAA,iBAAY,EAACjB,2BAAAA,uBAAAA,OAAQkB,YAAY,qBAApBlB,qBAAsBmB,aAAa;IACvE,MAAMC,MAAM;IAEZ,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMC,oBAAoBC,IAAAA,iCAA0B,EAACvB;IACrD,MAAMwB,UAAU,MAAMR,SAASS,KAAK,CAACC,aAAa,CAChD;QACEC,aAAazB;QACb0B,UAAU5B,EAAAA,oBAAAA,OAAO6B,SAAS,qBAAhB7B,kBAAkB8B,IAAI,KAAI9B,OAAO+B,qBAAqB,IAAI7B;QACpEG;QACA2B,YAAYhC;QACZiC,UAAU,MAAMC,IAAAA,2BAAoB,EAAChC,KAAKF;QAC1CmC,OAAO;YACLC,QAAQ;QACV;QACAhB;QACAiB,KAAKvB,QAAQuB,GAAG;QAChBC,WAAWC,IAAAA,oBAAe,EAAC;YACzBC,aAAa;YACbC,qBAAqBtC,8BAAgB,CAACsC,mBAAmB;YACzDzC;YACAoB;YACAf;YACAqC,qBAAqB1C,OAAOkB,YAAY,CAACwB,mBAAmB;YAC5DjC;YACA,uEAAuE;YACvEkC,oBAAoBC;QACtB;QACAtC;QACAC;QACAC;QACAqC,mBAAmBxB,kBAAkByB,IAAI,CAAC;QAC1ClC;IACF,GACA;QACEU;QACAyB,WAAW,GAAE/C,wBAAAA,OAAOkB,YAAY,qBAAnBlB,sBAAqBgD,oBAAoB;QACtDC,oBAAoB3B;IACtB;IAEF,IAAI;QACF,kFAAkF;QAClF,MAAM4B,YAAE,CAACC,SAAS,CAACC,aAAI,CAACN,IAAI,CAACzC,SAAS,cAAc;QAEpD,MAAM6C,YAAE,CAACG,KAAK,CAACD,aAAI,CAACN,IAAI,CAACzC,SAAS,WAAW;YAAEiD,WAAW;QAAK;QAC/D,MAAMJ,YAAE,CAACG,KAAK,CAACD,aAAI,CAACN,IAAI,CAACzC,SAAS,UAAUC,UAAU;YACpDgD,WAAW;QACb;QACA,MAAMJ,YAAE,CAACC,SAAS,CAChBC,aAAI,CAACN,IAAI,CAACzC,SAAS,iBACnBkD,KAAKC,SAAS,CACZ;YACEC,MAAM;QACR,GACA,MACA;QAIJ,6DAA6D;QAC7D,MAAMC,cAAc,MAAMlC,QAAQmC,yBAAyB,CAAChD;QAE5D,MAAMiD,iBAAiB,IAAIC,uCAAuB,CAAC;YACjDvD;YACAD;YACAE;QACF;QAEA,MAAMuD,iBAAiB,EAAE;QACzB,MAAMC,mBAAmB,EAAE;QAC3B,KAAK,MAAMC,SAASN,YAAYO,MAAM,CAAE;YACtC,IAAID,MAAME,QAAQ,KAAK,WAAWF,MAAME,QAAQ,KAAK,SAAS;gBAC5DJ,eAAeK,IAAI,CAACC,IAAAA,kBAAW,EAACJ;YAClC,OAAO,IAAIK,IAAAA,wBAAiB,EAACL,QAAQ;gBACnCD,iBAAiBI,IAAI,CAACC,IAAAA,kBAAW,EAACJ;YACpC;QACF;QAEA,IAAID,iBAAiBO,MAAM,GAAG,GAAG;YAC/BC,QAAQC,IAAI,CACV,CAAC,4BAA4B,EAC3BT,iBAAiBO,MAAM,CACxB,YAAY,EAAEP,iBAAiBjB,IAAI,CAAC,OAAO;QAEhD;QAEA,IAAIgB,eAAeQ,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAIL,CAJK,IAAIG,MACR,CAAC,4BAA4B,EAC3BX,eAAeQ,MAAM,CACtB,UAAU,EAAER,eAAehB,IAAI,CAAC,OAAO,GAHpC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QAEA,MAAM4B,qBAAqB,MAAMC,IAAAA,8CAA2B,EAACjB;QAE7D,MAAMkB,WAA2B,EAAE;QAEnC,IAAI,CAACjE,YAAY;YACf,KAAK,MAAM,CAACkE,MAAMC,MAAM,IAAIJ,mBAAmBG,IAAI,CAAE;gBACnDD,SAAST,IAAI,CACXY,IAAAA,kCAAe,EAAC;oBACdF;oBACAC;oBACAlB;gBACF;YAEJ;QACF;QAEA,KAAK,MAAM,CAACiB,MAAMC,MAAM,IAAIJ,mBAAmBM,GAAG,CAAE;YAClDJ,SAAST,IAAI,CACXY,IAAAA,kCAAe,EAAC;gBACdF;gBACAC;gBACAlB;YACF;QAEJ;QAEA,MAAMqB,QAAQC,GAAG,CAACN;QAElB,MAAMK,QAAQC,GAAG,CAAC;YAChBtB,eAAeuB,iBAAiB,CAAC;YACjCvB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeyB,gBAAgB,CAAC;YAChCzB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeuB,iBAAiB,CAAC;YACjCvB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeyB,gBAAgB,CAAC;YAChC3B,YAAY4B,eAAe,IACzB1B,eAAe2B,sBAAsB,CACnC,mBACA;YAEJ7B,YAAY8B,UAAU,IACnB,MAAM5B,eAAe2B,sBAAsB,CAC1C,cACA;SAEL;QAED,MAAM3B,eAAe6B,cAAc,CAAC;YAClCC,aAAa9C;YACb+C,oBAAoBjF;YACpBgD,aAAagB;QACf;QAEA,MAAMkB,kBAAkBpE,QAAQqE,QAAQ;QAExC,MAAMC,OAAOhF,QAAQC,MAAM,CAACF;QAC5B,OAAO;YACLkF,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;YAC9BE,mBAAmBpD;YACnBgD;QACF;IACF,EAAE,OAAOK,KAAK;QACZ,MAAMzE,QAAQqE,QAAQ;QACtB,MAAMI;IACR;AACF;AAEA,IAAIL;AACG,eAAe7F,WAAWmG,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAACjG,8BAAgB,EAAE+F,WAAWG,YAAY;IAEvD,iDAAiD;IACjDlG,8BAAgB,CAACH,MAAM,GAAG,MAAMsG,IAAAA,eAAU,EACxCC,iCAAsB,EACtBpG,8BAAgB,CAACD,GAAG;IAGtB,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAIsG,IAAAA,6BAAqB,EAACrG,8BAAgB,CAACH,MAAM,GAAG;QAClDG,8BAAgB,CAACH,MAAM,CAACK,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAMoG,YAAY,IAAIC,kBAAS,CAAC;QAC9BrG,SAASF,8BAAgB,CAACH,MAAM,CAACK,OAAO;IAC1C;IACAsG,IAAAA,gBAAS,EAAC,aAAaF;IAEvB,MAAMG,SAAS,MAAM/G;IACrB+F,kBAAkBgB,OAAOhB,eAAe;IACxC,OAAOgB;AACT;AAEO,eAAe9G;IACpB,IAAI8F,iBAAiB;QACnB,MAAMA;IACR;AACF"}