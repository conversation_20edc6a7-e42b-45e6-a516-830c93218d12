'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';
import ToastContainer, { useToast } from '@/components/Toast';
import { storage } from '@/lib/auth-utils';
import { OrderData, Company } from '@/types';
import { api } from '@/lib/api';
import { useApiMutation } from '@/hooks/useApi';

const CURRENCY_SYMBOL = 'R';

const companies: Record<string, Company> = {
  company1: {
    key: 'company1',
    name: "Shans Accessories PTY LTD",
    bankingInformation: `
      First National Bank<br>
      Account :  ***********<br>
      Branch code 257705<br>
      Swift code FIRNZAJJ
    `
  },
  company2: {
    key: 'company2',
    name: "Shans Autosport PTY LTD",
    bankingInformation: `
      Business Account<br>
      Capitec Current Account<br>
      Account: **********
    `
  },
  company3: {
    key: 'company3',
    name: "Shans Motorstyle PTY LTD",
    bankingInformation: `
      SHANS MOTORSTYLE (PTY) LTD<br>
      Gold Business Account<br>
      Account Number: ***********<br>
      Branch Code: 250655<br>
      Swift Code: FIRNZAJJ
    `
  }
};

export default function ReceiptPage() {
  const router = useRouter();
  const { messages, showToast, removeToast } = useToast();
  
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [referenceNumber, setReferenceNumber] = useState('');

  // Use API mutation hook for creating receipt
  const {
    mutate: createReceipt,
    loading: isConfirming,
    error: receiptError
  } = useApiMutation(api.receipts.create);

  const formatCurrency = (amount: number) => {
    return `${CURRENCY_SYMBOL}${parseFloat(amount.toString()).toLocaleString('en-ZA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })}`;
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Intl.DateTimeFormat('en-US', options).format(date);
  };

  const generateReferenceNumber = () => {
    return Math.floor(1000000 + Math.random() * 9000000).toString();
  };

  const loadOrderData = () => {
    try {
      const customerInfoCookie = storage.get('customerInfo');
      const selectedProductsCookie = storage.get('selectedProducts');
      const paymentMethodCookie = storage.get('paymentMethod');
      const commentsCookie = storage.get('comments');
      const salespersonNameCookie = storage.get('salespersonName');
      const companyKeyCookie = storage.get('selectedCompany');
      const includeTaxCookie = storage.get('includeTax');

      if (!customerInfoCookie || !selectedProductsCookie) {
        showToast('No order data found. Please create an order first.', 'error');
        router.push('/');
        return;
      }

      const customerInfo = JSON.parse(customerInfoCookie);
      const selectedProducts = JSON.parse(selectedProductsCookie);
      const paymentMethod = paymentMethodCookie || 'Cash';
      const comments = commentsCookie || '';
      const salespersonName = salespersonNameCookie || '';
      const companyKey = JSON.parse(companyKeyCookie || '{}').key || 'company1';
      const includeTax = includeTaxCookie === 'true';

      const data: OrderData = {
        customerInfo,
        selectedProducts,
        paymentMethod,
        comments,
        salespersonName,
        companyKey,
        includeTax
      };

      setOrderData(data);
      setReferenceNumber(generateReferenceNumber());
    } catch (error) {
      console.error('Error loading order data:', error);
      showToast('Error loading order data', 'error');
      router.push('/');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotals = () => {
    if (!orderData) return { subtotal: 0, tax: 0, total: 0 };

    let subtotal = 0;
    let tax = 0;
    let total = 0;

    orderData.selectedProducts.forEach(product => {
      const lineTotal = product.price * product.quantity;
      
      if (orderData.includeTax) {
        const netPrice = product.price / 1.15;
        const taxPerUnit = product.price - netPrice;
        subtotal += netPrice * product.quantity;
        tax += taxPerUnit * product.quantity;
        total += lineTotal;
      } else {
        subtotal += lineTotal;
        total += lineTotal;
      }
    });

    return {
      subtotal: Math.round(subtotal),
      tax: Math.round(tax),
      total: Math.round(total)
    };
  };

  const confirmReceipt = async () => {
    if (!orderData) return;

    try {
      const totals = calculateTotals();
      const selectedCompany = companies[orderData.companyKey];

      const receiptData = {
        reference_number: referenceNumber,
        company: {
          name: selectedCompany.name,
          bankingInformation: selectedCompany.bankingInformation
        },
        billing: orderData.customerInfo.billing,
        shipping: orderData.customerInfo.shipping,
        items: orderData.selectedProducts.map(product => ({
          item_code: product.item_code,
          item_name: product.name,
          room_name: product.room_name,
          quantity: product.quantity,
          unit_price_including_tax: orderData.includeTax ? product.price : product.price * 1.15,
          unit_price_excluding_tax: orderData.includeTax ? product.price / 1.15 : product.price,
          total_price: product.price * product.quantity,
          tax_per_product: orderData.includeTax ? product.price - (product.price / 1.15) : product.price * 0.15
        })),
        subtotal: totals.subtotal,
        tax: totals.tax,
        total: totals.total,
        payment_method: orderData.paymentMethod,
        comments: orderData.comments,
        salesperson_name: orderData.salespersonName,
        include_tax: orderData.includeTax
      };

      const result = await createReceipt(receiptData);

      if (result) {
        showToast('Receipt created successfully!', 'success');
        // Clear the order data
        storage.remove('customerInfo');
        storage.remove('selectedProducts');
        storage.remove('paymentMethod');
        storage.remove('comments');
        storage.remove('salespersonName');
        storage.remove('includeTax');

        // Redirect to home after a delay
        setTimeout(() => {
          router.push('/');
        }, 2000);
      }
    } catch (error) {
      console.error('Error creating receipt:', error);
      showToast(`Error: ${error instanceof Error ? error.message : 'Failed to create receipt'}`, 'error');
    }
  };

  const handleEditOrder = () => {
    sessionStorage.setItem('editingOrder', 'true');
    router.push('/');
  };

  useEffect(() => {
    loadOrderData();
  }, []);

  // Handle API errors
  useEffect(() => {
    if (receiptError) {
      showToast(`Error: ${receiptError}`, 'error');
    }
  }, [receiptError, showToast]);

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">Loading receipt...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!orderData) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <p className="text-gray-600 mb-4">No order data found</p>
            <button
              onClick={() => router.push('/')}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Go to Home
            </button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const totals = calculateTotals();
  const selectedCompany = companies[orderData.companyKey];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100 p-5">
        <ToastContainer messages={messages} onRemove={removeToast} />
        
        <div className="flex gap-5 mb-5">
          <button
            onClick={() => router.push('/')}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={handleEditOrder}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            ✏️ Edit Order
          </button>
        </div>

        <div id="pdf-1" className="max-w-4xl mx-auto bg-white p-8 shadow-lg">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-3xl font-bold text-green-600 mb-2">RECEIPT</h1>
              <p className="text-gray-600">Reference: {referenceNumber}</p>
              <p className="text-gray-600">Date: {formatDate(new Date())}</p>
              <p className="text-green-600 font-semibold">PAYMENT RECEIVED</p>
            </div>
            <div className="text-right">
              <h2 className="text-xl font-bold text-gray-800 mb-2">{selectedCompany.name}</h2>
              <div 
                className="text-sm text-gray-600"
                dangerouslySetInnerHTML={{ __html: selectedCompany.bankingInformation }}
              />
            </div>
          </div>

          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-bold text-gray-800 mb-2">BILL TO</h3>
              <div className="text-gray-600">
                <p>{orderData.customerInfo.billing.name}</p>
                <p>{orderData.customerInfo.billing.email}</p>
                <p>{orderData.customerInfo.billing.address}</p>
                <p>{orderData.customerInfo.billing.phone}</p>
              </div>
            </div>
            <div>
              <h3 className="font-bold text-gray-800 mb-2">SHIP TO</h3>
              <div className="text-gray-600">
                {orderData.customerInfo.shipping ? (
                  <>
                    <p>{orderData.customerInfo.shipping.name}</p>
                    <p>{orderData.customerInfo.shipping.email}</p>
                    <p>{orderData.customerInfo.shipping.address}</p>
                    <p>{orderData.customerInfo.shipping.phone}</p>
                  </>
                ) : (
                  <p>Same as billing address</p>
                )}
              </div>
            </div>
          </div>

          {/* Products Table */}
          <div className="mb-8">
            <div className="border border-gray-300">
              {/* Table Header */}
              <div className="bg-green-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300">
                <div className="col-span-4">DESCRIPTION</div>
                <div className="col-span-1 text-center">QTY</div>
                <div className="col-span-2 text-right">UNIT PRICE</div>
                {orderData.includeTax && <div className="col-span-2 text-right">TAX</div>}
                <div className={`${orderData.includeTax ? 'col-span-3' : 'col-span-5'} text-right`}>TOTAL</div>
              </div>
              
              {/* Table Body */}
              {orderData.selectedProducts.map((product, index) => {
                const lineTotal = product.price * product.quantity;
                const netPrice = orderData.includeTax ? product.price / 1.15 : product.price;
                const taxPerUnit = orderData.includeTax ? product.price - netPrice : 0;
                
                return (
                  <div key={index} className="grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0">
                    <div className="col-span-4">
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-gray-600">Room: {product.room_name}</div>
                    </div>
                    <div className="col-span-1 text-center">{product.quantity}</div>
                    <div className="col-span-2 text-right">
                      {formatCurrency(orderData.includeTax ? netPrice : product.price)}
                    </div>
                    {orderData.includeTax && (
                      <div className="col-span-2 text-right">{formatCurrency(taxPerUnit)}</div>
                    )}
                    <div className={`${orderData.includeTax ? 'col-span-3' : 'col-span-5'} text-right font-medium`}>
                      {formatCurrency(lineTotal)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-64">
              <div className="flex justify-between py-2 border-b border-gray-300">
                <span>Subtotal:</span>
                <span>{formatCurrency(totals.subtotal)}</span>
              </div>
              {orderData.includeTax && (
                <div className="flex justify-between py-2 border-b border-gray-300">
                  <span>Tax (15%):</span>
                  <span>{formatCurrency(totals.tax)}</span>
                </div>
              )}
              <div className="flex justify-between py-2 font-bold text-lg bg-green-100 px-2 rounded">
                <span>Total Paid:</span>
                <span className="text-green-600">{formatCurrency(totals.total)}</span>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-bold text-gray-800 mb-2">Payment Method</h3>
              <p className="text-gray-600">{orderData.paymentMethod}</p>
            </div>
            <div>
              <h3 className="font-bold text-gray-800 mb-2">Salesperson</h3>
              <p className="text-gray-600">{orderData.salespersonName || 'N/A'}</p>
            </div>
          </div>

          {orderData.comments && (
            <div className="mb-8">
              <h3 className="font-bold text-gray-800 mb-2">Comments</h3>
              <p className="text-gray-600">{orderData.comments}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <button
              onClick={confirmReceipt}
              disabled={isConfirming}
              className="bg-green-600 text-white px-6 py-3 rounded text-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isConfirming && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
              {isConfirming ? 'Creating...' : 'Confirm Receipt'}
            </button>
            <button
              onClick={() => window.print()}
              className="bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors"
            >
              📸 Print/Save
            </button>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
