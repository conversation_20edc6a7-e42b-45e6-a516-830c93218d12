(()=>{var e={};e.id=931,e.ids=[931],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},854:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(687);function s({isVisible:e,message:t="Loading products, please wait...",error:r,onRetry:s}){return e?(0,a.jsxs)("div",{className:"fixed top-0 left-0 w-full h-full bg-white bg-opacity-98 z-50 flex flex-col justify-start items-center pt-20 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"w-20 h-20 border-10 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-6 shadow-lg"}),(0,a.jsx)("p",{className:"text-gray-800 text-lg font-semibold text-center text-shadow-sm",children:r?"Connection Error":t}),r&&(0,a.jsxs)(a.<PERSON>agment,{children:[(0,a.jsx)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-3 rounded mt-4 max-w-xs text-center text-sm font-medium",children:r}),s&&(0,a.jsx)("button",{onClick:s,className:"mt-5 px-6 py-3 bg-blue-600 text-white border-none rounded cursor-pointer text-base font-semibold shadow-md transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-lg",children:"Retry"})]})]}):null}},3021:(e,t,r)=>{Promise.resolve().then(r.bind(r,7551))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5405:(e,t,r)=>{Promise.resolve().then(r.bind(r,9305))},7551:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(687),s=r(3210),l=r(6189),d=r(769),o=r(854),n=r(9265);function i(){let e=(0,l.useRouter)(),[t,r]=(0,s.useState)(""),[i,m]=(0,s.useState)(!1),[x,p]=(0,s.useState)(!1),[u,h]=(0,s.useState)(null),[g,b]=(0,s.useState)({carBrand:"",productCategory:"",carModel:"",room:"",colourTape:""}),{data:y=[],loading:j,error:v,refetch:f}=(0,n.U9)("/products"),{data:_=[],loading:w}=(0,n.U9)("/rooms"),{data:N=[],loading:C}=(0,n.U9)("/categories"),{data:k=[],loading:A}=(0,n.U9)("/car-brands"),{data:P=[],loading:S}=(0,n.U9)("/car-models"),M=(0,n.L2)("/products","POST"),U=(0,n.L2)("/products","PUT"),E=(0,n.L2)("/products","DELETE"),R=y.filter(e=>{let r=e.item_name.toLowerCase().includes(t.toLowerCase())||e.item_code.toLowerCase().includes(t.toLowerCase()),a=!g.carBrand||e.car_brand_id?.toString()===g.carBrand,s=!g.productCategory||e.product_category===g.productCategory,l=!g.carModel||e.car_model_id?.toString()===g.carModel,d=!g.room||e.room_id?.toString()===g.room,o=!g.colourTape||e.colour_tape===g.colourTape;return r&&a&&s&&l&&d&&o}),q=async e=>{try{await M.mutate(e),m(!1),f()}catch(e){console.error("Error adding product:",e)}},B=async e=>{if(u)try{await U.mutate(e,{endpoint:`/products/${u.id}`}),p(!1),h(null),f()}catch(e){console.error("Error updating product:",e)}},L=async e=>{if(confirm("Are you sure you want to delete this product?"))try{await E.mutate(void 0,{endpoint:`/products/${e}`}),f()}catch(e){console.error("Error deleting product:",e)}},D=e=>{h(e),p(!0)};return j||w||C||A||S?(0,a.jsx)(d.A,{requireAdmin:!0,children:(0,a.jsx)(o.A,{isVisible:!0,message:"Loading products..."})}):(0,a.jsx)(d.A,{requireAdmin:!0,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Manage Products"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,a.jsxs)("button",{onClick:()=>m(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"+"})," Add Product"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"⚙️"})," Manage Categories"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDE97"})," Manage Car Brands"]}),(0,a.jsxs)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDE98"})," Manage Car Models"]})]}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Brand"}),(0,a.jsxs)("select",{value:g.carBrand,onChange:e=>b(t=>({...t,carBrand:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Brands"}),k.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),(0,a.jsxs)("select",{value:g.productCategory,onChange:e=>b(t=>({...t,productCategory:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Categories"}),N.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Model"}),(0,a.jsxs)("select",{value:g.carModel,onChange:e=>b(t=>({...t,carModel:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Models"}),P.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room"}),(0,a.jsxs)("select",{value:g.room,onChange:e=>b(t=>({...t,room:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Rooms"}),_.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),placeholder:"Search products...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Code"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Room"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Item Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Car Model"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Retail Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:R.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.item_code}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:_.find(t=>t.id===e.room_id)?.name||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.item_name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:k.find(t=>t.id===e.car_brand_id)?.name||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:P.find(t=>t.id===e.car_model_id)?.name||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.product_category?.replace(/_/g," ")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.available_stock}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["R ",e.unit_retail_price?.toLocaleString()]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>D(e),className:"text-blue-600 hover:text-blue-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>L(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e.id))})]})})}),i&&(0,a.jsx)(c,{isOpen:i,onClose:()=>m(!1),onSubmit:q,rooms:_,categories:N,carBrands:k,carModels:P,title:"Add Product"}),x&&u&&(0,a.jsx)(c,{isOpen:x,onClose:()=>{p(!1),h(null)},onSubmit:B,rooms:_,categories:N,carBrands:k,carModels:P,title:"Edit Product",initialData:u})]})})})}function c({isOpen:e,onClose:t,onSubmit:r,rooms:l,categories:d,carBrands:o,carModels:n,title:i,initialData:c}){let[m,x]=(0,s.useState)({item_code:c?.item_code||"",room_id:c?.room_id||0,item_name:c?.item_name||"",car_brand_id:c?.car_brand_id||0,car_model_id:c?.car_model_id||0,product_category:c?.product_category||"",available_stock:c?.available_stock||0,low_stock_threshold:c?.low_stock_threshold||5,colour_tape:c?.colour_tape||"#000000",location:c?.location||"",unit_retail_price:c?.unit_retail_price||0,wholesale_price:c?.wholesale_price||0,unit_cost:c?.unit_cost||0,supplier_code:c?.supplier_code||"",additional_comments:c?.additional_comments||""}),p=(e,t)=>{x(r=>({...r,[e]:t}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:i}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 text-2xl",children:"\xd7"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(m)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Item Code *"}),(0,a.jsx)("input",{type:"text",value:m.item_code,onChange:e=>p("item_code",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Room *"}),(0,a.jsxs)("select",{value:m.room_id,onChange:e=>p("room_id",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a room"}),l.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Item Name *"}),(0,a.jsx)("input",{type:"text",value:m.item_name,onChange:e=>p("item_name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Brand"}),(0,a.jsxs)("select",{value:m.car_brand_id,onChange:e=>p("car_brand_id",parseInt(e.target.value)||void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Select a brand"}),o.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Car Model"}),(0,a.jsxs)("select",{value:m.car_model_id,onChange:e=>p("car_model_id",parseInt(e.target.value)||void 0),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Select a model"}),n.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Category *"}),(0,a.jsxs)("select",{value:m.product_category,onChange:e=>p("product_category",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),d.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Available Stock"}),(0,a.jsx)("input",{type:"number",value:m.available_stock,onChange:e=>p("available_stock",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Low Stock Threshold"}),(0,a.jsx)("input",{type:"number",value:m.low_stock_threshold,onChange:e=>p("low_stock_threshold",parseInt(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Colour Tape"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"color",value:m.colour_tape,onChange:e=>p("colour_tape",e.target.value),className:"w-16 h-10 border border-gray-300 rounded-md"}),(0,a.jsx)("input",{type:"text",value:m.colour_tape,onChange:e=>p("colour_tape",e.target.value),className:"flex-1 border border-gray-300 rounded-md px-3 py-2",placeholder:"#000000"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Location"}),(0,a.jsx)("input",{type:"text",value:m.location,onChange:e=>p("location",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Unit Retail Price (R)"}),(0,a.jsx)("input",{type:"number",value:m.unit_retail_price,onChange:e=>p("unit_retail_price",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wholesale Price (R)"}),(0,a.jsx)("input",{type:"number",value:m.wholesale_price,onChange:e=>p("wholesale_price",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Unit Cost (R)"}),(0,a.jsx)("input",{type:"number",value:m.unit_cost,onChange:e=>p("unit_cost",parseFloat(e.target.value)),className:"w-full border border-gray-300 rounded-md px-3 py-2",min:"0",step:"0.01"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Supplier Code"}),(0,a.jsx)("input",{type:"text",value:m.supplier_code,onChange:e=>p("supplier_code",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Comments"}),(0,a.jsx)("textarea",{value:m.additional_comments,onChange:e=>p("additional_comments",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2",rows:3})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[c?"Update":"Add"," Product"]})]})]})]})})}):null}},8102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>i});var a=r(5239),s=r(8088),l=r(8170),d=r.n(l),o=r(893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let i={children:["",{children:["admin",{children:["manage-products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9305)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-products\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-products\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/manage-products/page",pathname:"/admin/manage-products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9305:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\admin\\\\manage-products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\admin\\manage-products\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[825,227],()=>r(8102));module.exports=a})();