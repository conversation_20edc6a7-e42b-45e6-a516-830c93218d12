(()=>{var e={};e.id=598,e.ids=[598],e.modules={27:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,d:()=>o});var r=t(687),n=t(3210);function a({message:e,onRemove:s}){let t={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[e.type];return(0,r.jsx)("div",{className:`${t} text-white px-4 py-2 rounded opacity-95 text-sm toast`,children:e.message})}function i({messages:e,onRemove:s}){return(0,r.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:e.map(e=>(0,r.jsx)(a,{message:e,onRemove:s},e.id))})}function o(){let[e,s]=(0,n.useState)([]);return{messages:e,showToast:(e,t="info")=>{let r=Math.random().toString(36).substr(2,9);s(s=>[...s,{id:r,message:e,type:t}])},removeToast:e=>{s(s=>s.filter(s=>s.id!==e))}}}},234:(e,s,t)=>{Promise.resolve().then(t.bind(t,3482))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2185:(e,s,t)=>{"use strict";t.d(s,{FH:()=>a});var r=t(1439);async function n(e,s={}){try{let t=`${r.JR}${e}`,n=await fetch(t,{headers:{"Content-Type":"application/json",...s.headers},...s}),a=await n.json();if(!n.ok)return{success:!1,error:a.message||`HTTP error! status: ${n.status}`};return{success:!0,data:a}}catch(e){return{success:!1,error:e instanceof Error?e.message:"An unknown error occurred"}}}let a={products:{search:async(e,s=1,t=20)=>n(`/products/search?q=${encodeURIComponent(e)}&page=${s}&limit=${t}`),getAll:async(e=1,s=50)=>n(`/products?page=${e}&limit=${s}`),getByCode:async e=>n(`/products/${encodeURIComponent(e)}`)},quotations:{create:async e=>n("/quotations",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/quotations"),getById:async e=>n(`/quotations/${e}`),delete:async e=>n(`/quotations/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-quotation-to-receipt/${e}`,{method:"POST"})},invoices:{create:async e=>n("/invoices",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/invoices"),getById:async e=>n(`/invoices/${e}`),delete:async e=>n(`/invoices/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-invoice-to-receipt/${e}`,{method:"POST"})},receipts:{create:async e=>n("/receipts",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/receipts"),getById:async e=>n(`/receipts/${e}`),delete:async e=>n(`/receipts/${e}`,{method:"DELETE"})},auth:{login:async(e,s)=>n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:s})}),checkStatus:async()=>n("/auth/status"),verifyToken:async e=>n("/auth/verify",{headers:{Authorization:`Bearer ${e}`}})},server:{checkStatus:async()=>n("/status")}}},2276:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\invoice\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\invoice\\page.tsx","default")},2862:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=t(5239),n=t(8088),a=t(8170),i=t.n(a),o=t(893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(s,c);let l={children:["",{children:["invoice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2276)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\invoice\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\invoice\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/invoice/page",pathname:"/invoice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(687),n=t(3210),a=t(6189),i=t(769),o=t(27),c=t(1439),l=t(2185),d=t(9265);let m={company1:{key:"company1",name:"Shans Accessories PTY LTD",bankingInformation:`
      First National Bank<br>
      Account :  ***********<br>
      Branch code 257705<br>
      Swift code FIRNZAJJ
    `},company2:{key:"company2",name:"Shans Autosport PTY LTD",bankingInformation:`
      Business Account<br>
      Capitec Current Account<br>
      Account: **********
    `},company3:{key:"company3",name:"Shans Motorstyle PTY LTD",bankingInformation:`
      SHANS MOTORSTYLE (PTY) LTD<br>
      Gold Business Account<br>
      Account Number: ***********<br>
      Branch Code: 250655<br>
      Swift Code: FIRNZAJJ
    `}};function p(){var e;let s=(0,a.useRouter)(),{messages:t,showToast:p,removeToast:u}=(0,o.d)(),[x,h]=(0,n.useState)(null),[g,b]=(0,n.useState)(!0),[y,f]=(0,n.useState)(""),{mutate:v,loading:j,error:N}=(0,d.L2)(l.FH.invoices.create),T=e=>`R${parseFloat(e.toString()).toLocaleString("en-ZA",{minimumFractionDigits:0,maximumFractionDigits:0})}`,I=()=>{if(!x)return{subtotal:0,tax:0,total:0};let e=0,s=0,t=0;return x.selectedProducts.forEach(r=>{let n=r.price*r.quantity;if(x.includeTax){let a=r.price/1.15,i=r.price-a;e+=a*r.quantity,s+=i*r.quantity,t+=n}else e+=n,t+=n}),{subtotal:Math.round(e),tax:Math.round(s),total:Math.round(t)}},w=async()=>{if(x)try{let e=I(),t=m[x.companyKey],r={reference_number:y,company:{name:t.name,bankingInformation:t.bankingInformation},billing:x.customerInfo.billing,shipping:x.customerInfo.shipping,items:x.selectedProducts.map(e=>({item_code:e.item_code,item_name:e.name,room_name:e.room_name,quantity:e.quantity,unit_price_including_tax:x.includeTax?e.price:1.15*e.price,unit_price_excluding_tax:x.includeTax?e.price/1.15:e.price,total_price:e.price*e.quantity,tax_per_product:x.includeTax?e.price-e.price/1.15:.15*e.price})),subtotal:e.subtotal,tax:e.tax,total:e.total,payment_method:x.paymentMethod,comments:x.comments,salesperson_name:x.salespersonName,include_tax:x.includeTax};await v(r)&&(p("Invoice created successfully!","success"),c.IG.remove("customerInfo"),c.IG.remove("selectedProducts"),c.IG.remove("paymentMethod"),c.IG.remove("comments"),c.IG.remove("salespersonName"),c.IG.remove("includeTax"),setTimeout(()=>{s.push("/")},2e3))}catch(e){console.error("Error creating invoice:",e),p(`Error: ${e instanceof Error?e.message:"Failed to create invoice"}`,"error")}};if(g)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading invoice..."})]})})});if(!x)return(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"No order data found"}),(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Go to Home"})]})})});let P=I(),S=m[x.companyKey];return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100 p-5",children:[(0,r.jsx)(o.A,{messages:t,onRemove:u}),(0,r.jsxs)("div",{className:"flex gap-5 mb-5",children:[(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Home"}),(0,r.jsx)("button",{onClick:()=>{sessionStorage.setItem("editingOrder","true"),s.push("/")},className:"text-blue-600 hover:text-blue-800 transition-colors",children:"✏️ Edit Order"})]}),(0,r.jsxs)("div",{id:"pdf-1",className:"max-w-4xl mx-auto bg-white p-8 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-red-600 mb-2",children:"INVOICE"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Reference: ",y]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Date: ",(e=new Date,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e))]}),(0,r.jsx)("p",{className:"text-red-600 font-semibold",children:"PAYMENT DUE"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:S.name}),(0,r.jsx)("div",{className:"text-sm text-gray-600",dangerouslySetInnerHTML:{__html:S.bankingInformation}})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"BILL TO"}),(0,r.jsxs)("div",{className:"text-gray-600",children:[(0,r.jsx)("p",{children:x.customerInfo.billing.name}),(0,r.jsx)("p",{children:x.customerInfo.billing.email}),(0,r.jsx)("p",{children:x.customerInfo.billing.address}),(0,r.jsx)("p",{children:x.customerInfo.billing.phone})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"SHIP TO"}),(0,r.jsx)("div",{className:"text-gray-600",children:x.customerInfo.shipping?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:x.customerInfo.shipping.name}),(0,r.jsx)("p",{children:x.customerInfo.shipping.email}),(0,r.jsx)("p",{children:x.customerInfo.shipping.address}),(0,r.jsx)("p",{children:x.customerInfo.shipping.phone})]}):(0,r.jsx)("p",{children:"Same as billing address"})})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"border border-gray-300",children:[(0,r.jsxs)("div",{className:"bg-red-100 grid grid-cols-12 gap-2 p-3 font-bold text-gray-800 border-b border-gray-300",children:[(0,r.jsx)("div",{className:"col-span-4",children:"DESCRIPTION"}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:"QTY"}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:"UNIT PRICE"}),x.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:"TAX"}),(0,r.jsx)("div",{className:`${x.includeTax?"col-span-3":"col-span-5"} text-right`,children:"TOTAL"})]}),x.selectedProducts.map((e,s)=>{let t=e.price*e.quantity,n=x.includeTax?e.price/1.15:e.price,a=x.includeTax?e.price-n:0;return(0,r.jsxs)("div",{className:"grid grid-cols-12 gap-2 p-3 border-b border-gray-300 last:border-b-0",children:[(0,r.jsxs)("div",{className:"col-span-4",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Room: ",e.room_name]})]}),(0,r.jsx)("div",{className:"col-span-1 text-center",children:e.quantity}),(0,r.jsx)("div",{className:"col-span-2 text-right",children:T(x.includeTax?n:e.price)}),x.includeTax&&(0,r.jsx)("div",{className:"col-span-2 text-right",children:T(a)}),(0,r.jsx)("div",{className:`${x.includeTax?"col-span-3":"col-span-5"} text-right font-medium`,children:T(t)})]},s)})]})}),(0,r.jsx)("div",{className:"flex justify-end mb-8",children:(0,r.jsxs)("div",{className:"w-64",children:[(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Subtotal:"}),(0,r.jsx)("span",{children:T(P.subtotal)})]}),x.includeTax&&(0,r.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-300",children:[(0,r.jsx)("span",{children:"Tax (15%):"}),(0,r.jsx)("span",{children:T(P.tax)})]}),(0,r.jsxs)("div",{className:"flex justify-between py-2 font-bold text-lg bg-red-100 px-2 rounded",children:[(0,r.jsx)("span",{children:"Amount Due:"}),(0,r.jsx)("span",{className:"text-red-600",children:T(P.total)})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Method"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.paymentMethod})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Salesperson"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.salespersonName||"N/A"})]})]}),x.comments&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Comments"}),(0,r.jsx)("p",{className:"text-gray-600",children:x.comments})]}),(0,r.jsxs)("div",{className:"mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800 mb-2",children:"Payment Terms"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Payment is due within 30 days of invoice date. Please include the reference number with your payment."})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:w,disabled:j,className:"bg-red-600 text-white px-6 py-3 rounded text-lg hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2",children:[j&&(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),j?"Creating...":"Confirm Invoice"]}),(0,r.jsx)("button",{onClick:()=>window.print(),className:"bg-blue-600 text-white px-6 py-3 rounded text-lg hover:bg-blue-700 transition-colors",children:"\uD83D\uDCF8 Print/Save"})]})]})]})})}},3873:e=>{"use strict";e.exports=require("path")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9293:(e,s,t)=>{Promise.resolve().then(t.bind(t,2276))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[825,227],()=>t(2862));module.exports=r})();