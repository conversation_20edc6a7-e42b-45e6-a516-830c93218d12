(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[353],{7908:(e,t,s)=>{Promise.resolve().then(s.bind(s,8711))},8711:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(5155),i=s(2115),l=s(5695),r=s(9053),n=s(7389),o=s(5731),c=s(3213);function d(){let e=(0,l.useRouter)(),{messages:t,showToast:s,removeToast:d}=(0,n.d)(),{data:m,loading:x,error:b,execute:h}=(0,c.gf)(o.FH.quotations.getAll),{data:g,loading:u,error:v,execute:j}=(0,c.gf)(o.FH.invoices.getAll),{mutate:p,loading:N}=(0,c.L2)(o.FH.quotations.convertToReceipt),{mutate:y,loading:f}=(0,c.L2)(o.FH.invoices.convertToReceipt),[w,_]=(0,i.useState)(new Set),q=e=>"".concat("R").concat(parseFloat(e.toString()).toFixed(2)),E=e=>new Date(e).toLocaleDateString();(0,i.useEffect)(()=>{b&&s("Error loading quotations","error")},[b,s]),(0,i.useEffect)(()=>{v&&s("Error loading invoices","error")},[v,s]);let C=async(e,t)=>{let a="".concat(e,"-").concat(t);_(e=>new Set(e).add(a));try{let a;("quotation"===e?await p(t):await y(t))&&(s("".concat(e.charAt(0).toUpperCase()+e.slice(1)," converted to receipt successfully!"),"success"),"quotation"===e?h():j())}catch(e){console.error("Error converting to receipt:",e),s("Error: ".concat(e instanceof Error?e.message:"Conversion failed"),"error")}finally{_(e=>{let t=new Set(e);return t.delete(a),t})}};(0,i.useEffect)(()=>{h(),j()},[h,j]);let k=e=>{let{item:t,type:s}=e,i="quotation"===s?t.quotation_id:t.invoice_id,l="".concat(s,"-").concat(i),r=w.has(l);return(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-300 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("div",{className:"font-bold text-blue-600 text-lg",children:t.reference_number}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:E(t.date)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4",children:[(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-bold text-gray-800",children:"Customer:"}),(0,a.jsx)("div",{className:"text-gray-600",children:t.billing_name})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-bold text-gray-800",children:"Email:"}),(0,a.jsx)("div",{className:"text-gray-600",children:t.billing_email})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-bold text-gray-800",children:"Payment Method:"}),(0,a.jsx)("div",{className:"text-gray-600",children:t.payment_method})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("div",{className:"font-bold text-gray-800",children:"Salesperson:"}),(0,a.jsx)("div",{className:"text-gray-600",children:t.salesperson_name||"N/A"})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"font-bold mb-2 text-gray-800",children:["Products (",t.items.length," items):"]}),t.items.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white p-2 mb-1 rounded text-sm border-l-4 border-blue-500",children:[e.item_name," - Qty: ",e.quantity," \xd7 ",q(e.unit_price_excluding_tax)," = ",q(e.total_price)]},t))]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 p-3 bg-white rounded",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 mb-2 md:mb-0",children:["Subtotal: ",q(t.subtotal)," | Tax: ",q(t.tax)]}),(0,a.jsxs)("div",{className:"font-bold text-blue-600 text-lg",children:["Total: ",q(t.total)]})]}),(0,a.jsx)("button",{onClick:()=>C(s,i),disabled:r,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed",children:r?"Converting...":"Convert to Receipt"})]})};return(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)(n.A,{messages:t,onRemove:d}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-5 shadow-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-5 flex-wrap gap-4",children:[(0,a.jsx)("h1",{className:"text-gray-800 text-2xl font-normal m-0",children:"Convert to Receipt"}),(0,a.jsx)("button",{onClick:()=>e.push("/"),className:"bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700 transition-colors",children:"← Back to Dashboard"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"Select quotations or invoices below to convert them into receipts. Once converted, the original quotation/invoice will be removed from the system."}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Quotations"}),x?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading quotations..."})]}):m&&0!==m.length?(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:m.map(e=>(0,a.jsx)(k,{item:e,type:"quotation"},e.quotation_id))}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC4"}),(0,a.jsx)("p",{children:"No quotations available for conversion."})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Invoices"}),u?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading invoices..."})]}):g&&0!==g.length?(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:g.map(e=>(0,a.jsx)(k,{item:e,type:"invoice"},e.invoice_id))}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83E\uDDFE"}),(0,a.jsx)("p",{children:"No invoices available for conversion."})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[535,441,684,358],()=>t(7908)),_N_E=e.O()}]);