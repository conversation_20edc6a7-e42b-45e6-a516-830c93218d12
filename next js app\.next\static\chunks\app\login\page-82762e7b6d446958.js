(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var a=r(5155),s=r(2115),o=r(2799);let n=(0,s.createContext)(void 0);function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[i,c]=(0,s.useState)(!0),[u,d]=(0,s.useState)(!1),h=async()=>{try{if(c(!0),!await (0,o.z)({redirectOnFail:!1}))return l(null),d(!1),!1;{let e=(0,o.HW)();return l(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),l(null),d(!1),!1}finally{c(!1)}},m=async(e,t)=>{try{c(!0);let r=await fetch("".concat(o.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return o.IG.set("authToken",a.token),o.IG.set("userInfo",JSON.stringify(a.user)),o.IG.set("lastAuthCheck",Date.now().toString()),l(a.user),d(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{c(!1)}};return(0,s.useEffect)(()=>{h()},[]),(0,a.jsx)(n.Provider,{value:{user:r,isLoading:i,isAuthenticated:u,login:m,logout:()=>{l(null),d(!1),(0,o.ri)()},checkAuth:h},children:t})}},2409:(e,t,r)=>{Promise.resolve().then(r.bind(r,9690))},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>n,IG:()=>s,JR:()=>a,ri:()=>l,z:()=>o});let a="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:o=2,timeout:n=8e3}=e,l=s.get("authToken"),i=s.get("userInfo");if(!l)return t&&(window.location.href="/login"),!1;if(i)try{JSON.parse(i);let e=s.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=o;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),n),o="?_t=".concat(Date.now()),i=await fetch("".concat(a,"/auth/me").concat(o),{headers:{Authorization:"Bearer ".concat(l)},signal:e.signal});if(clearTimeout(r),i.ok){let e=await i.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===i.status||403===i.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(i.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===o){if(i)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function n(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function l(){let e=s.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},9690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(5155),s=r(2115),o=r(5695),n=r(283),l=r(2799);function i(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(""),[c,u]=(0,s.useState)(!1),[d,h]=(0,s.useState)(""),[m,g]=(0,s.useState)(""),[f,b]=(0,s.useState)("checking"),{login:x,isAuthenticated:w,user:y}=(0,n.A)(),v=(0,o.useRouter)();(0,s.useEffect)(()=>{p()},[]),(0,s.useEffect)(()=>{w&&y&&(y.is_admin?v.push("/admin"):v.push("/"))},[w,y,v]);let p=async()=>{try{b("checking");let e=new AbortController,t=setTimeout(()=>e.abort(),15e3);try{let r=await fetch("".concat(l.JR,"/health"),{method:"GET",signal:e.signal,headers:{"Content-Type":"application/json"}});if(clearTimeout(t),r.ok)b("ready");else throw Error("Server responded with error")}catch(e){try{await fetch("".concat(l.JR,"/products"),{method:"GET",headers:{"Content-Type":"application/json"}}),b("ready")}catch(e){console.error("Server check failed:",e),b("error")}}}catch(e){console.error("Server status check failed:",e),b("error")}},S=async t=>{t.preventDefault(),h(""),g(""),u(!0);let a=await x(e,r);a.success?g("Login successful! Redirecting..."):h(a.message||"Login failed"),u(!1)};return"checking"===f?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg text-center max-w-md w-full mx-4",children:[(0,a.jsx)("div",{className:"w-15 h-15 border-6 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-5"}),(0,a.jsx)("div",{className:"text-lg font-bold text-gray-800 mb-2",children:"Connecting to Server"}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm leading-relaxed",children:["Please wait while we check the server status...",(0,a.jsx)("br",{}),"This may take a few moments."]})]})}):"error"===f?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg text-center max-w-md w-full mx-4",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-800 mb-2",children:"Server Connection Failed"}),(0,a.jsxs)("div",{className:"bg-red-100 border border-red-300 text-red-800 p-4 rounded mb-4",children:[(0,a.jsx)("strong",{children:"Server Connection Failed"}),(0,a.jsx)("br",{}),"Unable to connect to the server. Please check your internet connection or try again later."]}),(0,a.jsx)("button",{onClick:p,className:"bg-blue-600 text-white px-5 py-2 rounded hover:bg-blue-700 transition-colors",children:"Retry Connection"})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 p-5",children:(0,a.jsxs)("div",{className:"bg-white p-10 rounded-lg shadow-lg w-full max-w-md text-center border border-gray-200 animate-fadeInUp",children:[(0,a.jsx)("div",{className:"w-18 h-18 mx-auto mb-6 bg-blue-600 rounded-lg flex items-center justify-center text-white text-3xl font-bold shadow-lg",children:"S"}),(0,a.jsx)("h1",{className:"text-gray-800 mb-2 text-2xl font-normal",children:"Shans System"}),(0,a.jsxs)("div",{className:"text-gray-600 text-sm mb-8 leading-relaxed",children:["Please sign in to your account",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Note:"})," Admins will be automatically redirected to the admin dashboard"]}),c?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Signing you in..."})]}):(0,a.jsxs)("form",{onSubmit:S,className:"space-y-5",children:[(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("label",{htmlFor:"email",className:"block mb-1 text-gray-800",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded text-base transition-all duration-300 focus:outline-none focus:border-blue-600 focus:shadow-md"})]}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("label",{htmlFor:"password",className:"block mb-1 text-gray-800",children:"Password"}),(0,a.jsx)("input",{type:"password",id:"password",value:r,onChange:e=>i(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded text-base transition-all duration-300 focus:outline-none focus:border-blue-600 focus:shadow-md"})]}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"w-full p-3 bg-blue-600 text-white border-none rounded text-base font-normal cursor-pointer transition-colors duration-300 mt-2 hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed",children:"Sign In"})]}),d&&(0,a.jsx)("div",{className:"text-red-600 mt-4 p-3 bg-red-100 border border-red-300 rounded text-sm",children:d}),m&&(0,a.jsx)("div",{className:"text-green-700 mt-4 p-3 bg-green-100 border border-green-300 rounded text-sm",children:m}),(0,a.jsx)("div",{className:"mt-8 text-gray-500 text-xs border-t border-gray-200 pt-5",children:(0,a.jsx)("p",{children:"Shans System \xa9 2024"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(2409)),_N_E=e.O()}]);