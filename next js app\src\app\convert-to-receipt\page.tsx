'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';
import ToastContainer, { useToast } from '@/components/Toast';
import { api } from '@/lib/api';
import { useApi, useApiMutation } from '@/hooks/useApi';

interface QuotationItem {
  quotation_id: number;
  reference_number: string;
  date: string;
  billing_name: string;
  billing_email: string;
  payment_method: string;
  salesperson_name: string;
  subtotal: number;
  tax: number;
  total: number;
  items: Array<{
    item_name: string;
    quantity: number;
    unit_price_excluding_tax: number;
    total_price: number;
  }>;
}

interface InvoiceItem {
  invoice_id: number;
  reference_number: string;
  date: string;
  billing_name: string;
  billing_email: string;
  payment_method: string;
  salesperson_name: string;
  subtotal: number;
  tax: number;
  total: number;
  items: Array<{
    item_name: string;
    quantity: number;
    unit_price_excluding_tax: number;
    total_price: number;
  }>;
}

const CURRENCY_SYMBOL = 'R';

export default function ConvertToReceiptPage() {
  const router = useRouter();
  const { messages, showToast, removeToast } = useToast();
  
  const [quotations, setQuotations] = useState<QuotationItem[]>([]);
  const [invoices, setInvoices] = useState<InvoiceItem[]>([]);
  const [quotationsLoading, setQuotationsLoading] = useState(true);
  const [invoicesLoading, setInvoicesLoading] = useState(true);
  const [convertingItems, setConvertingItems] = useState<Set<string>>(new Set());

  const formatCurrency = (amount: number) => {
    return `${CURRENCY_SYMBOL}${parseFloat(amount.toString()).toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const loadQuotations = async () => {
    try {
      setQuotationsLoading(true);
      const response = await fetch(`${API_BASE_URL}/quotations`);
      const data = await response.json();
      setQuotations(data);
    } catch (error) {
      console.error('Error loading quotations:', error);
      showToast('Error loading quotations', 'error');
    } finally {
      setQuotationsLoading(false);
    }
  };

  const loadInvoices = async () => {
    try {
      setInvoicesLoading(true);
      const response = await fetch(`${API_BASE_URL}/invoices`);
      const data = await response.json();
      setInvoices(data);
    } catch (error) {
      console.error('Error loading invoices:', error);
      showToast('Error loading invoices', 'error');
    } finally {
      setInvoicesLoading(false);
    }
  };

  const convertToReceipt = async (type: 'quotation' | 'invoice', id: number) => {
    const itemKey = `${type}-${id}`;
    setConvertingItems(prev => new Set(prev).add(itemKey));

    try {
      const endpoint = type === 'quotation'
        ? `${API_BASE_URL}/convert-quotation-to-receipt/${id}`
        : `${API_BASE_URL}/convert-invoice-to-receipt/${id}`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (response.ok) {
        showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} converted to receipt successfully!`, 'success');
        // Reload the appropriate section
        if (type === 'quotation') {
          loadQuotations();
        } else {
          loadInvoices();
        }
      } else {
        throw new Error(result.message || 'Conversion failed');
      }
    } catch (error) {
      console.error('Error converting to receipt:', error);
      showToast(`Error: ${error instanceof Error ? error.message : 'Conversion failed'}`, 'error');
    } finally {
      setConvertingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(itemKey);
        return newSet;
      });
    }
  };

  useEffect(() => {
    loadQuotations();
    loadInvoices();
  }, []);

  const ItemCard = ({ item, type }: { item: QuotationItem | InvoiceItem, type: 'quotation' | 'invoice' }) => {
    const itemId = type === 'quotation' ? (item as QuotationItem).quotation_id : (item as InvoiceItem).invoice_id;
    const itemKey = `${type}-${itemId}`;
    const isConverting = convertingItems.has(itemKey);

    return (
      <div className="bg-gray-50 border border-gray-300 rounded-lg p-4 mb-4">
        <div className="flex justify-between items-center mb-3">
          <div className="font-bold text-blue-600 text-lg">{item.reference_number}</div>
          <div className="text-gray-600 text-sm">{formatDate(item.date)}</div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
          <div className="text-sm">
            <div className="font-bold text-gray-800">Customer:</div>
            <div className="text-gray-600">{item.billing_name}</div>
          </div>
          <div className="text-sm">
            <div className="font-bold text-gray-800">Email:</div>
            <div className="text-gray-600">{item.billing_email}</div>
          </div>
          <div className="text-sm">
            <div className="font-bold text-gray-800">Payment Method:</div>
            <div className="text-gray-600">{item.payment_method}</div>
          </div>
          <div className="text-sm">
            <div className="font-bold text-gray-800">Salesperson:</div>
            <div className="text-gray-600">{item.salesperson_name || 'N/A'}</div>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-gray-800">Products ({item.items.length} items):</div>
          {item.items.map((product, index) => (
            <div key={index} className="bg-white p-2 mb-1 rounded text-sm border-l-4 border-blue-500">
              {product.item_name} - Qty: {product.quantity} × {formatCurrency(product.unit_price_excluding_tax)} = {formatCurrency(product.total_price)}
            </div>
          ))}
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 p-3 bg-white rounded">
          <div className="text-sm text-gray-600 mb-2 md:mb-0">
            Subtotal: {formatCurrency(item.subtotal)} | Tax: {formatCurrency(item.tax)}
          </div>
          <div className="font-bold text-blue-600 text-lg">
            Total: {formatCurrency(item.total)}
          </div>
        </div>

        <button
          onClick={() => convertToReceipt(type, itemId)}
          disabled={isConverting}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isConverting ? 'Converting...' : 'Convert to Receipt'}
        </button>
      </div>
    );
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100">
        <ToastContainer messages={messages} onRemove={removeToast} />
        
        <div className="max-w-4xl mx-auto bg-white p-5 shadow-lg">
          <div className="flex justify-between items-center mb-5 flex-wrap gap-4">
            <h1 className="text-gray-800 text-2xl font-normal m-0">Convert to Receipt</h1>
            <button
              onClick={() => router.push('/')}
              className="bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700 transition-colors"
            >
              ← Back to Dashboard
            </button>
          </div>

          <p className="text-gray-600 mb-8">
            Select quotations or invoices below to convert them into receipts. Once converted, the original quotation/invoice will be removed from the system.
          </p>

          {/* Quotations Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Quotations</h2>
            {quotationsLoading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                <p className="text-gray-600">Loading quotations...</p>
              </div>
            ) : quotations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">📄</div>
                <p>No quotations available for conversion.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {quotations.map((quotation) => (
                  <ItemCard key={quotation.quotation_id} item={quotation} type="quotation" />
                ))}
              </div>
            )}
          </div>

          {/* Invoices Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Invoices</h2>
            {invoicesLoading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                <p className="text-gray-600">Loading invoices...</p>
              </div>
            ) : invoices.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">🧾</div>
                <p>No invoices available for conversion.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {invoices.map((invoice) => (
                  <ItemCard key={invoice.invoice_id} item={invoice} type="invoice" />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
