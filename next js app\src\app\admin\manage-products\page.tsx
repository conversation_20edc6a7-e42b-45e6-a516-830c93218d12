'use client';

import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/ProtectedRoute';

export default function ManageProducts() {
  const router = useRouter();

  return (
    <ProtectedRoute requireAdmin>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/admin')}
              className="mb-4 bg-gray-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-gray-700 transition-colors"
            >
              ← Back to Admin Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Manage Products</h1>
          </div>

          {/* Coming Soon Content */}
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">📦</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Product Management</h2>
            <p className="text-gray-500 mb-6">
              This feature is coming soon. You&apos;ll be able to add, edit, and manage all products in your inventory.
            </p>
            <div className="text-sm text-gray-400">
              Features will include: Product search, filtering, stock management, and more.
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

  const { 
    data: carBrands = [], 
    loading: carBrandsLoading 
  } = useApiList<CarBrand>('/car-brands');

  const { 
    data: carModels = [], 
    loading: carModelsLoading 
  } = useApiList<CarModel>('/car-models');

  const addProductMutation = useApiMutation<Product, ProductFormData>('/products', 'POST');
  const updateProductMutation = useApiMutation<Product, Partial<ProductFormData>>('/products', 'PUT');
  const deleteProductMutation = useApiMutation<void, void>('/products', 'DELETE');

  // Filter and search products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.item_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.item_code.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCarBrand = !filters.carBrand || product.car_brand_id?.toString() === filters.carBrand;
    const matchesCategory = !filters.productCategory || product.product_category === filters.productCategory;
    const matchesCarModel = !filters.carModel || product.car_model_id?.toString() === filters.carModel;
    const matchesRoom = !filters.room || product.room_id?.toString() === filters.room;
    const matchesColour = !filters.colourTape || product.colour_tape === filters.colourTape;

    return matchesSearch && matchesCarBrand && matchesCategory && matchesCarModel && matchesRoom && matchesColour;
  });

  const handleAddProduct = async (formData: ProductFormData) => {
    try {
      await addProductMutation.mutate(formData);
      setShowAddModal(false);
      refetchProducts();
    } catch (error) {
      console.error('Error adding product:', error);
    }
  };

  const handleEditProduct = async (formData: Partial<ProductFormData>) => {
    if (!editingProduct) return;
    
    try {
      await updateProductMutation.mutate(formData, { 
        endpoint: `/products/${editingProduct.id}` 
      });
      setShowEditModal(false);
      setEditingProduct(null);
      refetchProducts();
    } catch (error) {
      console.error('Error updating product:', error);
    }
  };

  const handleDeleteProduct = async (productId: number) => {
    if (!confirm('Are you sure you want to delete this product?')) return;
    
    try {
      await deleteProductMutation.mutate(undefined, { 
        endpoint: `/products/${productId}` 
      });
      refetchProducts();
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  };

  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setShowEditModal(true);
  };

  const isLoading = productsLoading || roomsLoading || categoriesLoading || carBrandsLoading || carModelsLoading;

  if (isLoading) {
    return (
      <ProtectedRoute requireAdmin>
        <LoadingScreen isVisible={true} message="Loading products..." />
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/admin')}
              className="text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2"
            >
              ← Back to Admin Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Manage Products</h1>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 mb-6">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <span>+</span> Add Product
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
              <span>⚙️</span> Manage Categories
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
              <span>🚗</span> Manage Car Brands
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
              <span>🚘</span> Manage Car Models
            </button>
          </div>

          {/* Filters */}
          <div className="bg-white p-4 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Car Brand</label>
                <select
                  value={filters.carBrand}
                  onChange={(e) => setFilters(prev => ({ ...prev, carBrand: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="">All Brands</option>
                  {carBrands.map(brand => (
                    <option key={brand.id} value={brand.id}>{brand.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={filters.productCategory}
                  onChange={(e) => setFilters(prev => ({ ...prev, productCategory: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>{category.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Car Model</label>
                <select
                  value={filters.carModel}
                  onChange={(e) => setFilters(prev => ({ ...prev, carModel: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="">All Models</option>
                  {carModels.map(model => (
                    <option key={model.id} value={model.id}>{model.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Room</label>
                <select
                  value={filters.room}
                  onChange={(e) => setFilters(prev => ({ ...prev, room: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  <option value="">All Rooms</option>
                  {rooms.map(room => (
                    <option key={room.id} value={room.id}>{room.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search products..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
          </div>

          {/* Products Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car Brand</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Car Model</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Retail Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredProducts.map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {product.item_code}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {rooms.find(r => r.id === product.room_id)?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {product.item_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {carBrands.find(b => b.id === product.car_brand_id)?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {carModels.find(m => m.id === product.car_model_id)?.name || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.product_category?.replace(/_/g, ' ')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.available_stock}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        R {product.unit_retail_price?.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => openEditModal(product)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Add Product Modal */}
          {showAddModal && (
            <ProductModal
              isOpen={showAddModal}
              onClose={() => setShowAddModal(false)}
              onSubmit={handleAddProduct}
              rooms={rooms}
              categories={categories}
              carBrands={carBrands}
              carModels={carModels}
              title="Add Product"
            />
          )}

          {/* Edit Product Modal */}
          {showEditModal && editingProduct && (
            <ProductModal
              isOpen={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditingProduct(null);
              }}
              onSubmit={handleEditProduct}
              rooms={rooms}
              categories={categories}
              carBrands={carBrands}
              carModels={carModels}
              title="Edit Product"
              initialData={editingProduct}
            />
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}

// Product Modal Component
interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  rooms: Room[];
  categories: Category[];
  carBrands: CarBrand[];
  carModels: CarModel[];
  title: string;
  initialData?: Product;
}

function ProductModal({
  isOpen,
  onClose,
  onSubmit,
  rooms,
  categories,
  carBrands,
  carModels,
  title,
  initialData
}: ProductModalProps) {
  const [formData, setFormData] = useState<Partial<ProductFormData>>({
    item_code: initialData?.item_code || '',
    room_id: initialData?.room_id || 0,
    item_name: initialData?.item_name || '',
    car_brand_id: initialData?.car_brand_id || 0,
    car_model_id: initialData?.car_model_id || 0,
    product_category: initialData?.product_category || '',
    available_stock: initialData?.available_stock || 0,
    low_stock_threshold: initialData?.low_stock_threshold || 5,
    colour_tape: initialData?.colour_tape || '#000000',
    location: initialData?.location || '',
    unit_retail_price: initialData?.unit_retail_price || 0,
    wholesale_price: initialData?.wholesale_price || 0,
    unit_cost: initialData?.unit_cost || 0,
    supplier_code: initialData?.supplier_code || '',
    additional_comments: initialData?.additional_comments || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-900">{title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Item Code *
                </label>
                <input
                  type="text"
                  value={formData.item_code}
                  onChange={(e) => handleInputChange('item_code', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Room *
                </label>
                <select
                  value={formData.room_id}
                  onChange={(e) => handleInputChange('room_id', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Select a room</option>
                  {rooms.map(room => (
                    <option key={room.id} value={room.id}>{room.name}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Item Name *
                </label>
                <input
                  type="text"
                  value={formData.item_name}
                  onChange={(e) => handleInputChange('item_name', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Car Brand
                </label>
                <select
                  value={formData.car_brand_id}
                  onChange={(e) => handleInputChange('car_brand_id', parseInt(e.target.value) || undefined)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Select a brand</option>
                  {carBrands.map(brand => (
                    <option key={brand.id} value={brand.id}>{brand.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Car Model
                </label>
                <select
                  value={formData.car_model_id}
                  onChange={(e) => handleInputChange('car_model_id', parseInt(e.target.value) || undefined)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">Select a model</option>
                  {carModels.map(model => (
                    <option key={model.id} value={model.id}>{model.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product Category *
                </label>
                <select
                  value={formData.product_category}
                  onChange={(e) => handleInputChange('product_category', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>{category.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Available Stock
                </label>
                <input
                  type="number"
                  value={formData.available_stock}
                  onChange={(e) => handleInputChange('available_stock', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Low Stock Threshold
                </label>
                <input
                  type="number"
                  value={formData.low_stock_threshold}
                  onChange={(e) => handleInputChange('low_stock_threshold', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Colour Tape
                </label>
                <div className="flex gap-2">
                  <input
                    type="color"
                    value={formData.colour_tape}
                    onChange={(e) => handleInputChange('colour_tape', e.target.value)}
                    className="w-16 h-10 border border-gray-300 rounded-md"
                  />
                  <input
                    type="text"
                    value={formData.colour_tape}
                    onChange={(e) => handleInputChange('colour_tape', e.target.value)}
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2"
                    placeholder="#000000"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Retail Price (R)
                </label>
                <input
                  type="number"
                  value={formData.unit_retail_price}
                  onChange={(e) => handleInputChange('unit_retail_price', parseFloat(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Wholesale Price (R)
                </label>
                <input
                  type="number"
                  value={formData.wholesale_price}
                  onChange={(e) => handleInputChange('wholesale_price', parseFloat(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Unit Cost (R)
                </label>
                <input
                  type="number"
                  value={formData.unit_cost}
                  onChange={(e) => handleInputChange('unit_cost', parseFloat(e.target.value))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Supplier Code
                </label>
                <input
                  type="text"
                  value={formData.supplier_code}
                  onChange={(e) => handleInputChange('supplier_code', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Comments
                </label>
                <textarea
                  value={formData.additional_comments}
                  onChange={(e) => handleInputChange('additional_comments', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {initialData ? 'Update' : 'Add'} Product
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
