"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[535],{283:(e,t,r)=>{r.d(t,{A:()=>c,AuthProvider:()=>i});var n=r(5155),a=r(2115),o=r(2799);let s=(0,a.createContext)(void 0);function c(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function i(e){let{children:t}=e,[r,c]=(0,a.useState)(null),[i,u]=(0,a.useState)(!0),[l,d]=(0,a.useState)(!1),h=async()=>{try{if(u(!0),!await (0,o.z)({redirectOnFail:!1}))return c(null),d(!1),!1;{let e=(0,o.HW)();return c(e),d(!0),!0}}catch(e){return console.error("Auth check failed:",e),c(null),d(!1),!1}finally{u(!1)}},f=async(e,t)=>{try{u(!0);let r=await fetch("".concat(o.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),n=await r.json();if(r.ok)return o.IG.set("authToken",n.token),o.IG.set("userInfo",JSON.stringify(n.user)),o.IG.set("lastAuthCheck",Date.now().toString()),c(n.user),d(!0),{success:!0};return{success:!1,message:n.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{u(!1)}};return(0,a.useEffect)(()=>{h()},[]),(0,n.jsx)(s.Provider,{value:{user:r,isLoading:i,isAuthenticated:l,login:f,logout:()=>{c(null),d(!1),(0,o.ri)()},checkAuth:h},children:t})}},2799:(e,t,r)=>{r.d(t,{HW:()=>s,IG:()=>a,JR:()=>n,ri:()=>c,z:()=>o});let n="https://shans-backend.onrender.com/api",a={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:o=2,timeout:s=8e3}=e,c=a.get("authToken"),i=a.get("userInfo");if(!c)return t&&(window.location.href="/login"),!1;if(i)try{JSON.parse(i);let e=a.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=o;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),s),o="?_t=".concat(Date.now()),i=await fetch("".concat(n,"/auth/me").concat(o),{headers:{Authorization:"Bearer ".concat(c)},signal:e.signal});if(clearTimeout(r),i.ok){let e=await i.json();return a.set("userInfo",JSON.stringify(e.user)),a.set("lastAuthCheck",Date.now().toString()),!0}if(401===i.status||403===i.status){a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(i.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===o){if(i)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function s(){let e=a.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function c(){let e=a.get("authToken");e&&fetch("".concat(n,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),a.remove("authToken"),a.remove("userInfo"),a.remove("lastAuthCheck"),window.location.href="/login"}},3213:(e,t,r)=>{r.d(t,{L2:()=>s,U9:()=>o,gf:()=>a});var n=r(2115);function a(e){let[t,r]=(0,n.useState)({data:null,loading:!1,error:null}),a=(0,n.useCallback)(async function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];r(e=>({...e,loading:!0,error:null}));try{let t=await e(...n);if(t.success&&t.data)return r({data:t.data,loading:!1,error:null}),t.data;return r({data:null,loading:!1,error:t.error||"An unknown error occurred"}),null}catch(e){return r({data:null,loading:!1,error:e instanceof Error?e.message:"An unknown error occurred"}),null}},[e]),o=(0,n.useCallback)(()=>{r({data:null,loading:!1,error:null})},[]);return{...t,execute:a,reset:o}}function o(e){let t=a(e),r=(0,n.useCallback)(e=>{t.data&&i(t=>({...t,data:t.data?[...t.data,...e]:e}))},[t.data]),o=(0,n.useCallback)((e,t)=>{i(r=>{if(!r.data)return r;let n=[...r.data];return n[e]=t,{...r,data:n}})},[]),s=(0,n.useCallback)(e=>{i(t=>{if(!t.data)return t;let r=t.data.filter((t,r)=>r!==e);return{...t,data:r}})},[]),[c,i]=(0,n.useState)({data:null,loading:!1,error:null});return{...t,append:r,updateItem:o,removeItem:s}}function s(e){let[t,r]=(0,n.useState)(!1),[a,o]=(0,n.useState)(null);return{mutate:(0,n.useCallback)(async t=>{r(!0),o(null);try{let n=await e(t);if(n.success&&n.data)return r(!1),n.data;return o(n.error||"An unknown error occurred"),r(!1),null}catch(e){return o(e instanceof Error?e.message:"An unknown error occurred"),r(!1),null}},[e]),loading:t,error:a,reset:(0,n.useCallback)(()=>{r(!1),o(null)},[])}}},5695:(e,t,r)=>{var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},5731:(e,t,r)=>{r.d(t,{FH:()=>o});var n=r(2799);async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r="".concat(n.JR).concat(e),a=await fetch(r,{headers:{"Content-Type":"application/json",...t.headers},...t}),o=await a.json();if(!a.ok)return{success:!1,error:o.message||"HTTP error! status: ".concat(a.status)};return{success:!0,data:o}}catch(e){return{success:!1,error:e instanceof Error?e.message:"An unknown error occurred"}}}let o={products:{search:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20;return a("/products/search?q=".concat(encodeURIComponent(e),"&page=").concat(t,"&limit=").concat(r))},getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return a("/products?page=".concat(e,"&limit=").concat(t))},getByCode:async e=>a("/products/".concat(encodeURIComponent(e)))},quotations:{create:async e=>a("/quotations",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>a("/quotations"),getById:async e=>a("/quotations/".concat(e)),delete:async e=>a("/quotations/".concat(e),{method:"DELETE"}),convertToReceipt:async e=>a("/convert-quotation-to-receipt/".concat(e),{method:"POST"})},invoices:{create:async e=>a("/invoices",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>a("/invoices"),getById:async e=>a("/invoices/".concat(e)),delete:async e=>a("/invoices/".concat(e),{method:"DELETE"}),convertToReceipt:async e=>a("/convert-invoice-to-receipt/".concat(e),{method:"POST"})},receipts:{create:async e=>a("/receipts",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>a("/receipts"),getById:async e=>a("/receipts/".concat(e)),delete:async e=>a("/receipts/".concat(e),{method:"DELETE"})},auth:{login:async(e,t)=>a("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}),checkStatus:async()=>a("/auth/status"),verifyToken:async e=>a("/auth/verify",{headers:{Authorization:"Bearer ".concat(e)}})},server:{checkStatus:async()=>a("/status")}}},7389:(e,t,r)=>{r.d(t,{A:()=>s,d:()=>c});var n=r(5155),a=r(2115);function o(e){let{message:t,onRemove:r}=e;(0,a.useEffect)(()=>{let e=setTimeout(()=>{r(t.id)},3e3);return()=>clearTimeout(e)},[t.id,r]);let o={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[t.type];return(0,n.jsx)("div",{className:"".concat(o," text-white px-4 py-2 rounded opacity-95 text-sm toast"),children:t.message})}function s(e){let{messages:t,onRemove:r}=e;return(0,n.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:t.map(e=>(0,n.jsx)(o,{message:e,onRemove:r},e.id))})}function c(){let[e,t]=(0,a.useState)([]);return{messages:e,showToast:function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",n=Math.random().toString(36).substr(2,9);t(t=>[...t,{id:n,message:e,type:r}])},removeToast:e=>{t(t=>t.filter(t=>t.id!==e))}}}},9053:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(5155),a=r(2115),o=r(5695),s=r(283);function c(e){let{children:t,requireAdmin:r=!1,redirectTo:c="/login"}=e,{user:i,isLoading:u,isAuthenticated:l}=(0,s.A)(),d=(0,o.useRouter)(),[h,f]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{if(!u){if(!l)return void d.push(c);if(r&&i&&!i.is_admin)return void d.push("/");f(!0)}},[l,u,i,r,d,c]),u)?(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):h?(0,n.jsx)(n.Fragment,{children:t}):null}}}]);