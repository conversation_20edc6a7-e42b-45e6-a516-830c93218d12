(()=>{var e={};e.id=353,e.ids=[353],e.modules={27:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,d:()=>i});var r=s(687),n=s(3210);function a({message:e,onRemove:t}){let s={success:"bg-green-600",error:"bg-red-600",info:"bg-blue-600"}[e.type];return(0,r.jsx)("div",{className:`${s} text-white px-4 py-2 rounded opacity-95 text-sm toast`,children:e.message})}function o({messages:e,onRemove:t}){return(0,r.jsx)("div",{className:"fixed top-5 right-5 z-50 flex flex-col gap-2",children:e.map(e=>(0,r.jsx)(a,{message:e,onRemove:t},e.id))})}function i(){let[e,t]=(0,n.useState)([]);return{messages:e,showToast:(e,s="info")=>{let r=Math.random().toString(36).substr(2,9);t(t=>[...t,{id:r,message:e,type:s}])},removeToast:e=>{t(t=>t.filter(t=>t.id!==e))}}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1052:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(687),n=s(3210),a=s(6189),o=s(769),i=s(27),c=s(2185),l=s(9265);function d(){let e=(0,a.useRouter)(),{messages:t,showToast:s,removeToast:d}=(0,i.d)(),{data:m,loading:x,error:p,execute:u}=(0,l.gf)(c.FH.quotations.getAll),{data:h,loading:g,error:v,execute:b}=(0,l.gf)(c.FH.invoices.getAll),{mutate:y,loading:f}=(0,l.L2)(c.FH.quotations.convertToReceipt),{mutate:j,loading:N}=(0,l.L2)(c.FH.invoices.convertToReceipt),[w,_]=(0,n.useState)(new Set),q=e=>`R${parseFloat(e.toString()).toFixed(2)}`,$=e=>new Date(e).toLocaleDateString(),C=async(e,t)=>{let r=`${e}-${t}`;_(e=>new Set(e).add(r));try{let r;("quotation"===e?await y(t):await j(t))&&(s(`${e.charAt(0).toUpperCase()+e.slice(1)} converted to receipt successfully!`,"success"),"quotation"===e?u():b())}catch(e){console.error("Error converting to receipt:",e),s(`Error: ${e instanceof Error?e.message:"Conversion failed"}`,"error")}finally{_(e=>{let t=new Set(e);return t.delete(r),t})}},P=({item:e,type:t})=>{let s="quotation"===t?e.quotation_id:e.invoice_id,n=`${t}-${s}`,a=w.has(n);return(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-300 rounded-lg p-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsx)("div",{className:"font-bold text-blue-600 text-lg",children:e.reference_number}),(0,r.jsx)("div",{className:"text-gray-600 text-sm",children:$(e.date)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4",children:[(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-bold text-gray-800",children:"Customer:"}),(0,r.jsx)("div",{className:"text-gray-600",children:e.billing_name})]}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-bold text-gray-800",children:"Email:"}),(0,r.jsx)("div",{className:"text-gray-600",children:e.billing_email})]}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-bold text-gray-800",children:"Payment Method:"}),(0,r.jsx)("div",{className:"text-gray-600",children:e.payment_method})]}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-bold text-gray-800",children:"Salesperson:"}),(0,r.jsx)("div",{className:"text-gray-600",children:e.salesperson_name||"N/A"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"font-bold mb-2 text-gray-800",children:["Products (",e.items.length," items):"]}),e.items.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white p-2 mb-1 rounded text-sm border-l-4 border-blue-500",children:[e.item_name," - Qty: ",e.quantity," \xd7 ",q(e.unit_price_excluding_tax)," = ",q(e.total_price)]},t))]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-4 p-3 bg-white rounded",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-2 md:mb-0",children:["Subtotal: ",q(e.subtotal)," | Tax: ",q(e.tax)]}),(0,r.jsxs)("div",{className:"font-bold text-blue-600 text-lg",children:["Total: ",q(e.total)]})]}),(0,r.jsx)("button",{onClick:()=>C(t,s),disabled:a,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed",children:a?"Converting...":"Convert to Receipt"})]})};return(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)(i.A,{messages:t,onRemove:d}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-5 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-5 flex-wrap gap-4",children:[(0,r.jsx)("h1",{className:"text-gray-800 text-2xl font-normal m-0",children:"Convert to Receipt"}),(0,r.jsx)("button",{onClick:()=>e.push("/"),className:"bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700 transition-colors",children:"← Back to Dashboard"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Select quotations or invoices below to convert them into receipts. Once converted, the original quotation/invoice will be removed from the system."}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Quotations"}),x?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading quotations..."})]}):m&&0!==m.length?(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:m.map(e=>(0,r.jsx)(P,{item:e,type:"quotation"},e.quotation_id))}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC4"}),(0,r.jsx)("p",{children:"No quotations available for conversion."})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Invoices"}),g?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading invoices..."})]}):h&&0!==h.length?(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:h.map(e=>(0,r.jsx)(P,{item:e,type:"invoice"},e.invoice_id))}):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83E\uDDFE"}),(0,r.jsx)("p",{children:"No invoices available for conversion."})]})]})]})]})})}},2185:(e,t,s)=>{"use strict";s.d(t,{FH:()=>a});var r=s(1439);async function n(e,t={}){try{let s=`${r.JR}${e}`,n=await fetch(s,{headers:{"Content-Type":"application/json",...t.headers},...t}),a=await n.json();if(!n.ok)return{success:!1,error:a.message||`HTTP error! status: ${n.status}`};return{success:!0,data:a}}catch(e){return{success:!1,error:e instanceof Error?e.message:"An unknown error occurred"}}}let a={products:{search:async(e,t=1,s=20)=>n(`/products/search?q=${encodeURIComponent(e)}&page=${t}&limit=${s}`),getAll:async(e=1,t=50)=>n(`/products?page=${e}&limit=${t}`),getByCode:async e=>n(`/products/${encodeURIComponent(e)}`)},quotations:{create:async e=>n("/quotations",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/quotations"),getById:async e=>n(`/quotations/${e}`),delete:async e=>n(`/quotations/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-quotation-to-receipt/${e}`,{method:"POST"})},invoices:{create:async e=>n("/invoices",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/invoices"),getById:async e=>n(`/invoices/${e}`),delete:async e=>n(`/invoices/${e}`,{method:"DELETE"}),convertToReceipt:async e=>n(`/convert-invoice-to-receipt/${e}`,{method:"POST"})},receipts:{create:async e=>n("/receipts",{method:"POST",body:JSON.stringify(e)}),getAll:async()=>n("/receipts"),getById:async e=>n(`/receipts/${e}`),delete:async e=>n(`/receipts/${e}`,{method:"DELETE"})},auth:{login:async(e,t)=>n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}),checkStatus:async()=>n("/auth/status"),verifyToken:async e=>n("/auth/verify",{headers:{Authorization:`Bearer ${e}`}})},server:{checkStatus:async()=>n("/status")}}},2845:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Videos\\\\workspace\\\\shans-frontend\\\\next js app\\\\src\\\\app\\\\convert-to-receipt\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\convert-to-receipt\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>l});var r=s(5239),n=s(8088),a=s(8170),o=s.n(a),i=s(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l={children:["",{children:["convert-to-receipt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2845)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\convert-to-receipt\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Videos\\workspace\\shans-frontend\\next js app\\src\\app\\convert-to-receipt\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/convert-to-receipt/page",pathname:"/convert-to-receipt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5148:(e,t,s)=>{Promise.resolve().then(s.bind(s,1052))},7004:(e,t,s)=>{Promise.resolve().then(s.bind(s,2845))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[825,227],()=>s(5136));module.exports=r})();