(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[339],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>c});var a=r(5155),s=r(2115),n=r(2799);let o=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[r,i]=(0,s.useState)(null),[c,l]=(0,s.useState)(!0),[u,h]=(0,s.useState)(!1),d=async()=>{try{if(l(!0),!await (0,n.z)({redirectOnFail:!1}))return i(null),h(!1),!1;{let e=(0,n.HW)();return i(e),h(!0),!0}}catch(e){return console.error("Auth check failed:",e),i(null),h(!1),!1}finally{l(!1)}},m=async(e,t)=>{try{l(!0);let r=await fetch("".concat(n.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(r.ok)return n.IG.set("authToken",a.token),n.IG.set("userInfo",JSON.stringify(a.user)),n.IG.set("lastAuthCheck",Date.now().toString()),i(a.user),h(!0),{success:!0};return{success:!1,message:a.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{l(!1)}};return(0,s.useEffect)(()=>{d()},[]),(0,a.jsx)(o.Provider,{value:{user:r,isLoading:c,isAuthenticated:u,login:m,logout:()=>{i(null),h(!1),(0,n.ri)()},checkAuth:d},children:t})}},2799:(e,t,r)=>{"use strict";r.d(t,{HW:()=>o,IG:()=>s,JR:()=>a,ri:()=>i,z:()=>n});let a="https://shans-backend.onrender.com/api",s={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(r){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:r=!1,retryCount:n=2,timeout:o=8e3}=e,i=s.get("authToken"),c=s.get("userInfo");if(!i)return t&&(window.location.href="/login"),!1;if(c)try{JSON.parse(c);let e=s.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=n;e++)try{let e=new AbortController,r=setTimeout(()=>e.abort(),o),n="?_t=".concat(Date.now()),c=await fetch("".concat(a,"/auth/me").concat(n),{headers:{Authorization:"Bearer ".concat(i)},signal:e.signal});if(clearTimeout(r),c.ok){let e=await c.json();return s.set("userInfo",JSON.stringify(e.user)),s.set("lastAuthCheck",Date.now().toString()),!0}if(401===c.status||403===c.status){s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(c.status))}catch(r){if(console.warn("Auth check attempt ".concat(e+1," failed:"),r),e===n){if(c)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",r),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function o(){let e=s.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=s.get("authToken");e&&fetch("".concat(a,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),s.remove("authToken"),s.remove("userInfo"),s.remove("lastAuthCheck"),window.location.href="/login"}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6577:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(5155),s=r(5695),n=r(9053);function o(){let e=(0,s.useRouter)();return(0,a.jsx)(n.A,{requireAdmin:!0,children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("button",{onClick:()=>e.push("/admin"),className:"text-blue-600 hover:text-blue-800 mb-4 flex items-center gap-2",children:"← Back to Admin Dashboard"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Backup Files"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-12 text-center",children:[(0,a.jsx)("div",{className:"text-indigo-600 text-6xl mb-4",children:"☁️"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Data Backup & Recovery"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"This feature is coming soon. You'll be able to backup and restore your data."}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsx)("div",{children:"• Create data backups"}),(0,a.jsx)("div",{children:"• Schedule automatic backups"}),(0,a.jsx)("div",{children:"• Download backup files"}),(0,a.jsx)("div",{children:"• Restore from backups"})]})]})]})})})}},8621:(e,t,r)=>{Promise.resolve().then(r.bind(r,6577))},9053:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(5155),s=r(2115),n=r(5695),o=r(283);function i(e){let{children:t,requireAdmin:r=!1,redirectTo:i="/login"}=e,{user:c,isLoading:l,isAuthenticated:u}=(0,o.A)(),h=(0,n.useRouter)(),[d,m]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(!l){if(!u)return void h.push(i);if(r&&c&&!c.is_admin)return void h.push("/");m(!0)}},[u,l,c,r,h,i]),l)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):d?(0,a.jsx)(a.Fragment,{children:t}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(8621)),_N_E=e.O()}]);