(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,o)=>{"use strict";o.d(t,{A:()=>i,AuthProvider:()=>c});var r=o(5155),n=o(2115),a=o(2799);let s=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[o,i]=(0,n.useState)(null),[c,u]=(0,n.useState)(!0),[l,h]=(0,n.useState)(!1),f=async()=>{try{if(u(!0),!await (0,a.z)({redirectOnFail:!1}))return i(null),h(!1),!1;{let e=(0,a.HW)();return i(e),h(!0),!0}}catch(e){return console.error("Auth check failed:",e),i(null),h(!1),!1}finally{u(!1)}},g=async(e,t)=>{try{u(!0);let o=await fetch("".concat(a.JR,"/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),r=await o.json();if(o.ok)return a.IG.set("authToken",r.token),a.IG.set("userInfo",JSON.stringify(r.user)),a.IG.set("lastAuthCheck",Date.now().toString()),i(r.user),h(!0),{success:!0};return{success:!1,message:r.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please try again."}}finally{u(!1)}};return(0,n.useEffect)(()=>{f()},[]),(0,r.jsx)(s.Provider,{value:{user:o,isLoading:c,isAuthenticated:l,login:g,logout:()=>{i(null),h(!1),(0,a.ri)()},checkAuth:f},children:t})}},347:()=>{},2799:(e,t,o)=>{"use strict";o.d(t,{HW:()=>s,IG:()=>n,JR:()=>r,ri:()=>i,z:()=>a});let r="https://shans-backend.onrender.com/api",n={get:function(e){try{return localStorage.getItem(e)}catch(t){return console.warn("localStorage not available, using sessionStorage"),sessionStorage.getItem(e)}},set:function(e,t){try{localStorage.setItem(e,t)}catch(o){console.warn("localStorage not available, using sessionStorage"),sessionStorage.setItem(e,t)}},remove:function(e){try{localStorage.removeItem(e)}catch(t){console.warn("localStorage not available, using sessionStorage"),sessionStorage.removeItem(e)}}};async function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectOnFail:t=!0,showLoading:o=!1,retryCount:a=2,timeout:s=8e3}=e,i=n.get("authToken"),c=n.get("userInfo");if(!i)return t&&(window.location.href="/login"),!1;if(c)try{JSON.parse(c);let e=n.get("lastAuthCheck"),t=Date.now();if(e&&t-parseInt(e)<3e5)return!0}catch(e){console.warn("Error parsing cached user info:",e)}for(let e=0;e<=a;e++)try{let e=new AbortController,o=setTimeout(()=>e.abort(),s),a="?_t=".concat(Date.now()),c=await fetch("".concat(r,"/auth/me").concat(a),{headers:{Authorization:"Bearer ".concat(i)},signal:e.signal});if(clearTimeout(o),c.ok){let e=await c.json();return n.set("userInfo",JSON.stringify(e.user)),n.set("lastAuthCheck",Date.now().toString()),!0}if(401===c.status||403===c.status){n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck"),t&&(window.location.href="/login");break}throw Error("HTTP error! status: ".concat(c.status))}catch(o){if(console.warn("Auth check attempt ".concat(e+1," failed:"),o),e===a){if(c)return console.log("Using cached authentication due to network issues"),!0;return console.error("Authentication failed after all retries:",o),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck"),t&&setTimeout(()=>{window.location.href="/login"},2e3),!1}await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e)))}return!1}function s(){let e=n.get("userInfo");if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Error parsing user info:",e),null}}function i(){let e=n.get("authToken");e&&fetch("".concat(r,"/auth/logout"),{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}).catch(e=>{console.warn("Logout API call failed:",e)}),n.remove("authToken"),n.remove("userInfo"),n.remove("lastAuthCheck"),window.location.href="/login"}},5764:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,347,23)),Promise.resolve().then(o.bind(o,283))}},e=>{var t=t=>e(e.s=t);e.O(0,[690,441,684,358],()=>t(5764)),_N_E=e.O()}]);